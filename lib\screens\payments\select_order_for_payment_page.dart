import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/order_model.dart';
import '../../models/invoice_model.dart';
import '../../services/order_service.dart';
import '../../services/invoice_service.dart';
import 'payment_receive_page.dart';
import 'invoice_preview_page.dart';
import '../orders/order_details_screen.dart';

class SelectOrderForPaymentPage extends StatefulWidget {
  const SelectOrderForPaymentPage({super.key});

  @override
  State<SelectOrderForPaymentPage> createState() => _SelectOrderForPaymentPageState();
}

class _OrderWithInvoice {
  final Order order;
  final Invoice? invoice;
  const _OrderWithInvoice({required this.order, required this.invoice});
}

class _SelectOrderForPaymentPageState extends State<SelectOrderForPaymentPage> {
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final TextEditingController _searchController = TextEditingController();

  bool _isLoading = true;
  String? _error;
  String _currencySymbol = '';

  List<_OrderWithInvoice> _orders = [];
  List<_OrderWithInvoice> _filteredOrders = [];

  // Filters
  String _filter = 'all'; // all | unpaid | paid
  OrderStatus? _statusFilter; // optional status filter
  DateTimeRange? _dateRange; // optional date range

  // Sorting
  String _sort = 'newest'; // newest | oldest | amount_high | amount_low

  @override
  void initState() {
    super.initState();
    _loadAll();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAll() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load currency symbol (best-effort)
      try {
        _currencySymbol = await _orderService.getCurrencySymbol();
      } catch (_) {}

      final orders = await _orderService.getAllOrders();

      // Fetch invoices in parallel
      final List<_OrderWithInvoice> combined = await Future.wait(orders.map((order) async {
        try {
          final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
          return _OrderWithInvoice(order: order, invoice: invoice);
        } catch (_) {
          return _OrderWithInvoice(order: order, invoice: null);
        }
      }));

      if (!mounted) return;
      setState(() {
        _orders = combined;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _applyFilters() {
    final query = _searchController.text.trim().toLowerCase();

    List<_OrderWithInvoice> list = List.of(_orders);

    // Paid/unpaid filter
    if (_filter == 'unpaid') {
      list = list.where((e) => e.invoice?.hasOutstandingAmount == true).toList();
    } else if (_filter == 'paid') {
      list = list.where((e) => e.invoice?.isPaid == true).toList();
    }

    // Status filter
    if (_statusFilter != null) {
      list = list.where((e) => e.order.status == _statusFilter).toList();
    }

    // Date range filter
    if (_dateRange != null) {
      final start = DateTime(_dateRange!.start.year, _dateRange!.start.month, _dateRange!.start.day);
      final end = DateTime(_dateRange!.end.year, _dateRange!.end.month, _dateRange!.end.day, 23, 59, 59);
      list = list.where((e) => e.order.createdAt.isAfter(start.subtract(const Duration(milliseconds: 1))) && e.order.createdAt.isBefore(end.add(const Duration(milliseconds: 1)))).toList();
    }

    // Search filter
    if (query.isNotEmpty) {
      list = list.where((e) {
        final orderNum = e.order.orderNumber.toLowerCase();
        final invoiceNum = e.invoice?.invoiceNumber.toLowerCase() ?? '';
        return orderNum.contains(query) || invoiceNum.contains(query);
      }).toList();
    }

    // Sorting
    list.sort((a, b) {
      switch (_sort) {
        case 'oldest':
          return a.order.createdAt.compareTo(b.order.createdAt);
        case 'amount_high':
          final aAmt = a.invoice?.effectiveOutstandingAmount ?? a.order.totalAmount;
          final bAmt = b.invoice?.effectiveOutstandingAmount ?? b.order.totalAmount;
          return bAmt.compareTo(aAmt);
        case 'amount_low':
          final aAmt2 = a.invoice?.effectiveOutstandingAmount ?? a.order.totalAmount;
          final bAmt2 = b.invoice?.effectiveOutstandingAmount ?? b.order.totalAmount;
          return aAmt2.compareTo(bAmt2);
        case 'newest':
        default:
          return b.order.createdAt.compareTo(a.order.createdAt);
      }
    });

    _filteredOrders = list;
    setState(() {});
  }

  Future<void> _openPayment(_OrderWithInvoice data) async {
    final invoice = data.invoice;
    
    // Allow payment collection regardless of status or payment state
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentReceivePage(
          orderId: data.order.id,
          invoiceId: invoice?.id, // May be null - PaymentReceivePage will create if needed
          prefilledAmount: invoice?.effectiveOutstandingAmount ?? data.order.totalAmount,
          isPickupFlow: false,
        ),
      ),
    );

    if (!mounted) return;
    await _loadAll();
  }

  Future<void> _handleOrderTap(_OrderWithInvoice data) async {
    final invoice = data.invoice;

    if (invoice == null) {
      // No invoice exists - offer to create one for any order (regardless of status)
      await _showCreateInvoiceDialog(data);
    } else if (invoice.hasOutstandingAmount) {
      // Has outstanding amount - open payment
      await _openPayment(data);
    } else {
      // Fully paid - show options dialog with additional payment option
      await _showPaidOrderOptions(data);
    }
  }

  Future<void> _showPaidOrderOptions(_OrderWithInvoice data) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24),
            SizedBox(width: 8),
            Text('Order Paid'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order ${data.order.orderNumber} is fully paid.'),
            const SizedBox(height: 8),
            const Text('What would you like to do?', style: TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('cancel'),
            child: const Text('Cancel'),
          ),
          OutlinedButton.icon(
            onPressed: () => Navigator.of(context).pop('view_order'),
            icon: const Icon(Icons.receipt_long),
            label: const Text('View Order'),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop('print_invoice'),
            icon: const Icon(Icons.print),
            label: const Text('Print Invoice'),
          ),
        ],
      ),
    );

    if (result == 'view_order') {
      await _viewOrderDetails(data);
    } else if (result == 'print_invoice') {
      await _viewInvoice(data);
    }
  }

  Future<void> _viewOrderDetails(_OrderWithInvoice data) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderDetailsScreen(
          orderId: data.order.id,
        ),
      ),
    );
  }

  Future<void> _viewInvoice(_OrderWithInvoice data) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InvoicePreviewPage(
          orderId: data.order.id,
          invoiceId: data.invoice!.id,
        ),
      ),
    );
  }

  Future<void> _showCreateInvoiceDialog(_OrderWithInvoice data) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Invoice & Collect Payment'),
        content: Text(
          'Order ${data.order.orderNumber} doesn\'t have an invoice yet. Create an invoice and proceed with payment collection?\n\nOrder Status: ${data.order.status.displayName}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Create Invoice & Collect Payment'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _createInvoiceAndOpenPayment(data);
    }
  }

  Future<void> _createInvoiceAndOpenPayment(_OrderWithInvoice data) async {
    try {
      // Show loading
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Navigate to payment page - it will create the invoice
      await Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => PaymentReceivePage(
            orderId: data.order.id,
            invoiceId: null, // No invoice ID - will trigger creation
            isPickupFlow: false,
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create invoice: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  int get _countAll => _orders.length;
  int get _countUnpaid => _orders.where((e) => e.invoice?.hasOutstandingAmount == true).length;
  int get _countPaid => _orders.where((e) => e.invoice?.isPaid == true).length;

  Future<void> _pickDateRange() async {
    final now = DateTime.now();
    final initial = _dateRange ?? DateTimeRange(start: now.subtract(const Duration(days: 7)), end: now);
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      initialDateRange: initial,
    );
    if (picked != null) {
      setState(() => _dateRange = picked);
      _applyFilters();
    }
  }

  void _clearDateFilter() {
    setState(() => _dateRange = null);
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Receive Payment'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            tooltip: 'Refresh',
            onPressed: _isLoading ? null : _loadAll,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildError()
              : _buildContent(),
    );
  }

  Widget _buildError() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 12),
            Text(
              _error ?? 'Unknown error',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _loadAll,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Search
        Padding(
          padding: const EdgeInsets.fromLTRB(12, 12, 12, 6),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by Order/Invoice Number',
              prefixIcon: const Icon(Icons.search),
              filled: true,
              fillColor: Colors.grey[100],
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              suffixIcon: _searchController.text.isEmpty
                  ? null
                  : IconButton(
                      onPressed: () => _searchController.clear(),
                      icon: const Icon(Icons.clear),
                    ),
            ),
          ),
        ),

        // Filters Card
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Card(
            elevation: 1,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(12, 10, 12, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Primary filter chips with counts
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildCountChip('All', _countAll, _filter == 'all', () {
                          setState(() => _filter = 'all');
                          _applyFilters();
                        }),
                        const SizedBox(width: 8),
                        _buildCountChip('Unpaid', _countUnpaid, _filter == 'unpaid', () {
                          setState(() => _filter = 'unpaid');
                          _applyFilters();
                        }, color: Colors.orange),
                        const SizedBox(width: 8),
                        _buildCountChip('Paid', _countPaid, _filter == 'paid', () {
                          setState(() => _filter = 'paid');
                          _applyFilters();
                        }, color: Colors.green),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),
                  // Secondary filters row
                  Row(
                    children: [
                      // Status dropdown
                      Expanded(
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<OrderStatus?>(
                              isExpanded: true,
                              value: _statusFilter,
                              hint: const Text('All Statuses'),
                              items: [
                                const DropdownMenuItem<OrderStatus?>(value: null, child: Text('All Statuses')),
                                ...OrderStatus.values.map((s) => DropdownMenuItem<OrderStatus?>(
                                      value: s,
                                      child: Text(s.displayName),
                                    )),
                              ],
                              onChanged: (val) {
                                setState(() => _statusFilter = val);
                                _applyFilters();
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      // Date range button
                      OutlinedButton.icon(
                        onPressed: _pickDateRange,
                        icon: const Icon(Icons.date_range),
                        label: Text(_dateRange == null
                            ? 'Date Range'
                            : '${_dateRange!.start.year}/${_dateRange!.start.month}/${_dateRange!.start.day} - ${_dateRange!.end.year}/${_dateRange!.end.month}/${_dateRange!.end.day}'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                      if (_dateRange != null)
                        IconButton(
                          tooltip: 'Clear',
                          onPressed: _clearDateFilter,
                          icon: const Icon(Icons.clear),
                        ),
                    ],
                  ),

                  const SizedBox(height: 10),
                  // Sort chips (wrap to avoid overflow)
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 2),
                        child: Text(
                          'Sort',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                      ),
                      _buildSortChip('Newest', 'newest'),
                      _buildSortChip('Oldest', 'oldest'),
                      _buildSortChip('Amount High', 'amount_high'),
                      _buildSortChip('Amount Low', 'amount_low'),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _filter = 'all';
                            _statusFilter = null;
                            _dateRange = null;
                            _sort = 'newest';
                          });
                          _applyFilters();
                        },
                        icon: const Icon(Icons.filter_alt_off),
                        label: const Text('Clear Filters'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // Section title with result count
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
          child: Row(
            children: [
              Text(
                'Orders',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  _ordersCountLabel(_filteredOrders.length),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadAll,
            child: _filteredOrders.isEmpty
                ? ListView( // enables pull-to-refresh in empty state
                    children: const [
                      SizedBox(height: 180),
                      Center(child: Text('No orders found')),
                    ],
                  )
                : ListView.separated(
                    itemCount: _filteredOrders.length,
                    separatorBuilder: (_, __) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final item = _filteredOrders[index];
                      final order = item.order;
                      final inv = item.invoice;

                      final paymentStatusText = inv?.paymentStatus.displayName ?? 'No Invoice';
                      final isPaid = inv?.isPaid == true;
                      final hasOutstanding = inv?.hasOutstandingAmount == true;
                      final outstanding = inv?.effectiveOutstandingAmount ?? 0.0;

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: hasOutstanding
                              ? Colors.orange.withValues(alpha: 0.15)
                              : isPaid
                                  ? Colors.green.withValues(alpha: 0.15)
                                  : Colors.grey.withValues(alpha: 0.15),
                          child: Icon(
                            hasOutstanding
                                ? Icons.warning_amber_rounded
                                : isPaid
                                    ? Icons.check_circle
                                    : Icons.receipt_long,
                            color: hasOutstanding
                                ? Colors.orange
                                : isPaid
                                    ? Colors.green
                                    : Colors.grey[700],
                          ),
                        ),
                        title: Text(order.orderNumber),
                        subtitle: Text(
                          '${order.status.displayName} • $paymentStatusText${hasOutstanding ? ' • Outstanding: $_currencySymbol${outstanding.toStringAsFixed(2)}' : ''}',
                        ),
                        trailing: order.status != OrderStatus.cancelled && !isPaid
                            ? ElevatedButton(
                                onPressed: () => _openPayment(item),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: hasOutstanding ? Colors.orange : Colors.green,
                                  foregroundColor: Colors.white,
                                ),
                                child: Text(hasOutstanding ? 'Collect' : 'Payment'),
                              )
                            : const Icon(Icons.chevron_right),
                        onTap: () => _handleOrderTap(item),
                      );
                    },
                  ),
          ),
        ),
      ],
    );
  }

  // UI helpers
  Widget _buildCountChip(String label, int count, bool selected, VoidCallback onTap, {Color? color}) {
    final chipColor = color ?? Theme.of(context).colorScheme.primary;
    return ChoiceChip(
      selected: selected,
      onSelected: (_) => onTap(),
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label),
          const SizedBox(width: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: selected ? Colors.white.withValues(alpha: 0.9) : chipColor.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '$count',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: selected ? chipColor : chipColor,
              ),
            ),
          )
        ],
      ),
      selectedColor: chipColor.withValues(alpha: 0.18),
      labelPadding: const EdgeInsets.symmetric(horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    );
  }

  Widget _buildSortChip(String text, String value) {
    final isSelected = _sort == value;
    return FilterChip(
      label: Text(text),
      selected: isSelected,
      onSelected: (_) {
        setState(() => _sort = value);
        _applyFilters();
      },
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    );
  }

  String _ordersCountLabel(int n) {
    if (n == 0) return '0 orders';
    if (n == 1) return '1 order';
    return '$n orders';
  }
}
