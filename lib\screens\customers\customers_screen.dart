import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import '../../providers/staff_auth_provider.dart';
import 'add_customer_screen.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  bool _isLoading = true;
  String? _error;
  
  // Privacy controls - track which customer details are visible
  final Set<String> _visibleEmails = <String>{};
  final Set<String> _visiblePhones = <String>{};
  
  // Customer addresses cache
  final Map<String, List<CustomerAddress>> _customerAddresses = {};
  final Set<String> _loadingAddresses = <String>{};
  final Set<String> _expandedCustomers = <String>{};

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final customers = await CustomerService.getAllCustomers();
      
      setState(() {
        _customers = customers;
        _applySearch();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applySearch() {
    if (_searchQuery.isEmpty) {
      _filteredCustomers = _customers;
    } else {
      final query = _searchQuery.toLowerCase();
      _filteredCustomers = _customers.where((customer) {
        final nameMatch = customer.fullName.toLowerCase().contains(query);
        final emailMatch = customer.email?.toLowerCase().contains(query) ?? false;
        final phoneMatch = customer.phone?.toLowerCase().contains(query) ?? false;
        final countryMatch = customer.country?.toLowerCase().contains(query) ?? false;
        return nameMatch || emailMatch || phoneMatch || countryMatch;
      }).toList();
    }
  }

  Future<void> _loadCustomerAddresses(String customerId) async {
    if (_customerAddresses.containsKey(customerId) || _loadingAddresses.contains(customerId)) {
      return;
    }

    setState(() {
      _loadingAddresses.add(customerId);
    });

    try {
      final addresses = await CustomerService.getCustomerAddresses(customerId);
      setState(() {
        _customerAddresses[customerId] = addresses;
        _loadingAddresses.remove(customerId);
      });
    } catch (e) {
      setState(() {
        _loadingAddresses.remove(customerId);
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load addresses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleEmailVisibility(String customerId) {
    setState(() {
      if (_visibleEmails.contains(customerId)) {
        _visibleEmails.remove(customerId);
      } else {
        _visibleEmails.add(customerId);
      }
    });
  }

  void _togglePhoneVisibility(String customerId) {
    setState(() {
      if (_visiblePhones.contains(customerId)) {
        _visiblePhones.remove(customerId);
      } else {
        _visiblePhones.add(customerId);
      }
    });
  }

  void _toggleCustomerExpansion(String customerId) {
    setState(() {
      if (_expandedCustomers.contains(customerId)) {
        _expandedCustomers.remove(customerId);
      } else {
        _expandedCustomers.add(customerId);
        _loadCustomerAddresses(customerId);
      }
    });
  }

  void _editCustomer(Customer customer) async {
    final result = await Navigator.of(context).push<Customer>(
      MaterialPageRoute(
        builder: (context) => AddCustomerScreen(
          customer: customer, // Pass the customer for editing
        ),
      ),
    );

    if (result != null) {
      // Refresh the customer list to show updated data
      _loadCustomers();
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${result.fullName} updated successfully!'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  String _maskEmail(String email) {
    if (email.length <= 3) return '***';
    final parts = email.split('@');
    if (parts.length != 2) return '***';
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) {
      return '***@$domain';
    }
    
    final maskedUsername = '${username.substring(0, 2)}${'*' * (username.length - 2)}';
    return '$maskedUsername@$domain';
  }

  String _maskPhone(String phone) {
    if (phone.length <= 4) return '****';
    return '****${phone.substring(phone.length - 4)}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customers'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(56),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _applySearch();
                });
              },
              decoration: InputDecoration(
                hintText: 'Search by name, email, phone, or country',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                            _applySearch();
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadCustomers,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh customers',
          ),
        ],
      ),
      body: Consumer<StaffAuthProvider>(
        builder: (context, staffAuthProvider, child) {
          final isStaff = staffAuthProvider.userType == UserType.staff;
          
          if (!isStaff) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Access Denied',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text('This page is only available to staff members.'),
                ],
              ),
            );
          }

          return _buildCustomersList();
        },
      ),
      floatingActionButton: Consumer<StaffAuthProvider>(
        builder: (context, staffAuthProvider, child) {
          // Only show FAB for staff members
          if (staffAuthProvider.userType != UserType.staff) {
            return const SizedBox.shrink();
          }
          
          return FloatingActionButton(
            onPressed: () async {
              final result = await Navigator.of(context).push<Customer>(
                MaterialPageRoute(
                  builder: (context) => const AddCustomerScreen(),
                ),
              );
              
              // Refresh the customer list if a new customer was added
              if (result != null) {
                _loadCustomers();
              }
            },
            backgroundColor: Colors.indigo,
            foregroundColor: Colors.white,
            child: const Icon(Icons.person_add),
          );
        },
      ),
    );
  }

  Widget _buildCustomersList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading customers...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading customers',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadCustomers,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredCustomers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No customers found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty 
                  ? 'No customers match your search criteria'
                  : 'No customers registered yet',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCustomers,
      child: Column(
        children: [
          // Customer count
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Text(
              'Total: ${_filteredCustomers.length} customer${_filteredCustomers.length != 1 ? 's' : ''}',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(12),
              itemCount: _filteredCustomers.length,
              itemBuilder: (context, index) {
                final customer = _filteredCustomers[index];
                return _buildCustomerCard(customer);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCard(Customer customer) {
    final isExpanded = _expandedCustomers.contains(customer.id);
    final addresses = _customerAddresses[customer.id] ?? [];
    final isLoadingAddresses = _loadingAddresses.contains(customer.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Customer header - more compact
                Row(
                  children: [
                    CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.indigo,
                      child: Text(
                        customer.fullName.isNotEmpty 
                            ? customer.fullName.substring(0, 1).toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            customer.fullName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (customer.country != null || customer.email != null || customer.phone != null) ...[
                            const SizedBox(height: 2),
                            Wrap(
                              spacing: 4,
                              runSpacing: 2,
                              children: [
                                if (customer.country != null) ...[
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.flag, size: 12, color: Colors.grey[600]),
                                      const SizedBox(width: 2),
                                      Text(
                                        customer.country!,
                                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                                      ),
                                    ],
                                  ),
                                ],
                                if (customer.email != null) ...[
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.email, size: 12, color: Colors.grey[600]),
                                      const SizedBox(width: 2),
                                      GestureDetector(
                                        onTap: () => _toggleEmailVisibility(customer.id),
                                        child: ConstrainedBox(
                                          constraints: const BoxConstraints(maxWidth: 100),
                                          child: Text(
                                            _visibleEmails.contains(customer.id)
                                                ? customer.email!
                                                : _maskEmail(customer.email!),
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                              fontFamily: _visibleEmails.contains(customer.id) ? null : 'monospace',
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                                if (customer.phone != null) ...[
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.phone, size: 12, color: Colors.grey[600]),
                                      const SizedBox(width: 2),
                                      GestureDetector(
                                        onTap: () => _togglePhoneVisibility(customer.id),
                                        child: Text(
                                          _visiblePhones.contains(customer.id)
                                              ? customer.phone!
                                              : _maskPhone(customer.phone!),
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                            fontFamily: _visiblePhones.contains(customer.id) ? null : 'monospace',
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    // Joined date - compact
                    Text(
                      _formatDate(customer.createdAt),
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[500],
                      ),
                    ),
                    const SizedBox(width: 4),
                    IconButton(
                      onPressed: () => _editCustomer(customer),
                      icon: const Icon(
                        Icons.edit,
                        size: 18,
                        color: Colors.blue,
                      ),
                      visualDensity: VisualDensity.compact,
                      tooltip: 'Edit Customer',
                    ),
                    IconButton(
                      onPressed: () => _toggleCustomerExpansion(customer.id),
                      icon: Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        size: 20,
                      ),
                      visualDensity: VisualDensity.compact,
                    ),
                  ],
                ),

                // Notes - only show if expanded or very short
                if (customer.notes != null && customer.notes!.isNotEmpty) ...[
                  if (!isExpanded && customer.notes!.length > 50) ...[
                    const SizedBox(height: 6),
                    Text(
                      '${customer.notes!.substring(0, 50)}...',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ] else if (isExpanded || customer.notes!.length <= 50) ...[
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        customer.notes!,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),

          // Expandable addresses section
          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.location_on, size: 18),
                      const SizedBox(width: 8),
                      const Text(
                        'Addresses',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (isLoadingAddresses) ...[
                        const SizedBox(width: 8),
                        const SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  if (addresses.isEmpty && !isLoadingAddresses) 
                    Center(
                      child: Text(
                        'No addresses found',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    )
                  else
                    ...addresses.map((address) => _buildAddressCard(address)),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddressCard(CustomerAddress address) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: address.isDefault ? Colors.indigo[50] : Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: address.isDefault ? Colors.indigo[200]! : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                address.title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
              if (address.isDefault) ...[
                const SizedBox(width: 6),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                  decoration: BoxDecoration(
                    color: Colors.indigo,
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: const Text(
                    'DEFAULT',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 4),
          Text(
            address.fullAddress,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

