import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/service_model.dart';
import '../../models/store_model.dart';
import '../../services/store_service.dart';
import '../../services/service_service.dart';
import '../../services/order_service.dart';
import 'customer_order_details_screen.dart';

enum FulfillmentMode { inStorePickup, delivery }

class CustomerCreateOrderScreen extends StatefulWidget {
  const CustomerCreateOrderScreen({super.key});

  @override
  State<CustomerCreateOrderScreen> createState() =>
      _CustomerCreateOrderScreenState();
}

class _CustomerCreateOrderScreenState extends State<CustomerCreateOrderScreen> {
  // Fulfillment mode for this order
  FulfillmentMode _fulfillmentMode = FulfillmentMode.inStorePickup;
  final PageController _pageController = PageController();
  final _specialInstructionsController = TextEditingController();
  late final OrderService _orderService;

  int _currentStep = 0;

  // Order data - customer is automatically set to current user
  final List<Store> _selectedStores = [];
  final List<Service> _selectedServices = [];
  String? _pickupAddressId;
  String? _deliveryAddressId;
  DateTime? _pickupDate;
  String? _pickupTimeSlot;
  DateTime? _deliveryDate;
  String? _deliveryTimeSlot;

  // Data loading
  List<Store> _stores = [];
  List<Service> _services = [];
  bool _loadingStores = false;
  bool _loadingServices = false;

  // Current user
  User? get _currentUser => Supabase.instance.client.auth.currentUser;

  @override
  void initState() {
    super.initState();
    _orderService = OrderService(supabase: Supabase.instance.client);
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    await _loadStores();
    await _loadServices();
  }

  Future<void> _loadStores() async {
    if (!mounted) return;
    setState(() => _loadingStores = true);
    try {
      final stores = await StoreService.getAllStores();
      if (!mounted) return;
      setState(() {
        _stores = stores;
        _loadingStores = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingStores = false);
      _showErrorSnackBar('Failed to load stores: $e');
    }
  }

  Future<void> _loadServices() async {
    if (!mounted) return;
    setState(() => _loadingServices = true);
    try {
      final services = await ServiceService.getAllServices();
      if (!mounted) return;
      setState(() {
        _services = services;
        _loadingServices = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingServices = false);
      _showErrorSnackBar('Failed to load services: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_currentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Create Order')),
        body: const Center(child: Text('Please sign in to create an order')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Order'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.blue.shade50,
            child: Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: (_currentStep + 1) / 3,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.blue.shade600,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Step ${_currentStep + 1} of 3',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Main content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildServiceSelectionStep(),
                _buildStoreSelectionStep(),
                _buildOrderSummaryStep(),
              ],
            ),
          ),

          // Navigation buttons
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildServiceSelectionStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Services',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose the services you need for your order',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          if (_loadingServices)
            const Center(child: CircularProgressIndicator())
          else
            ..._services.map((service) => _buildServiceCard(service)),
        ],
      ),
    );
  }

  Widget _buildServiceCard(Service service) {
    final isSelected = _selectedServices.contains(service);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedServices.remove(service);
            } else {
              _selectedServices.add(service);
            }
          });
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? Colors.blue.shade50 : Colors.white,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.local_laundry_service, color: Colors.blue),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      service.description ?? 'No description available',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildServicePricing(service),
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: Colors.blue.shade600, size: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStoreSelectionStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Store',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose where you want to drop off your items',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          if (_loadingStores)
            const Center(child: CircularProgressIndicator())
          else
            ..._stores.map((store) => _buildStoreCard(store)),
        ],
      ),
    );
  }

  Widget _buildStoreCard(Store store) {
    final isSelected = _selectedStores.contains(store);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedStores.remove(store);
            } else {
              _selectedStores.clear();
              _selectedStores.add(store);
            }
          });
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? Colors.blue.shade50 : Colors.white,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.store, color: Colors.green),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      store.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      store.address.isNotEmpty
                          ? store.address
                          : 'No address available',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    if (store.phone != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        store.phone!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: Colors.blue.shade600, size: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderSummaryStep() {
    final serviceTotal = _selectedServices.fold(
      0.0,
      (sum, service) => sum + service.basePrice,
    );
    final deliveryFee = _fulfillmentMode == FulfillmentMode.delivery
        ? 5.0
        : 0.0;
    final estimatedTotal = serviceTotal + deliveryFee;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Order Summary',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Staff will catalog your items when they are picked up',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),

          // Services
          if (_selectedServices.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.local_laundry_service,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Selected Services',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ..._selectedServices.map(
                      (service) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    service.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  if (service.description != null)
                                    Text(
                                      service.description!,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: _buildServicePricingInline(service),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Selected Store
          if (_selectedStores.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.store, color: Colors.green.shade600),
                        const SizedBox(width: 8),
                        const Text(
                          'Selected Store',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _selectedStores.first.name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _selectedStores.first.address,
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                    if (_selectedStores.first.phone != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _selectedStores.first.phone!,
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Fulfillment mode
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.local_shipping, color: Colors.orange.shade600),
                      const SizedBox(width: 8),
                      const Text(
                        'Fulfillment Method',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<FulfillmentMode>(
                          title: const Text('In-Store Pickup'),
                          subtitle: const Text('Free'),
                          value: FulfillmentMode.inStorePickup,
                          groupValue: _fulfillmentMode,
                          onChanged: (value) {
                            setState(() {
                              _fulfillmentMode = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<FulfillmentMode>(
                          title: const Text('Delivery'),
                          subtitle: const Text('\$5.00'),
                          value: FulfillmentMode.delivery,
                          groupValue: _fulfillmentMode,
                          onChanged: (value) {
                            setState(() {
                              _fulfillmentMode = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Special instructions
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.note_add, color: Colors.purple.shade600),
                      const SizedBox(width: 8),
                      const Text(
                        'Special Instructions',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _specialInstructionsController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Any special requirements or preferences...',
                      helperText:
                          'Optional: Let us know if you have any specific requests',
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Cost information
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.indigo.shade50, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    const Text(
                      'Pricing Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Final pricing will be determined after staff catalog your items during pickup. The cost will be based on:',
                  style: TextStyle(color: Colors.grey.shade700),
                ),
                const SizedBox(height: 8),
                ...[
                  '• Weight of your items (if applicable)',
                  '• Number and type of garments',
                  '• Selected service pricing',
                  '• Any additional treatments needed',
                ].map(
                  (point) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      point,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Base Service Cost'),
                          Text('\$${serviceTotal.toStringAsFixed(2)}'),
                        ],
                      ),
                      if (deliveryFee > 0) ...[
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Delivery Fee'),
                            Text('\$${deliveryFee.toStringAsFixed(2)}'),
                          ],
                        ),
                      ],
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Estimated Minimum',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '\$${estimatedTotal.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Previous'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceedToNextStep() ? _nextStep : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(_currentStep == 2 ? 'Create Order' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0:
        return _selectedServices.isNotEmpty;
      case 1:
        return _selectedStores.isNotEmpty;
      case 2:
        return true; // Order summary step - always can proceed
      default:
        return false;
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _createOrder();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _createOrder() async {
    if (_currentUser == null) {
      _showErrorSnackBar('User not authenticated');
      return;
    }

    if (_selectedServices.isEmpty || _selectedStores.isEmpty) {
      _showErrorSnackBar('Please complete all required fields');
      return;
    }

    try {
      // Get the selected service and store
      final service = _selectedServices.first;
      final store = _selectedStores.first;

      // Create the order in the database (without items - staff will add them during pickup)
      final createdOrder = await _orderService.createOrder(
        userId: _currentUser!.id,
        serviceId: service.id,
        items: [], // Empty items list - staff will add items during pickup
        storeId: store.id,
        pickupAddressId: _pickupAddressId,
        deliveryAddressId: _deliveryAddressId,
        pickupDate: _pickupDate,
        pickupTimeSlot: _pickupTimeSlot,
        deliveryDate: _deliveryDate,
        deliveryTimeSlot: _deliveryTimeSlot,
        specialInstructions: _specialInstructionsController.text.isNotEmpty
            ? _specialInstructionsController.text
            : null,
      );

      if (!mounted) return;

      _showSuccessSnackBar('Order created successfully!');

      // Navigate to order details
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => CustomerOrderDetailsScreen(order: createdOrder),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('Failed to create order: $e');
    }
  }

  Widget _buildServicePricingInline(Service service) {
    String pricingText;
    switch (service.pricingType.toLowerCase()) {
      case 'per_kg':
        pricingText =
            'From \$${(service.pricePerKg ?? service.basePrice).toStringAsFixed(2)} per kg';
        break;
      case 'per_item':
        pricingText =
            'From \$${(service.pricePerItem ?? service.basePrice).toStringAsFixed(2)} per item';
        break;
      case 'fixed':
        pricingText = 'Fixed \$${service.basePrice.toStringAsFixed(2)}';
        break;
      default:
        pricingText = 'From \$${service.basePrice.toStringAsFixed(2)}';
        break;
    }

    return Text(
      pricingText,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: Colors.blue.shade700,
      ),
    );
  }

  Widget _buildServicePricing(Service service) {
    switch (service.pricingType.toLowerCase()) {
      case 'per_kg':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '\$${(service.pricePerKg ?? service.basePrice).toStringAsFixed(2)} per kg',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade600,
              ),
            ),
            if (service.basePrice != (service.pricePerKg ?? service.basePrice))
              Text(
                'Base: \$${service.basePrice.toStringAsFixed(2)}',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
          ],
        );
      case 'per_item':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '\$${(service.pricePerItem ?? service.basePrice).toStringAsFixed(2)} per item',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade600,
              ),
            ),
            if (service.basePrice !=
                (service.pricePerItem ?? service.basePrice))
              Text(
                'Base: \$${service.basePrice.toStringAsFixed(2)}',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
          ],
        );
      case 'fixed':
        return Text(
          'Fixed: \$${service.basePrice.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade600,
          ),
        );
      default:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'From \$${service.basePrice.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade600,
              ),
            ),
            Text(
              'Type: ${service.pricingType}',
              style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
            ),
          ],
        );
    }
  }
}
