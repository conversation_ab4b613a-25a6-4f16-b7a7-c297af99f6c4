import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import 'customer_order_details_screen.dart';
import 'customer_create_order_screen.dart';

class CustomerOrdersScreen extends StatefulWidget {
  const CustomerOrdersScreen({super.key});

  @override
  State<CustomerOrdersScreen> createState() => _CustomerOrdersScreenState();
}

class _CustomerOrdersScreenState extends State<CustomerOrdersScreen> {
  final OrderService _orderService = OrderService(
    supabase: Supabase.instance.client,
  );

  List<Order> _orders = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Filter options
  OrderStatus? _selectedStatus;
  String _searchQuery = '';

  // Sorting options
  String _sortBy = 'created_at'; // 'created_at', 'total_amount', 'status'
  bool _sortAscending = false; // Default to newest first

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        setState(() {
          _errorMessage = 'Please sign in to view your orders';
          _isLoading = false;
        });
        return;
      }

      // Load orders for the current customer only
      final orders = await _orderService.getUserOrders(currentUser.id);

      if (!mounted) return;

      // Sort orders based on current sort settings
      orders.sort((a, b) {
        int comparison;
        switch (_sortBy) {
          case 'total_amount':
            comparison = a.totalAmount.compareTo(b.totalAmount);
            break;
          case 'status':
            comparison = a.status.index.compareTo(b.status.index);
            break;
          case 'created_at':
          default:
            comparison = a.createdAt.compareTo(b.createdAt);
            break;
        }
        return _sortAscending ? comparison : -comparison;
      });

      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'Failed to load orders: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  List<Order> get _filteredOrders {
    return _orders.where((order) {
      // Filter by status
      if (_selectedStatus != null && order.status != _selectedStatus) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return order.orderNumber.toLowerCase().contains(query) ||
            (order.service?.name.toLowerCase().contains(query) ?? false) ||
            (order.store?.name.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return AlertDialog(
              title: const Text('Filter & Sort Orders'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status filter
                    const Text(
                      'Order Status',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<OrderStatus?>(
                      value: _selectedStatus,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'All Statuses',
                      ),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Statuses'),
                        ),
                        ...OrderStatus.values.map(
                          (status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.displayName),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setDialogState(() {
                          _selectedStatus = value;
                        });
                      },
                    ),

                    const SizedBox(height: 20),

                    // Sort options
                    const Text(
                      'Sort By',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _sortBy,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'created_at',
                          child: Text('Order Date'),
                        ),
                        DropdownMenuItem(
                          value: 'total_amount',
                          child: Text('Total Amount'),
                        ),
                        DropdownMenuItem(
                          value: 'status',
                          child: Text('Status'),
                        ),
                      ],
                      onChanged: (value) {
                        setDialogState(() {
                          _sortBy = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 12),

                    // Sort direction
                    Row(
                      children: [
                        Checkbox(
                          value: _sortAscending,
                          onChanged: (value) {
                            setDialogState(() {
                              _sortAscending = value!;
                            });
                          },
                        ),
                        const Text('Ascending order'),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedStatus = null;
                      _sortBy = 'created_at';
                      _sortAscending = false;
                    });
                    _loadOrders();
                  },
                  child: const Text('Clear All'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _loadOrders();
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Orders'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOrders,
            tooltip: 'Refresh Orders',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter & Sort',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search orders...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // Filter and sort chips
          if (_selectedStatus != null ||
              _sortBy != 'created_at' ||
              _sortAscending)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Wrap(
                spacing: 8,
                children: [
                  if (_selectedStatus != null)
                    Chip(
                      label: Text('Status: ${_selectedStatus!.displayName}'),
                      onDeleted: () {
                        setState(() {
                          _selectedStatus = null;
                        });
                      },
                      backgroundColor: Colors.blue.shade100,
                    ),
                  if (_sortBy != 'created_at')
                    Chip(
                      label: Text('Sort: ${_getSortDisplayName()}'),
                      onDeleted: () {
                        setState(() {
                          _sortBy = 'created_at';
                          _sortAscending = false;
                        });
                        _loadOrders();
                      },
                      backgroundColor: Colors.green.shade100,
                    ),
                  if (_sortAscending && _sortBy == 'created_at')
                    Chip(
                      label: const Text('Oldest First'),
                      onDeleted: () {
                        setState(() {
                          _sortAscending = false;
                        });
                        _loadOrders();
                      },
                      backgroundColor: Colors.orange.shade100,
                    ),
                ],
              ),
            ),

          // Orders list
          Expanded(child: _buildOrdersList()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CustomerCreateOrderScreen(),
            ),
          );
        },
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('New Order'),
      ),
    );
  }

  String _getSortDisplayName() {
    String name;
    switch (_sortBy) {
      case 'total_amount':
        name = 'Amount';
        break;
      case 'status':
        name = 'Status';
        break;
      case 'created_at':
      default:
        name = 'Date';
        break;
    }
    return '$name ${_sortAscending ? '(Asc)' : '(Desc)'}';
  }

  Widget _buildOrdersList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _errorMessage!.contains('sign in')
                  ? Icons.login
                  : Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!.contains('sign in')
                  ? 'Authentication Required'
                  : 'Error Loading Orders',
              style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            if (!_errorMessage!.contains('sign in'))
              ElevatedButton(
                onPressed: _loadOrders,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Try Again'),
              ),
          ],
        ),
      );
    }

    if (_filteredOrders.isEmpty) {
      if (_orders.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              const Text(
                'No orders yet',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first order to get started',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CustomerCreateOrderScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.add),
                label: const Text('Create Order'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.filter_list_off,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              const Text(
                'No orders match your filters',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your search or filter criteria',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                    _selectedStatus = null;
                  });
                },
                child: const Text('Clear Filters'),
              ),
            ],
          ),
        );
      }
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: CustomScrollView(
        slivers: [
          // Order Statistics Summary
          if (!_isLoading && _orders.isNotEmpty)
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade50, Colors.blue.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Order Summary',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildSummaryItem(
                            'Total Orders',
                            _orders.length.toString(),
                            Icons.shopping_bag,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            'Active Orders',
                            _orders.where((o) => o.isActive).length.toString(),
                            Icons.pending_actions,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            'Total Spent',
                            '\$${_orders.fold(0.0, (sum, order) => sum + order.totalAmount).toStringAsFixed(2)}',
                            Icons.attach_money,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

          // Orders List
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final order = _filteredOrders[index];
              return Padding(
                padding: EdgeInsets.fromLTRB(
                  16,
                  index == 0 ? ((_orders.isNotEmpty) ? 0 : 16) : 0,
                  16,
                  index == _filteredOrders.length - 1 ? 16 : 12,
                ),
                child: _buildOrderCard(order),
              );
            }, childCount: _filteredOrders.length),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.blue.shade600),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade800,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.blue.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOrderCard(Order order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CustomerOrderDetailsScreen(order: order),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with order number and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Order #${order.orderNumber}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(order.status),
                ],
              ),

              const SizedBox(height: 12),

              // Service and store info
              if (order.service != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.local_laundry_service,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        order.service!.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              if (order.store != null) ...[
                Row(
                  children: [
                    Icon(Icons.store, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        order.store!.name,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Order items summary
              if (order.items.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.inventory_2,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${order.items.length} item${order.items.length == 1 ? '' : 's'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Bottom row with date and total
              Row(
                children: [
                  Expanded(
                    child: Text(
                      _formatDate(order.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ),
                  Text(
                    '\$${order.totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color chipColor;
    Color textColor;

    switch (status) {
      case OrderStatus.pending:
        chipColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case OrderStatus.accepted:
        chipColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
      case OrderStatus.pickedUp:
        chipColor = Colors.purple.shade100;
        textColor = Colors.purple.shade800;
        break;
      case OrderStatus.inProcess:
        chipColor = Colors.indigo.shade100;
        textColor = Colors.indigo.shade800;
        break;
      case OrderStatus.readyForPickup:
        chipColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case OrderStatus.outForDelivery:
        chipColor = Colors.teal.shade100;
        textColor = Colors.teal.shade800;
        break;
      case OrderStatus.completed:
        chipColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case OrderStatus.cancelled:
        chipColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
