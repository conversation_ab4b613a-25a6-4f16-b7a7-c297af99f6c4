import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import 'register_screen.dart';
import 'forgot_password_screen.dart';
import 'otp_login_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _isStaffId = false;
  bool _isValidating = false;
  String _validationMessage = '';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late AnimationController _loadingController;
  late Animation<double> _loadingAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _loadingController, curve: Curves.easeInOut),
    );

    _animationController.forward();

    // Listen to identifier changes to detect staff ID
    _identifierController.addListener(_onIdentifierChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _loadingController.dispose();
    _identifierController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _onIdentifierChanged() {
    final value = _identifierController.text;
    final isStaffId = RegExp(r'^[0-9]{4,}$').hasMatch(value);
    if (isStaffId != _isStaffId) {
      setState(() {
        _isStaffId = isStaffId;
        _validationMessage = '';
      });
    }
  }

  Future<void> _validateCredentials() async {
    if (_identifierController.text.isEmpty) return;

    setState(() {
      _isValidating = true;
      _validationMessage = '';
    });

    _loadingController.repeat();

    try {
      if (_isStaffId) {
        // Validate staff ID exists using the new database function
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final staffId = int.tryParse(_identifierController.text);

        if (staffId != null) {
          final staffInfo = await authProvider.validateStaffId(staffId);

          if (staffInfo != null) {
            // Check if staff has an auth_user_id (can login)
            if (staffInfo['auth_user_id'] != null) {
              final fullName = staffInfo['full_name'] as String? ?? '';
              final truncatedName = fullName.length > 3 
                  ? '${fullName.substring(0, 3)}***' 
                  : fullName;
              
              setState(() {
                _validationMessage = '✓ Staff found: $truncatedName (Ready to login)';
              });
            } else {
              setState(() {
                _validationMessage = '⚠ Staff found but account not configured for login';
              });
            }
          } else {
            setState(() {
              _validationMessage = '✗ Staff ID not found or account inactive';
            });
          }
        } else {
          setState(() {
            _validationMessage = '✗ Invalid Staff ID format';
          });
        }
      } else {
        // Basic email format validation
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _validationMessage = '✓ Email format valid';
        });
      }
    } catch (e) {
      setState(() {
        _validationMessage = '✗ Validation failed: ${e.toString()}';
      });
    } finally {
      // Check if widget is still mounted before using controllers
      if (mounted) {
        _loadingController.stop();
        setState(() {
          _isValidating = false;
        });
      }
    }
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      // Show loading state
      setState(() {
        _isValidating = true;
        _validationMessage = _isStaffId ? 'Authenticating staff...' : 'Authenticating...';
      });

      _loadingController.repeat();

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.signIn(
        identifier: _identifierController.text.trim(),
        password: _passwordController.text,
      );

      // Check if widget is still mounted before using controllers
      if (mounted) {
        _loadingController.stop();
        setState(() {
          _isValidating = false;
          _validationMessage = '';
        });
      }

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(authProvider.errorMessage ?? 'Login failed'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      } else if (success && mounted) {
        // Show success message briefly
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle_outline, color: Colors.white),
                const SizedBox(width: 8),
                Text(_isStaffId ? 'Staff login successful!' : 'Login successful!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 40),
                  Image.asset(
                    'Assets/laundry_logo.png',
                    height: 80,
                    width: 80,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Welcome to LaundryHub',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                    // Input type indicator
                    if (_identifierController.text.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _isStaffId ? Colors.orange.shade100 : Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _isStaffId ? Colors.orange : Colors.blue,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _isStaffId ? Icons.badge : Icons.email,
                              size: 16,
                              color: _isStaffId ? Colors.orange : Colors.blue,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _isStaffId ? 'Staff Login' : 'Customer Login',
                              style: TextStyle(
                                fontSize: 12,
                                color: _isStaffId ? Colors.orange.shade700 : Colors.blue.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    TextFormField(
                      controller: _identifierController,
                      keyboardType: TextInputType.text,
                      decoration: InputDecoration(
                        labelText: 'Enter Your Email',
                        hintText: 'Enter your email',
                        prefixIcon: Icon(
                          _isStaffId ? Icons.badge : Icons.email,
                          color: _isStaffId ? Colors.orange : Colors.blue,
                        ),
                        suffixIcon: _isValidating
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: RotationTransition(
                                    turns: _loadingAnimation,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        _isStaffId ? Colors.orange : Colors.blue,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : _identifierController.text.isNotEmpty && _validationMessage.isNotEmpty
                                ? Icon(
                                    _validationMessage.startsWith('✓')
                                        ? Icons.check_circle
                                        : Icons.error,
                                    color: _validationMessage.startsWith('✓')
                                        ? Colors.green
                                        : Colors.red,
                                  )
                                : null,
                        border: const OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: _isStaffId ? Colors.orange : Colors.blue,
                            width: 2,
                          ),
                        ),
                      ),
                      onChanged: (value) {
                        // Debounce validation
                        if (value.length >= 4) {
                          Future.delayed(const Duration(milliseconds: 1000), () {
                            if (_identifierController.text == value && value.length >= 4) {
                              _validateCredentials();
                            }
                          });
                        }
                      },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email or staff ID';
                      }
                      // Check if it's a staff ID (numeric, 4+ digits) or email format
                      final isStaffId = RegExp(r'^[0-9]{4,}$').hasMatch(value);
                      final isEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);

                      if (!isStaffId && !isEmail) {
                        return 'Please enter a valid email or staff ID';
                      }
                      return null;
                    },
                  ),
                  // Validation message
                  if (_validationMessage.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, left: 12.0),
                      child: Row(
                        children: [
                          Icon(
                            _validationMessage.startsWith('✓')
                                ? Icons.check_circle_outline
                                : Icons.error_outline,
                            size: 16,
                            color: _validationMessage.startsWith('✓')
                                ? Colors.green
                                : Colors.red,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              _validationMessage,
                              style: TextStyle(
                                fontSize: 12,
                                color: _validationMessage.startsWith('✓')
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  

                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _passwordController,
                    obscureText: _obscurePassword,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      border: const OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your password';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: (authProvider.status == AuthStatus.loading || _isValidating)
                        ? null
                        : _handleLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isStaffId ? Colors.orange : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    child: (authProvider.status == AuthStatus.loading || _isValidating)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                _isValidating
                                    ? (_isStaffId ? 'Validating Staff ID...' : 'Validating...')
                                    : 'Logging in...',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            _isStaffId ? 'Login as Staff' : 'Login',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                  const SizedBox(height: 16),
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const OtpLoginScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.sms),
                    label: const Text('Login with OTP'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const ForgotPasswordScreen(),
                            ),
                          );
                        },
                        child: const Text('Forgot Password?'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const RegisterScreen(),
                            ),
                          );
                        },
                        child: const Text('Sign Up'),
                      ),
                    ],
                  ),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
