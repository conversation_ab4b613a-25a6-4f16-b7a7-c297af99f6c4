import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';
import '../../providers/auth_provider.dart';
import '../../utils/email_formatter.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String email;
  final OtpType otpType;
  final String title;
  final String subtitle;
  final int? staffId; // Add staff ID for staff OTP login tracking

  const OtpVerificationScreen({
    super.key,
    required this.email,
    required this.otpType,
    required this.title,
    required this.subtitle,
    this.staffId,
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  
  // Countdown timer variables
  int _countdownSeconds = 0;
  Timer? _countdownTimer;
  bool _canResendOtp = true;

  @override
  void initState() {
    super.initState();
    // Start countdown when screen loads
    _startCountdown();
  }

  @override
  void dispose() {
    _otpController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdownSeconds = 180; // 3 minutes
    _canResendOtp = false;
    _updateCountdown();
  }

  void _updateCountdown() {
    if (_countdownSeconds > 0) {
      _countdownTimer = Timer(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _countdownSeconds--;
          });
          _updateCountdown();
        }
      });
    } else {
      setState(() {
        _canResendOtp = true;
      });
    }
  }

  String _formatCountdown() {
    final minutes = _countdownSeconds ~/ 60;
    final seconds = _countdownSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Future<void> _handleVerifyOtp() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.verifyOtp(
        email: widget.email,
        token: _otpController.text.trim(),
        type: widget.otpType,
        staffId: widget.staffId,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Verification successful!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to main app or close all auth screens
        if (widget.otpType == OtpType.recovery) {
          // For password recovery, navigate to password reset screen
          Navigator.pushReplacementNamed(context, '/reset-password');
        } else {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage ?? 'Verification failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleResendOtp() async {
    if (!_canResendOtp) return; // Prevent resending if countdown is active
    
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.signInWithOtp(email: widget.email);

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('OTP resent to your email!'),
          backgroundColor: Colors.green,
        ),
      );
      // Restart countdown after successful resend
      _startCountdown();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage ?? 'Failed to resend OTP'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.staffId != null ? Colors.orange : Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Icon(
                    Icons.verified_user, 
                    size: 80, 
                    color: widget.staffId != null ? Colors.orange : Colors.blue,
                  ),
                  const SizedBox(height: 32),
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.subtitle,
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'OTP sent to: ${EmailFormatter.formatEmail(widget.email)}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  // Display truncated email for privacy
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.email_outlined,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          EmailFormatter.formatEmail(widget.email),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  TextFormField(
                    controller: _otpController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 24, letterSpacing: 8),
                    decoration: const InputDecoration(
                      labelText: 'Enter OTP',
                      hintText: '000000',
                      border: OutlineInputBorder(),
                      counterText: '',
                    ),
                    maxLength: 6,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the OTP';
                      }
                      if (value.length != 6) {
                        return 'OTP must be 6 digits';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: authProvider.status == AuthStatus.loading
                        ? null
                        : _handleVerifyOtp,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.staffId != null ? Colors.orange : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: authProvider.status == AuthStatus.loading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Verify OTP'),
                  ),
                  const SizedBox(height: 16),
                  
                  // Countdown progress indicator
                  if (!_canResendOtp)
                    Column(
                      children: [
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: _countdownSeconds / 180,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            widget.staffId != null ? Colors.orange : Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Next OTP request available in ${_formatCountdown()}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  const SizedBox(height: 8),
                  
                  // Resend OTP button with countdown
                  SizedBox(
                    width: double.infinity,
                    child: _canResendOtp
                        ? TextButton(
                            onPressed: authProvider.status == AuthStatus.loading
                                ? null
                                : _handleResendOtp,
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'Resend OTP',
                              style: TextStyle(
                                fontSize: 16,
                                color: widget.staffId != null ? Colors.orange : Colors.blue,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.timer,
                                  size: 20,
                                  color: Colors.grey.shade600,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Resend OTP in ${_formatCountdown()}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Back'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
