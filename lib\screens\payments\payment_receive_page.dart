import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/invoice_model.dart';
import '../../models/payment_model.dart';
import '../../models/order_model.dart';
import '../../services/invoice_service.dart';
import '../../services/order_service.dart';
import '../../services/currency_service.dart' as currency_service;
import 'invoice_preview_page.dart';
import '../orders/order_details_screen.dart';

class PaymentReceivePage extends StatefulWidget {
  final String orderId;
  final String? invoiceId;
  final double? prefilledAmount;
  final bool isPickupFlow;

  const PaymentReceivePage({
    super.key,
    required this.orderId,
    this.invoiceId,
    this.prefilledAmount,
    this.isPickupFlow = false,
  });

  @override
  State<PaymentReceivePage> createState() => _PaymentReceivePageState();
}

class _PaymentReceivePageState extends State<PaymentReceivePage> {
  final _formKey = GlobalKey<FormState>();
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final currency_service.AppCurrencyService _currencyService =
      currency_service.AppCurrencyService();

  // Form controllers
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _receiptController = TextEditingController();
  final TextEditingController _transactionController = TextEditingController();
  final TextEditingController _channelTargetController = TextEditingController();
  final TextEditingController _payerPhoneController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // State
  Invoice? _invoice;
  Order? _order;
  PaymentMethod _selectedMethod = PaymentMethod.cash;
  OrderStatus? _selectedOrderStatus;
  bool _updateOrderStatus = false;
  bool _isLoading = false;
  bool _isProcessing = false;
  String _currencySymbol = '';

  @override
  void initState() {
    super.initState();
    _loadCurrency();
    _loadData();
  }

  @override
  void dispose() {
    _amountController.removeListener(_suggestOrderStatus);
    _amountController.dispose();
    _receiptController.dispose();
    _transactionController.dispose();
    _channelTargetController.dispose();
    _payerPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrency() async {
    try {
      final symbol = await _currencyService.getCurrencySymbol();
      if (mounted) {
        setState(() => _currencySymbol = symbol);
      }
    } catch (_) {
      // ignore errors, fallback to empty symbol
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load order details first
      _order = await _orderService.getOrderById(widget.orderId);
      
      // Load or create invoice
      if (widget.invoiceId != null) {
        _invoice = await _invoiceService.getInvoiceWithPayments(widget.invoiceId!);
      } else {
        try {
          _invoice = await _invoiceService.getInvoiceByOrderId(widget.orderId);
        } catch (e) {
          // No invoice found - create one automatically for payment
          _invoice = await _invoiceService.getOrCreateInvoiceForOrder(widget.orderId);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Invoice created for this order'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }

      // Prefill amount with outstanding amount or provided amount
      final amount = widget.prefilledAmount ?? _invoice!.effectiveOutstandingAmount;
      _amountController.text = amount.toStringAsFixed(2);

      // Set suggested order status based on current status and payment completion
      _suggestOrderStatus();
      
      // Re-suggest when amount changes
      _amountController.addListener(_suggestOrderStatus);

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _suggestOrderStatus() {
    if (_order == null || _invoice == null) return;
    
    final outstandingAfterPayment = _invoice!.effectiveOutstandingAmount - (double.tryParse(_amountController.text) ?? 0);
    final willBeFullyPaid = outstandingAfterPayment <= 0.01; // Account for floating point precision
    
    // Suggest status based on current order status and payment completion
    switch (_order!.status) {
      case OrderStatus.pending:
      case OrderStatus.accepted:
        _selectedOrderStatus = OrderStatus.pickedUp;  // Order collection
        break;
      case OrderStatus.pickedUp:
        _selectedOrderStatus = OrderStatus.inProcess;
        break;
      case OrderStatus.inProcess:
        _selectedOrderStatus = OrderStatus.readyForPickup;  // Laundry done, ready for pickup
        break;
      case OrderStatus.readyForPickup:
        if (willBeFullyPaid) {
          _selectedOrderStatus = OrderStatus.completed;  // Complete after payment (customer pickup)
        } else {
          _selectedOrderStatus = null; // No suggestion
        }
        break;
      case OrderStatus.outForDelivery:
        if (willBeFullyPaid) {
          _selectedOrderStatus = OrderStatus.completed;  // Complete after payment and delivery
        } else {
          _selectedOrderStatus = null; // No suggestion
        }
        break;
      case OrderStatus.completed:
      case OrderStatus.cancelled:
        _selectedOrderStatus = null; // No suggestion for final states
        break;
    }
  }

  Future<void> _createInvoice() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final created = await _invoiceService.getOrCreateInvoiceForOrder(widget.orderId);
      setState(() {
        _invoice = created;
      });

      // Prefill amount
      final amount = widget.prefilledAmount ?? created.effectiveOutstandingAmount;
      _amountController.text = amount.toStringAsFixed(2);
      
      // Update suggested order status
      _suggestOrderStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _onMethodChanged(PaymentMethod? method) {
    if (method == null) return;

    setState(() {
      _selectedMethod = method;
      
      // Clear method-specific fields
      _receiptController.clear();
      _transactionController.clear();
      _channelTargetController.clear();
      _payerPhoneController.clear();

      // No auto-generation for any method; receipt is optional for all
    });
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please correct the highlighted fields.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      // Create payment
      await _invoiceService.createPayment(
        invoiceId: _invoice!.id,
        orderId: widget.orderId,
        amount: amount,
        method: _selectedMethod,
        receiptNumber: _receiptController.text.isNotEmpty ? _receiptController.text : null,
        transactionCode: _selectedMethod.isMpesa ? _transactionController.text : null,
        channelTarget: null,
        payerPhone: _payerPhoneController.text.isNotEmpty ? _payerPhoneController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      // Update order status if requested
      if (_updateOrderStatus && _selectedOrderStatus != null && _order != null) {
        final currentUser = Supabase.instance.client.auth.currentUser;
        if (currentUser != null) {
          try {
            await _orderService.updateOrderStatus(
              orderId: widget.orderId,
              newStatus: _selectedOrderStatus!,
              changedBy: currentUser.id,
              notes: 'Status updated during payment processing',
            );
          } catch (statusError) {
            // Don't fail the whole payment if status update fails
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Payment recorded but status update failed: $statusError'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          }
        }
      }

      if (mounted) {
        // Show success dialog with options
        await _showPaymentSuccessDialog(amount);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error processing payment: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Collect Payment'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_invoice == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Collect Payment'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.receipt_long, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('No Invoice Found', style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'This order does not have an invoice yet. You can create one now to proceed with payment collection.',
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _isProcessing ? null : _createInvoice,
                          icon: const Icon(Icons.add),
                          label: _isProcessing
                              ? const SizedBox(height: 18, width: 18, child: CircularProgressIndicator(strokeWidth: 2))
                              : const Text('Create Invoice'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Collect Payment'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Invoice summary
              _buildInvoiceSummary(),
              const SizedBox(height: 24),

              // Payment amount
              _buildAmountField(),
              const SizedBox(height: 24),

              // Payment method selection
              _buildPaymentMethodSelection(),
              const SizedBox(height: 24),

              // Method-specific fields
              _buildMethodSpecificFields(),
              const SizedBox(height: 24),

              // Notes field
              _buildNotesField(),
              const SizedBox(height: 24),

              // Order status update section
              if (_order != null) _buildOrderStatusUpdateSection(),
              const SizedBox(height: 32),

              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice: ${_invoice!.invoiceNumber}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total Amount:'),
                Text(
                  '$_currencySymbol${_invoice!.totalAmount.toStringAsFixed(2)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Paid Amount:'),
                Text(
                  '$_currencySymbol${(_invoice!.totalPaid ?? 0.0).toStringAsFixed(2)}',
                  style: const TextStyle(color: Colors.green),
                ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Outstanding:'),
                Text(
                  '$_currencySymbol${_invoice!.effectiveOutstandingAmount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _invoice!.isPaid ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _invoice!.displayStatus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isProcessing ? null : () {
                    // Pre-fill with outstanding amount for quick payment
                    _amountController.text = _invoice!.effectiveOutstandingAmount.toStringAsFixed(2);
                    // Scroll to payment form
                    Scrollable.ensureVisible(
                      context,
                      duration: const Duration(milliseconds: 300),
                    );
                  },
                  icon: const Icon(Icons.flash_on, size: 16),
                  label: const Text('Quick Pay'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(80, 32),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: InputDecoration(
        labelText: 'Payment Amount *',
        hintText: 'Enter amount to collect',
        prefixText: _currencySymbol.isNotEmpty ? '$_currencySymbol ' : null,
        border: const OutlineInputBorder(),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Amount is required';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'Please enter a valid amount';
        }
        // Allow overpayment (additional payments beyond outstanding amount)
        // Remove the restriction that prevented payments exceeding outstanding balance
        return null;
      },
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Method *',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        ...PaymentMethod.values.map((method) {
          return RadioListTile<PaymentMethod>(
            title: Text(method.displayName),
            value: method,
            groupValue: _selectedMethod,
            onChanged: _onMethodChanged,
            controlAffinity: ListTileControlAffinity.leading,
          );
        }),
      ],
    );
  }

  Widget _buildMethodSpecificFields() {
    // All methods: optional receipt field. M-Pesa methods additionally require transaction code.
    return Column(
      children: [
        _buildOptionalReceiptField(),
        if (_selectedMethod.isMpesa) ...[
          const SizedBox(height: 16),
          // Helpful notice for M-Pesa payments
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.06),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.info_outline, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'M-Pesa payments require the 10-character transaction code (A–Z and 0–9). Please enter it exactly as received.',
                    style: TextStyle(fontSize: 13),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          _buildMpesaFields(),
        ],
      ],
    );
  }

  Widget _buildOptionalReceiptField() {
    return TextFormField(
      controller: _receiptController,
      decoration: const InputDecoration(
        labelText: 'Receipt Number (Optional)',
        hintText: 'Enter receipt number (if any)',
        prefixIcon: Icon(Icons.receipt),
        border: OutlineInputBorder(),
      ),
    );
  }

  Widget _buildMpesaFields() {
    // Show only the transaction code field for all M-Pesa methods
    return Column(
      children: [
        TextFormField(
          controller: _transactionController,
          decoration: const InputDecoration(
            labelText: 'M-Pesa Transaction Code *',
            hintText: 'Enter 10-character transaction code',
            prefixIcon: Icon(Icons.qr_code),
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.characters,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
            LengthLimitingTextInputFormatter(10),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Transaction code is required for M-Pesa payments';
            }
            if (!_invoiceService.isValidMpesaTransactionCode(value)) {
              return 'Please enter a valid 10-character transaction code';
            }
            return null;
          },
        ),
      ],
    );
  }


  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'Notes (Optional)',
        hintText: 'Add any additional notes',
        prefixIcon: Icon(Icons.note),
        border: OutlineInputBorder(),
      ),
      maxLines: 3,
      maxLength: 500,
    );
  }

  Widget _buildOrderStatusUpdateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assignment_turned_in, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Order Status Update',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Current order status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Text(
                    'Current Status: ',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _order!.status.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Checkbox to enable status update
            CheckboxListTile(
              title: const Text('Update order status after payment'),
              subtitle: _selectedOrderStatus != null 
                  ? Text('Suggested: ${_selectedOrderStatus!.displayName}')
                  : const Text('Select a new status below'),
              value: _updateOrderStatus,
              onChanged: (value) {
                setState(() {
                  _updateOrderStatus = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
            
            // Status selection dropdown (only when checkbox is checked)
            if (_updateOrderStatus) ...[
              const SizedBox(height: 8),
              DropdownButtonFormField<OrderStatus>(
                decoration: const InputDecoration(
                  labelText: 'New Order Status',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                value: _selectedOrderStatus,
                items: _getAvailableOrderStatuses().map((status) {
                  return DropdownMenuItem<OrderStatus>(
                    value: status,
                    child: Text(status.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedOrderStatus = value;
                  });
                },
                validator: _updateOrderStatus 
                    ? (value) => value == null ? 'Please select a status' : null
                    : null,
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<OrderStatus> _getAvailableOrderStatuses() {
    if (_order == null) return [];
    
    // Return all statuses except the current one and cancelled
    return OrderStatus.values
        .where((status) => status != _order!.status && status != OrderStatus.cancelled)
        .toList();
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isProcessing ? null : () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isProcessing ? null : _processPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: _isProcessing
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Record Payment'),
          ),
        ),
      ],
    );
  }

  Future<void> _showPaymentSuccessDialog(double amount) async {
    final statusMessage = _updateOrderStatus && _selectedOrderStatus != null
        ? ' Order status updated to ${_selectedOrderStatus!.displayName}.'
        : '';

    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Color(0xFFF0FFF0)],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.payment,
                  color: Colors.green.shade600,
                  size: 48,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Success message
              Text(
                'Payment Recorded Successfully!',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 12),
              
              // Payment details
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Payment Amount:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        Text(
                          '$_currencySymbol${amount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Payment Method:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        Text(
                          _selectedMethod.displayName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    if (statusMessage.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.info, size: 16, color: Colors.blue.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                statusMessage.trim(),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Action buttons
              Column(
                children: [
                  // View Invoice button
                  if (_invoice != null)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => Navigator.of(ctx).pop('view_invoice'),
                        icon: const Icon(Icons.receipt_long, size: 20),
                        label: const Text('View Invoice'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                      ),
                    ),
                  
                  if (_invoice != null) const SizedBox(height: 12),
                  
                  // Open Order Details button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.of(ctx).pop('view_order'),
                      icon: const Icon(Icons.assignment, size: 20),
                      label: const Text('Open Order Details'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue.shade700,
                        side: BorderSide(color: Colors.blue.shade300, width: 2),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Close button
                  TextButton(
                    onPressed: () => Navigator.of(ctx).pop('close'),
                    child: Text(
                      'Close',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    // Handle user choice
    if (!mounted) return;
    
    if (result == 'view_invoice' && _invoice != null) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => InvoicePreviewPage(
            orderId: widget.orderId,
            invoiceId: _invoice!.id,
          ),
        ),
      );
    } else if (result == 'view_order') {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => OrderDetailsScreen(orderId: widget.orderId),
        ),
      );
    } else {
      // Close - return success data to previous page
      Navigator.of(context).pop({
        'success': true,
        'amount': amount,
        'method': _selectedMethod,
        'statusUpdated': _updateOrderStatus && _selectedOrderStatus != null,
        'newStatus': _selectedOrderStatus?.displayName,
      });
    }
  }
}
