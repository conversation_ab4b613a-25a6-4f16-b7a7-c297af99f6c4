import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../providers/staff_auth_provider.dart';
import '../../models/order_model.dart';
import '../../models/store_model.dart';
import '../../services/order_service.dart';
import '../../services/store_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  late final OrderService _orderService;

  // Filters
  String _selectedStoreId = 'all';
  String _selectedStaffId = 'all';
  DateTimeRange? _selectedDateRange;
  String _selectedStatus = 'all';

  // Data
  List<Order> _orders = [];
  List<Store> _stores = [];
  List<Map<String, String>> _staffList = [];

  // UI State
  bool _isLoading = false;
  String? _error;

  // Report Data
  final Map<String, int> _statusCounts = {};
  final Map<String, int> _storeCounts = {};
  final Map<String, int> _staffCounts = {};
  int _totalOrders = 0;

  final List<String> _statusOptions = [
    'all',
    'pending',
    'accepted',
    'picked_up',
    'in_process',
    'ready_for_pickup',
    'out_for_delivery',
    'completed',
    'cancelled',
  ];

  @override
  void initState() {
    super.initState();
    _orderService = OrderService(supabase: Supabase.instance.client);
    _initializeDateRange();
    _loadInitialData();
  }

  void _initializeDateRange() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    _selectedDateRange = DateTimeRange(start: startOfMonth, end: now);
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadStores(),
      _loadStaff(),
      _loadReportData(),
    ]);
  }

  Future<void> _loadStores() async {
    try {
      final stores = await StoreService.getAllStores();
      setState(() {
        _stores = stores;
      });
    } catch (e) {
      // Handle error silently - stores are optional
    }
  }

  Future<void> _loadStaff() async {
    try {
      // Get all orders to extract unique staff members
      final allOrders = await _orderService.getAllOrders();
      final staffSet = <String, String>{};

      for (final order in allOrders) {
        if (order.assignedAgentId != null) {
          // In a real app, you'd fetch user details from a users table
          // For now, we'll use the UUID as display name
          staffSet[order.assignedAgentId!] = 'Staff ${order.assignedAgentId!.substring(0, 8)}';
        }
      }

      setState(() {
        _staffList = staffSet.entries
            .map((e) => {'id': e.key, 'name': e.value})
            .toList();
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadReportData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Get all orders first
      List<Order> orders = await _orderService.getAllOrders();

      // Apply filters
      orders = _applyFilters(orders);

      // Calculate report data
      _calculateReportData(orders);

      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<Order> _applyFilters(List<Order> orders) {
    return orders.where((order) {
      // Date range filter
      if (_selectedDateRange != null) {
        final orderDate = order.createdAt;
        if (orderDate.isBefore(_selectedDateRange!.start) ||
            orderDate.isAfter(_selectedDateRange!.end.add(const Duration(days: 1)))) {
          return false;
        }
      }

      // Store filter
      if (_selectedStoreId != 'all' && order.storeId != _selectedStoreId) {
        return false;
      }

      // Staff filter
      if (_selectedStaffId != 'all' && order.assignedAgentId != _selectedStaffId) {
        return false;
      }

      // Status filter
      if (_selectedStatus != 'all' && order.status.value != _selectedStatus) {
        return false;
      }

      return true;
    }).toList();
  }

  void _calculateReportData(List<Order> orders) {
    _statusCounts.clear();
    _storeCounts.clear();
    _staffCounts.clear();
    _totalOrders = orders.length;

    for (final order in orders) {
      // Status counts
      final status = order.status.value;
      _statusCounts[status] = (_statusCounts[status] ?? 0) + 1;

      // Store counts
      if (order.store != null) {
        final storeName = order.store!.name;
        _storeCounts[storeName] = (_storeCounts[storeName] ?? 0) + 1;
      }

      // Staff counts
      if (order.assignedAgentId != null) {
        final staffId = order.assignedAgentId!;
        final staffName = _staffList
            .firstWhere((s) => s['id'] == staffId, orElse: () => {'name': 'Unknown'})['name']!;
        _staffCounts[staffName] = (_staffCounts[staffName] ?? 0) + 1;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StaffAuthProvider>(
      builder: (context, staffAuthProvider, child) {
        final isStaff = staffAuthProvider.userType == UserType.staff;

        if (!isStaff) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Reports'),
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Access Denied',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Reports are only available to staff members',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('Reports'),
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadReportData,
                tooltip: 'Refresh Reports',
              ),
            ],
          ),
          body: Column(
            children: [
              _buildFiltersSection(),
              Expanded(child: _buildReportContent()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFiltersSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filters',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Date Range Filter
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedDateRange != null
                          ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                          : 'Select Date Range',
                    ),
                    onPressed: _showDateRangePicker,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _selectedDateRange = null;
                    });
                    _loadReportData();
                  },
                  tooltip: 'Clear Date Filter',
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Store and Staff Filters Row
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Filter by Store',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: _selectedStoreId,
                    items: [
                      const DropdownMenuItem(value: 'all', child: Text('All Stores')),
                      ..._stores.map((store) => DropdownMenuItem(
                        value: store.id,
                        child: Text(store.name),
                      )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedStoreId = value!;
                      });
                      _loadReportData();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Filter by Staff',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: _selectedStaffId,
                    items: [
                      const DropdownMenuItem(value: 'all', child: Text('All Staff')),
                      ..._staffList.map((staff) => DropdownMenuItem(
                        value: staff['id'],
                        child: Text(staff['name']!),
                      )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedStaffId = value!;
                      });
                      _loadReportData();
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Status Filter
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Filter by Status',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              value: _selectedStatus,
              items: _statusOptions.map((status) => DropdownMenuItem(
                value: status,
                child: Text(_formatStatusText(status)),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value!;
                });
                _loadReportData();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text('Error loading reports', style: TextStyle(fontSize: 18, color: Colors.grey[800])),
            const SizedBox(height: 8),
            Text(_error!, style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadReportData,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(),
          const SizedBox(height: 20),
          _buildStatusChart(),
          const SizedBox(height: 20),
          _buildStoreChart(),
          const SizedBox(height: 20),
          _buildStaffChart(),
          const SizedBox(height: 20),
          _buildOrdersList(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: Card(
            color: Colors.blue[50],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(Icons.receipt_long, size: 32, color: Colors.blue[700]),
                  const SizedBox(height: 8),
                  Text(
                    '$_totalOrders',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                  const Text('Total Orders'),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Card(
            color: Colors.green[50],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(Icons.check_circle, size: 32, color: Colors.green[700]),
                  const SizedBox(height: 8),
                  Text(
                    '${_statusCounts['completed'] ?? 0}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                  const Text('Completed'),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Card(
            color: Colors.orange[50],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(Icons.pending, size: 32, color: Colors.orange[700]),
                  const SizedBox(height: 8),
                  Text(
                    '${_getActiveOrdersCount()}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[700],
                    ),
                  ),
                  const Text('Active Orders'),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChart() {
    if (_statusCounts.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Orders by Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._statusCounts.entries.map((entry) {
              final percentage = _totalOrders > 0 ? (entry.value / _totalOrders * 100) : 0;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(_formatStatusText(entry.key)),
                    ),
                    Expanded(
                      flex: 3,
                      child: LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation(_getStatusColor(entry.key)),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 60,
                      child: Text(
                        '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreChart() {
    if (_storeCounts.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Orders by Store',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._storeCounts.entries.map((entry) {
              final percentage = _totalOrders > 0 ? (entry.value / _totalOrders * 100) : 0;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(entry.key),
                    ),
                    Expanded(
                      flex: 3,
                      child: LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation(Colors.blue),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 60,
                      child: Text(
                        '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStaffChart() {
    if (_staffCounts.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Orders by Staff',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._staffCounts.entries.map((entry) {
              final percentage = _totalOrders > 0 ? (entry.value / _totalOrders * 100) : 0;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(entry.key),
                    ),
                    Expanded(
                      flex: 3,
                      child: LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation(Colors.purple),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 60,
                      child: Text(
                        '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersList() {
    if (_orders.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.inbox, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No orders found',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Try adjusting your filters',
                  style: TextStyle(color: Colors.grey[500]),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Details (${_orders.length} orders)',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _orders.length,
              itemBuilder: (context, index) {
                final order = _orders[index];
                return _buildOrderListItem(order);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderListItem(Order order) {
    final statusColor = _getStatusColor(order.status.value);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Text(
            order.statusIcon,
            style: const TextStyle(fontSize: 16),
          ),
        ),
        title: Text(order.orderNumber),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Service: ${order.service?.name ?? 'Unknown'}'),
            if (order.store != null) Text('Store: ${order.store!.name}'),
            if (order.customer != null) Text('Customer: ${order.customer!.fullName}'),
            Text('Created: ${_formatDate(order.createdAt)}'),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: statusColor),
          ),
          child: Text(
            order.status.displayName,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        isThreeLine: true,
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _loadReportData();
    }
  }

  int _getActiveOrdersCount() {
    int count = 0;
    for (final entry in _statusCounts.entries) {
      if (entry.key != 'completed' && entry.key != 'cancelled') {
        count += entry.value;
      }
    }
    return count;
  }

  String _formatStatusText(String status) {
    switch (status) {
      case 'all': return 'All Orders';
      case 'pending': return 'Pending';
      case 'accepted': return 'Accepted';
      case 'picked_up': return 'Picked Up';
      case 'in_process': return 'In Process';
      case 'ready_for_pickup': return 'Ready for Pickup';
      case 'out_for_delivery': return 'Out for Delivery';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return status.toUpperCase();
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending': return Colors.orange;
      case 'accepted': return Colors.blue;
      case 'picked_up': return Colors.purple;
      case 'in_process': return Colors.indigo;
      case 'ready_for_pickup': return Colors.teal;
      case 'out_for_delivery': return Colors.amber;
      case 'completed': return Colors.green;
      case 'cancelled': return Colors.red;
      default: return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
