import 'package:flutter/material.dart';

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            // Header Banner (modern)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.indigo.shade600, Colors.blue.shade400],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.indigo.withValues(alpha: 0.25),
                    blurRadius: 18,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(Icons.description, color: Colors.white, size: 36),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Terms of Service',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Terms and conditions for using our app',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(color: Colors.white.withValues(alpha: 0.9)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Terms Content
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader('Agreement Overview', Icons.handshake),
                    const SizedBox(height: 8),
                    Text('By using laundry Hub, you agree to these Terms of Service. Please read them carefully.', style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 16),
                    _buildSectionHeader('Use of Service', Icons.rule),
                    const SizedBox(height: 8),
                    _buildTermsItem('Provide accurate information and keep your account secure'),
                    _buildTermsItem('Comply with applicable laws and our policies'),
                    _buildTermsItem('Do not misuse, reverse engineer, or disrupt the service'),
                    const SizedBox(height: 16),
                    _buildSectionHeader('Payments & Subscriptions', Icons.payment),
                    const SizedBox(height: 8),
                    _buildTermsItem('Fees (if applicable) will be communicated before charge'),
                    _buildTermsItem('Invoices and receipts are provided within the app'),
                    _buildTermsItem('Refunds subject to applicable policies and laws'),
                    const SizedBox(height: 16),
                    _buildSectionHeader('Data & Privacy', Icons.privacy_tip),
                    const SizedBox(height: 8),
                    _buildTermsItem('We handle your data per our Privacy Policy'),
                    _buildTermsItem('You retain ownership of your business data'),
                    _buildTermsItem('We may anonymize data for analytics'),
                    const SizedBox(height: 16),
                    _buildSectionHeader('Liability & Warranty', Icons.verified_user),
                    const SizedBox(height: 8),
                    _buildTermsItem('Service is provided “as is” without warranties'),
                    _buildTermsItem('We are not liable for indirect or incidental damages'),
                    _buildTermsItem('We strive for high availability but do not guarantee uptime'),
                    const SizedBox(height: 16),
                    _buildSectionHeader('Support & Contact', Icons.support_agent),
                    const SizedBox(height: 8),
                    Text('Email: <EMAIL>\nPhone: +254723280154', style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.gavel,
                            color: Colors.purple,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Fair, transparent, and customer-friendly terms',
                              style: TextStyle(
                                color: Colors.purple,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.indigo.withValues(alpha: 0.15),
                Colors.indigo.withValues(alpha: 0.08),
              ],
            ),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: Colors.indigo,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: Colors.indigo,
              letterSpacing: 0.1,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTermsItem(String item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.indigo.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.indigo.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.gavel_outlined,
              color: Colors.indigo,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              item,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }
}
