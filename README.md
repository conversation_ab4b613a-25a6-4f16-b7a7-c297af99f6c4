# LaundryHub - Laundry Management System

<div align="center">
  <img src="Assets/laundry_logo.png" alt="LaundryHub Logo" width="200" height="200">
  
  [![GitHub Release](https://img.shields.io/github/v/release/Thiararapeter/laundryhub?style=for-the-badge)](https://github.com/Thiararapeter/laundryhub/releases)
  [![GitHub License](https://img.shields.io/github/license/Thiararapeter/laundryhub?style=for-the-badge)](https://github.com/Thiararapeter/laundryhub/blob/main/LICENSE)
</div>

LaundryHub is a comprehensive laundry management application built with Flutter, providing solutions for both customers and laundry service providers.

## Features

### Customer Features
- **Authentication & Security**
  - Email/Phone registration and login
  - OTP verification
  - Password reset functionality
- **Order Management**
  - Create new laundry orders with multiple garments
  - View order status (Pending, Processing, Ready, Delivered)
  - Track order history with detailed timelines
  - Estimate pricing before submission
- **Payment System**
  - Multiple payment methods integration
  - View and download invoices
  - Payment history tracking
- **Profile & Settings**
  - Manage personal information
  - Save multiple delivery addresses
  - Notification preferences
  - Dark/light theme selection
- **Support Center**
  - FAQ section with common questions
  - Direct customer support channel
  - View terms of service and privacy policy

### Staff Features
- **Staff Portal**
  - Dedicated staff login/registration
  - Role-based access control
  - PIN/Password authentication
- **Order Processing**
  - View and filter active orders
  - Update order status in real-time
  - Add special instructions/notes
  - Generate custom pricing for special requests
- **Customer Management**
  - View customer profiles and order history
  - Add/edit customer information
  - Customer communication tools
- **Payment Handling**
  - Process cash and digital payments
  - Generate and send invoices
  - Payment reconciliation
  - View payment reports
- **Reporting & Analytics**
  - Daily/weekly/monthly order reports
  - Revenue tracking
  - Popular services analysis
  - Customer retention metrics

### Admin Features
- Store management
- Service pricing configuration
- Staff management
- Comprehensive reporting

## Technologies Used
- Flutter (Cross-platform framework)
- Supabase (Backend service)
- SQL (Database)
- Dart (Programming language)

## Project Structure
```
lib/
├── main.dart            # Entry point
├── models/              # Data models
├── providers/           # State management
├── screens/             # UI screens
├── services/            # Business logic
└── widgets/             # Reusable components
```

## Getting Started

### Prerequisites
- Flutter SDK
- Dart SDK
- Android Studio/Xcode (for mobile development)
- Supabase account (for backend)

### Installation
1. Clone the repository
2. Install dependencies:
```shell
flutter pub get
```
3. Set up environment variables (copy .env.example to .env)
4. Run the app:
```shell
flutter run
```

## Documentation
- [Flutter Documentation](https://docs.flutter.dev/)
- [Supabase Documentation](https://supabase.com/docs)

## Repository
The project is hosted on GitHub:  
[![GitHub Repo](https://img.shields.io/badge/GitHub-Repository-blue?style=for-the-badge&logo=github)](https://github.com/Thiararapeter/laundryhub)

## Contributing
We welcome contributions! Here's how to get started:

1. **Fork** the repository
2. **Clone** your fork locally:
```shell
git clone https://github.com/Thiararapeter/laundryhub.git
```
3. Create a new branch for your changes
4. Make your improvements
5. Submit a pull request

For major changes, please open an issue first to discuss your proposed changes.

## Feedback & Issues
Found a bug or have a feature request?  
[Open an Issue](https://github.com/Thiararapeter/laundryhub/issues)

## License
[MIT](https://choosealicense.com/licenses/mit/)

---

**Connect with the project**:  
[![GitHub Stars](https://img.shields.io/github/stars/Thiararapeter/laundryhub?style=social)](https://github.com/Thiararapeter/laundryhub/stargazers)  
[![GitHub Forks](https://img.shields.io/github/forks/Thiararapeter/laundryhub?style=social)](https://github.com/Thiararapeter/laundryhub/network/members)
