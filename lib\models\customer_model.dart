class Customer {
  final String id;
  final String fullName;
  final String? email;
  final String? phone;
  final String? country;
  final String? notes;
  final bool isActive;
  final String? createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Enhanced profile fields
  final String? profilePicture;
  final String? dateOfBirth;
  final String? gender;
  final String? preferredLanguage;

  // Account preferences
  final String theme; // 'light', 'dark', 'system'
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final String preferredCurrency; // 'USD', 'EUR', 'GBP', etc.
  final String? timezone;

  // Laundry preferences
  final String? preferredDetergent;
  final String? preferredFabricSoftener;
  final bool ecoFriendly;
  final String? specialInstructions;

  Customer({
    required this.id,
    required this.fullName,
    this.email,
    this.phone,
    this.country,
    this.notes,
    this.isActive = true,
    this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.profilePicture,
    this.dateOfBirth,
    this.gender,
    this.preferredLanguage,
    this.theme = 'system',
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.pushNotifications = true,
    this.preferredCurrency = 'USD',
    this.timezone,
    this.preferredDetergent,
    this.preferredFabricSoftener,
    this.ecoFriendly = false,
    this.specialInstructions,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as String,
      fullName: json['full_name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      country: json['country'] as String?,
      notes: json['notes'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdBy: json['created_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      profilePicture: json['profile_picture'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      gender: json['gender'] as String?,
      preferredLanguage: json['preferred_language'] as String?,
      theme: json['theme'] as String? ?? 'system',
      emailNotifications: json['email_notifications'] as bool? ?? true,
      smsNotifications: json['sms_notifications'] as bool? ?? false,
      pushNotifications: json['push_notifications'] as bool? ?? true,
      preferredCurrency: json['preferred_currency'] as String? ?? 'USD',
      timezone: json['timezone'] as String?,
      preferredDetergent: json['preferred_detergent'] as String?,
      preferredFabricSoftener: json['preferred_fabric_softener'] as String?,
      ecoFriendly: json['eco_friendly'] as bool? ?? false,
      specialInstructions: json['special_instructions'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'country': country,
      'notes': notes,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'profile_picture': profilePicture,
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'preferred_language': preferredLanguage,
      'theme': theme,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'push_notifications': pushNotifications,
      'preferred_currency': preferredCurrency,
      'timezone': timezone,
      'preferred_detergent': preferredDetergent,
      'preferred_fabric_softener': preferredFabricSoftener,
      'eco_friendly': ecoFriendly,
      'special_instructions': specialInstructions,
    };
  }

  Customer copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phone,
    String? country,
    String? notes,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? profilePicture,
    String? dateOfBirth,
    String? gender,
    String? preferredLanguage,
    String? theme,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    String? preferredCurrency,
    String? timezone,
    String? preferredDetergent,
    String? preferredFabricSoftener,
    bool? ecoFriendly,
    String? specialInstructions,
  }) {
    return Customer(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      country: country ?? this.country,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      profilePicture: profilePicture ?? this.profilePicture,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      theme: theme ?? this.theme,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      timezone: timezone ?? this.timezone,
      preferredDetergent: preferredDetergent ?? this.preferredDetergent,
      preferredFabricSoftener:
          preferredFabricSoftener ?? this.preferredFabricSoftener,
      ecoFriendly: ecoFriendly ?? this.ecoFriendly,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }

  // Helper methods for preferences
  String get themeDisplayName {
    switch (theme) {
      case 'light':
        return 'Light Theme';
      case 'dark':
        return 'Dark Theme';
      case 'system':
        return 'System Default';
      default:
        return 'System Default';
    }
  }

  String get currencyDisplayName {
    switch (preferredCurrency) {
      case 'USD':
        return 'US Dollar (\$)';
      case 'EUR':
        return 'Euro (€)';
      case 'GBP':
        return 'British Pound (£)';
      case 'KES':
        return 'Kenyan Shilling (KSh)';
      default:
        return preferredCurrency;
    }
  }

  String get genderDisplayName {
    switch (gender) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'other':
        return 'Other';
      case 'prefer_not_to_say':
        return 'Prefer not to say';
      default:
        return 'Not specified';
    }
  }
}

class CustomerAddress {
  final String id;
  final String customerId;
  final String title;
  final String addressLine1;
  final String? addressLine2;
  final String city;
  final String? state;
  final String? postalCode;
  final String country;
  final double? latitude;
  final double? longitude;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  CustomerAddress({
    required this.id,
    required this.customerId,
    required this.title,
    required this.addressLine1,
    this.addressLine2,
    required this.city,
    this.state,
    this.postalCode,
    required this.country,
    this.latitude,
    this.longitude,
    this.isDefault = false,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomerAddress.fromJson(Map<String, dynamic> json) {
    return CustomerAddress(
      id: json['id'] as String,
      customerId: json['customer_id'] as String,
      title: json['title'] as String,
      addressLine1: json['address_line_1'] as String,
      addressLine2: json['address_line_2'] as String?,
      city: json['city'] as String,
      state: json['state'] as String?,
      postalCode: json['postal_code'] as String?,
      country: json['country'] as String,
      latitude: json['latitude'] != null
          ? (json['latitude'] as num).toDouble()
          : null,
      longitude: json['longitude'] != null
          ? (json['longitude'] as num).toDouble()
          : null,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'title': title,
      'address_line_1': addressLine1,
      'address_line_2': addressLine2,
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'is_default': isDefault,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get fullAddress {
    final parts = <String>[
      addressLine1,
      if (addressLine2 != null && addressLine2!.isNotEmpty) addressLine2!,
      city,
      if (state != null && state!.isNotEmpty) state!,
      if (postalCode != null && postalCode!.isNotEmpty) postalCode!,
      country,
    ];
    return parts.join(', ');
  }

  CustomerAddress copyWith({
    String? id,
    String? customerId,
    String? title,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    double? latitude,
    double? longitude,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomerAddress(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      title: title ?? this.title,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
