import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/email_formatter.dart';

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isStaffIdMode = false;
  bool _isValidating = false;
  bool _isPasswordResetMode = false;
  String _validationMessage = '';

  @override
  void initState() {
    super.initState();
    _identifierController.addListener(_onIdentifierChanged);
  }

  @override
  void dispose() {
    _identifierController.removeListener(_onIdentifierChanged);
    _identifierController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _onIdentifierChanged() {
    if (_isStaffIdMode && _identifierController.text.length >= 4) {
      // Debounce validation
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (_identifierController.text.length >= 4 && mounted) {
          _validateStaffId();
        }
      });
    } else {
      setState(() {
        _validationMessage = '';
      });
    }
  }

  Future<void> _validateStaffId() async {
    if (_identifierController.text.isEmpty || !_isStaffIdMode) return;

    setState(() {
      _isValidating = true;
      _validationMessage = '';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final staffId = int.tryParse(_identifierController.text);

      if (staffId != null) {
        final staffInfo = await authProvider.validateStaffId(staffId);

        if (staffInfo != null) {
          final fullName = staffInfo['full_name'] as String? ?? '';
          final truncatedName = fullName.length > 3 
              ? '${fullName.substring(0, 3)}***' 
              : fullName;
          
          setState(() {
            if (staffInfo['auth_user_id'] != null) {
              _validationMessage = '✓ Staff found: $truncatedName';
            } else {
              _validationMessage = '⚠ Staff found but account not configured for login';
            }
          });
        } else {
          setState(() {
            _validationMessage = '✗ Staff ID not found or account inactive';
          });
        }
      } else {
        setState(() {
          _validationMessage = '✗ Invalid Staff ID format';
        });
      }
    } catch (e) {
      setState(() {
        _validationMessage = '✗ Validation failed: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isValidating = false;
        });
      }
    }
  }

  Future<void> _handleResetPassword() async {
    if (_formKey.currentState!.validate()) {
      // Store context and services before async operations
      final currentContext = context;
      final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
      final navigator = Navigator.of(currentContext);
      final authProvider = Provider.of<AuthProvider>(currentContext, listen: false);
      
      final identifier = _identifierController.text.trim();
      
      if (_isStaffIdMode) {
        // Staff ID mode - send reset email to staff's registered email
        final staffId = int.tryParse(identifier);
        if (staffId != null) {
          // First validate the staff ID exists and is active
          final staffInfo = await authProvider.validateStaffId(staffId);
          
          if (staffInfo == null) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Invalid staff ID or account inactive. Please check and try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return;
          }
          
          // Check if staff has an email configured
          if (staffInfo['email'] == null) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Staff account not properly configured. Please contact administrator.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return;
          }
          
          final success = await authProvider.resetPasswordByStaffId(staffId: staffId);

          if (success && mounted) {
            _showSuccessDialog(
              scaffoldMessenger: scaffoldMessenger,
              navigator: navigator,
              message: 'Password reset email sent to your registered email address!',
              isStaff: true,
            );
          } else if (mounted) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(authProvider.errorMessage ?? 'Failed to send reset email. Please try again.'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // Email mode - send reset email to provided email address
        final success = await authProvider.resetPassword(email: identifier);

        if (success && mounted) {
                      _showSuccessDialog(
              scaffoldMessenger: scaffoldMessenger,
              navigator: navigator,
              message: 'Password reset email sent to ${EmailFormatter.formatEmail(identifier)}!',
              isStaff: false,
            );
        } else if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(authProvider.errorMessage ?? 'Failed to send reset email. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showSuccessDialog({
    required ScaffoldMessengerState scaffoldMessenger,
    required NavigatorState navigator,
    required String message,
    required bool isStaff,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.email_outlined,
                color: isStaff ? Colors.orange : Colors.blue,
              ),
              const SizedBox(width: 8),
              const Text('Email Sent'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.mark_email_read,
                size: 64,
                color: isStaff ? Colors.orange : Colors.blue,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              const Text(
                'Please check your email and click the reset link to set a new password.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(); // Close dialog
                // Instead of going back to login, switch to password reset mode
                setState(() {
                  _isPasswordResetMode = true;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isStaff ? Colors.orange : Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Set New Password'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(); // Close dialog
                navigator.pop(); // Go back to login
              },
              child: const Text('Back to Login'),
            ),
          ],
        );
      },
    );
  }

  void _toggleInputMode() {
    setState(() {
      _isStaffIdMode = !_isStaffIdMode;
      _identifierController.clear();
    });
  }

  String? _validatePassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    
    // Check for at least one uppercase letter, one lowercase letter, and one number
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }
    
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your password';
    }
    
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  Future<void> _handlePasswordReset() async {
    if (_formKey.currentState!.validate()) {
      final currentContext = context;
      final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
      final navigator = Navigator.of(currentContext);
      
      try {
        // Here you would typically call the API to reset the password
        // For now, we'll simulate a successful password reset
        await Future.delayed(const Duration(seconds: 2)); // Simulate API call
        
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Password reset successfully! You can now log in with your new password.'),
              backgroundColor: Colors.green,
            ),
          );
          
          // Clear the form and go back to login
          _passwordController.clear();
          _confirmPasswordController.clear();
          setState(() {
            _isPasswordResetMode = false;
          });
          
          navigator.pop();
        }
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Failed to reset password: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _resetToInitialState() {
    setState(() {
      _isPasswordResetMode = false;
      _passwordController.clear();
      _confirmPasswordController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isPasswordResetMode ? 'Set New Password' : 'Reset Password'),
        backgroundColor: _isPasswordResetMode 
            ? Colors.green 
            : (_isStaffIdMode ? Colors.orange : Colors.blue),
        foregroundColor: Colors.white,
        leading: _isPasswordResetMode 
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: _resetToInitialState,
              )
            : null,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (_isPasswordResetMode) {
            return _buildPasswordResetForm();
          }
          
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Icon(
                    _isStaffIdMode ? Icons.badge : Icons.email_outlined,
                    size: 80,
                    color: _isStaffIdMode ? Colors.orange : Colors.blue,
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'Reset Password',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Mode indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: _isStaffIdMode ? Colors.orange.shade100 : Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: _isStaffIdMode ? Colors.orange : Colors.blue,
                        width: 2,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isStaffIdMode ? Icons.badge : Icons.email,
                          color: _isStaffIdMode ? Colors.orange : Colors.blue,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isStaffIdMode ? 'Staff ID Mode' : 'Email Mode',
                          style: TextStyle(
                            color: _isStaffIdMode ? Colors.orange : Colors.blue,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Text(
                    _isStaffIdMode
                        ? 'Enter your staff ID below and we\'ll send a password reset link to your registered email address.'
                        : 'Enter your email address below and we\'ll send you a link to reset your password.',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  
                  // Input field that changes based on mode
                  TextFormField(
                    controller: _identifierController,
                    keyboardType: _isStaffIdMode ? TextInputType.number : TextInputType.emailAddress,
                    decoration: InputDecoration(
                      labelText: _isStaffIdMode ? 'Staff ID *' : 'Email Address *',
                      prefixIcon: Icon(_isStaffIdMode ? Icons.badge : Icons.email),
                      suffixIcon: _isValidating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: Padding(
                                padding: EdgeInsets.all(12.0),
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                            )
                          : _validationMessage.isNotEmpty
                              ? Icon(
                                  _validationMessage.startsWith('✓')
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: _validationMessage.startsWith('✓')
                                      ? Colors.green
                                      : Colors.red,
                                )
                              : null,
                      border: const OutlineInputBorder(),
                      hintText: _isStaffIdMode 
                          ? 'Enter your 4-digit staff ID'
                          : 'Enter your email address',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return _isStaffIdMode 
                            ? 'Please enter your staff ID'
                            : 'Please enter your email address';
                      }
                      
                      if (_isStaffIdMode) {
                        final staffId = int.tryParse(value.trim());
                        if (staffId == null || staffId < 1000 || staffId > 9999) {
                          return 'Please enter a valid 4-digit staff ID';
                        }
                      } else {
                        // Email validation
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
                          return 'Please enter a valid email address';
                        }
                      }
                      return null;
                    },
                  ),
                  
                  // Validation message
                  if (_validationMessage.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, left: 12.0),
                      child: Row(
                        children: [
                          Icon(
                            _validationMessage.startsWith('✓')
                                ? Icons.check_circle_outline
                                : Icons.error_outline,
                            size: 16,
                            color: _validationMessage.startsWith('✓')
                                ? Colors.green
                                : Colors.red,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              _validationMessage,
                              style: TextStyle(
                                fontSize: 12,
                                color: _validationMessage.startsWith('✓')
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  

                  const SizedBox(height: 24),
                  
                  // Toggle button for input mode - make it more prominent
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _isStaffIdMode ? Colors.orange.shade50 : Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _isStaffIdMode ? Colors.orange.shade200 : Colors.blue.shade200,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _isStaffIdMode ? Icons.email : Icons.badge,
                              color: _isStaffIdMode ? Colors.blue : Colors.orange,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _isStaffIdMode 
                                  ? 'Need to use email instead?'
                                  : 'Are you a staff member?',
                              style: TextStyle(
                                color: _isStaffIdMode ? Colors.blue : Colors.orange,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: _toggleInputMode,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            backgroundColor: _isStaffIdMode ? Colors.blue : Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            _isStaffIdMode ? 'Switch to Email' : 'Use Staff ID Instead',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  ElevatedButton(
                    onPressed: authProvider.status == AuthStatus.loading ? null : _handleResetPassword,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isStaffIdMode ? Colors.orange : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: authProvider.status == AuthStatus.loading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            _isStaffIdMode 
                                ? 'Send Reset Link to Staff Email'
                                : 'Send Reset Link to Email',
                            style: const TextStyle(fontSize: 16),
                          ),
                  ),
                  const SizedBox(height: 16),
                  
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Back to Login'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPasswordResetForm() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Icon(
              Icons.lock_reset,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 32),
            Text(
              'Set New Password',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.green.shade700),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Please enter your new password below. Make sure it\'s secure and easy to remember.',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            
            // New Password Field
            TextFormField(
              controller: _passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'New Password *',
                hintText: 'Enter your new password',
                prefixIcon: Icon(Icons.lock_outline),
                border: OutlineInputBorder(),
                helperText: 'Must be at least 8 characters with uppercase, lowercase, and number',
              ),
              validator: _validatePassword,
            ),
            const SizedBox(height: 16),
            
            // Confirm Password Field
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Confirm Password *',
                hintText: 'Confirm your new password',
                prefixIcon: Icon(Icons.lock_outline),
                border: OutlineInputBorder(),
              ),
              validator: _validateConfirmPassword,
            ),
            const SizedBox(height: 32),
            
            // Reset Password Button
            ElevatedButton(
              onPressed: _handlePasswordReset,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Reset Password',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            TextButton(
              onPressed: _resetToInitialState,
              child: const Text('Back to Reset Request'),
            ),
          ],
        ),
      ),
    );
  }
}
