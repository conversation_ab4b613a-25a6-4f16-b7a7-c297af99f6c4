import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/customer_model.dart';
import '../../models/service_model.dart';
import '../../models/garment_model.dart';
import '../../models/store_model.dart';
import '../../models/order_model.dart';
import '../../models/country_model.dart' as country_model;
import '../../services/customer_service.dart';
import '../../services/store_service.dart';
import '../../services/service_service.dart';
import '../../services/garment_service.dart';
import '../../services/order_service.dart';
import '../../widgets/custom_pricing_dialog.dart';
import '../../widgets/country_picker_dialog.dart';
import 'order_details_screen.dart';
import '../customers/add_customer_screen.dart';

enum FulfillmentMode { inStorePickup, delivery }

class CreateOrderScreen extends StatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  State<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen> {
  // Fulfillment mode for this order
  FulfillmentMode _fulfillmentMode = FulfillmentMode.inStorePickup;
  final PageController _pageController = PageController();
  final _customerSearchController = TextEditingController();
  final _specialInstructionsController = TextEditingController();
  late final OrderService _orderService;

  int _currentStep = 0;
  bool _isLoading = false;

  // Order data
  Customer? _selectedCustomer;
  final List<Store> _selectedStores = []; // Changed to support multiple stores
  final List<Service> _selectedServices = [];
  final List<OrderItem> _orderItems = [];
  CustomerAddress? _pickupAddress;
  CustomerAddress? _deliveryAddress;
  DateTime? _pickupDate;
  String? _pickupTimeSlot;
  DateTime? _deliveryDate;
  String? _deliveryTimeSlot;

  // Customer search
  List<Customer> _customers = [];
  List<Store> _stores = [];
  List<Service> _services = [];
  Map<String, List<Garment>> _garmentsByCategory = {};
  bool _loadingCustomers = false;
  bool _loadingStores = false;
  bool _loadingServices = false;
  bool _loadingGarments = false;

  // Custom pricing for services
  final Map<String, double> _customAmountsPerService = {};
  final Map<String, String> _customPricingModePerService = {};

  // Store weight input for per_kg services
  final Map<String, double> _serviceWeights = {};

  // Expansion state for garment categories
  final Set<String> _expandedCategories = {};

  @override
  void initState() {
    super.initState();
    _orderService = OrderService(supabase: Supabase.instance.client);
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _customerSearchController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    await _searchCustomers('');
    await _loadStores();
    await _loadServices();
    await _loadGarments();
  }

  Future<void> _searchCustomers(String query) async {
    if (!mounted) return;
    setState(() => _loadingCustomers = true);
    try {
      final customers = await CustomerService.searchCustomers(query);
      if (!mounted) return;
      setState(() {
        _customers = customers;
        _loadingCustomers = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingCustomers = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading customers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadStores() async {
    if (!mounted) return;
    setState(() => _loadingStores = true);
    try {
      final stores = await StoreService.getAllStores();
      if (!mounted) return;
      setState(() {
        _stores = stores;
        _loadingStores = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingStores = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading stores: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadServices() async {
    if (!mounted) return;
    setState(() => _loadingServices = true);
    try {
      final services = await ServiceService.getAllServices();
      if (!mounted) return;
      setState(() {
        _services = services;
        _loadingServices = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingServices = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading services: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadGarments() async {
    if (!mounted) return;
    setState(() => _loadingGarments = true);
    try {
      final garments = await GarmentService.getGarmentsByCategory();
      if (!mounted) return;
      setState(() {
        _garmentsByCategory = garments;
        _loadingGarments = false;

        // Expand all categories by default for better UX
        _expandedCategories.clear();
        _expandedCategories.addAll(garments.keys);
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _loadingGarments = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading garments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<Garment> _getFallbackGarment() async {
    try {
      // Try to get any existing garment from the database
      final response = await Supabase.instance.client
          .from('garments')
          .select()
          .eq('is_active', true)
          .limit(1)
          .maybeSingle();

      if (response != null) {
        return Garment.fromJson(response);
      }
    } catch (e) {
      debugPrint('Could not fetch garments from database: $e');
    }

    // If no garments exist, create a default one
    return Garment(
      id: 'default-garment-001',
      name: 'Mixed Items',
      category: 'clothing',
      description: 'General laundry items',
      garmentType: 'mixed_items',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  Future<void> _createOrder() async {
    // Validate required data before creating order
    if (_validateOrderData()) {
      setState(() => _isLoading = true);

      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                SizedBox(width: 16),
                Text('Creating order...'),
              ],
            ),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.blue,
          ),
        );
      }

      try {
        // For per_kg services without garments, create a generic order item
        List<OrderItem> finalOrderItems = List.from(_orderItems);

        // If no specific garments are selected, create generic items for services
        if (finalOrderItems.isEmpty) {
          // Get a fallback garment from the database
          final fallbackGarment = await _getFallbackGarment();

          for (final service in _selectedServices) {
            double quantity = 1.0;
            double unitPrice = 0.0;

            // Calculate pricing based on service type
            final customAmount = _customAmountsPerService[service.id];
            final customMode = _customPricingModePerService[service.id];
            final weight = _serviceWeights[service.id] ?? 1.0;

            if (customAmount != null && customAmount > 0) {
              if (customMode == 'per_kg') {
                quantity = weight;
                unitPrice = customAmount;
              } else {
                quantity = 1.0;
                unitPrice = customAmount;
              }
            } else {
              switch (service.pricingType) {
                case 'per_kg':
                  quantity = weight;
                  unitPrice = service.pricePerKg ?? 0.0;
                  break;
                case 'per_item':
                  quantity = 1.0;
                  unitPrice = service.pricePerItem ?? 0.0;
                  break;
                case 'fixed':
                  quantity = 1.0;
                  unitPrice = service.basePrice;
                  break;
                default:
                  quantity = 1.0;
                  unitPrice = 0.0;
              }
            }

            finalOrderItems.add(OrderItem(
              garmentId: fallbackGarment.id,
              garment: fallbackGarment,
              quantity: quantity.round().clamp(1, 999), // Ensure positive integer
              unitPrice: unitPrice,
              notes: '${service.name}${service.pricingType == 'per_kg' ? ' - Weight: ${weight}kg' : ''}',
            ));
          }
        }

        if (finalOrderItems.isEmpty) {
          throw Exception('No items in order');
        }

        debugPrint('Final order items count: ${finalOrderItems.length}');
        for (final item in finalOrderItems) {
          debugPrint('Item: ${item.garment.name}, ID: ${item.garmentId}, Qty: ${item.quantity}, Price: ${item.unitPrice}');
        }

        // Get current user ID
        final currentUser = Supabase.instance.client.auth.currentUser;
        if (currentUser == null) {
          throw Exception('User not authenticated');
        }

        // For now, always use the current authenticated user's ID to satisfy RLS policies
        // In a production app, you'd need to modify RLS policies or use stored procedures
        // to allow staff to create orders for customers
        final userId = currentUser.id;

        // Get custom pricing information if available
        if (_selectedServices.isEmpty) {
          throw Exception('No service selected');
        }

        final serviceId = _selectedServices.first.id;
        if (serviceId.isEmpty) {
          throw Exception('Invalid service ID');
        }

        final customAmount = _customAmountsPerService[serviceId];
        final customPricingMode = _customPricingModePerService[serviceId];

        debugPrint('Creating order with service ID: $serviceId');
        debugPrint('Fulfillment mode: $_fulfillmentMode');
        debugPrint('Selected delivery address: ${_deliveryAddress?.id}');
        debugPrint('Selected pickup address: ${_pickupAddress?.id}');

        // Determine pickup/delivery IDs based on fulfillment mode
        String? pickupId;
        String? deliveryId;
        
        if (_fulfillmentMode == FulfillmentMode.inStorePickup) {
          // For in-store pickup, no addresses are needed
          pickupId = null;
          deliveryId = null;
        } else if (_fulfillmentMode == FulfillmentMode.delivery) {
          // For delivery, use the selected delivery address
          pickupId = null;  // No pickup address needed for delivery
          deliveryId = _deliveryAddress?.id;
          
          if (deliveryId == null) {
            throw Exception('No delivery address selected for delivery mode');
          }
        }
        
        debugPrint('Final pickupId: $pickupId');
        debugPrint('Final deliveryId: $deliveryId');

        // Create the order
        final order = await _orderService.createOrder(
          userId: userId,
          customerId: _selectedCustomer?.id,
          storeId: _selectedStores.isNotEmpty ? _selectedStores.first.id : null,
          serviceId: serviceId,
          items: finalOrderItems,
          pickupAddressId: pickupId,
          deliveryAddressId: deliveryId,
          pickupDate: _pickupDate,
          pickupTimeSlot: _pickupTimeSlot,
          deliveryDate: _deliveryDate,
          deliveryTimeSlot: _deliveryTimeSlot,
          specialInstructions: _specialInstructionsController.text.isNotEmpty
              ? _specialInstructionsController.text
              : null,
          customAmount: customAmount,
          customPricingMode: customPricingMode,
        );

        if (mounted) {
          setState(() => _isLoading = false);

          // Show success dialog with options
          _showOrderCreatedDialog(order);
        }
      } catch (e) {
        debugPrint('Order creation error: $e');
        if (mounted) {
          String errorMessage = 'Error creating order: $e';

          // Provide more user-friendly error messages
          if (e.toString().contains('foreign key')) {
            errorMessage = 'Invalid data: Please check that all selected items are valid.';
          } else if (e.toString().contains('garments')) {
            errorMessage = 'Garment data issue: Please try selecting different items.';
          } else if (e.toString().contains('Permission denied')) {
            errorMessage = 'Permission denied: Please check your account permissions.';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(errorMessage),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  void _showOrderCreatedDialog(Order order) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Order Created Successfully!',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Text(
              'Order Number: ${order.orderNumber}',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text('Total: \$${order.totalAmount.toStringAsFixed(2)}'),
            Text('Status: ${order.status.displayName}'),
            if (order.pickupDate != null)
              Text('Pickup: ${_formatDate(order.pickupDate!)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close create order screen
            },
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close create order screen
              // Navigate to order details
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => OrderDetailsScreen(orderId: order.id),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('View Order'),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Create Order',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 4,
        shadowColor: Colors.blue.withValues(alpha: 0.3),
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildStoreSelection(),
                _buildCustomerSelection(),
                _buildServiceSelection(),
                _buildGarmentSelection(),
                _buildAddressSelection(),
                _buildTimeSlotSelection(),
                _buildOrderSummary(),
              ],
            ),
          ),
          _buildNavigationBar(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final steps = [
      'Store',
      'Customer',
      'Services',
      'Items',
      'Address',
      'Schedule',
      'Summary',
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: List.generate(steps.length, (index) {
          final isActive = index == _currentStep;
          final isCompleted = index < _currentStep;

          return Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 4,
                    decoration: BoxDecoration(
                      color: isCompleted || isActive
                          ? Colors.orange
                          : Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.green
                        : isActive
                        ? Colors.orange
                        : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: isCompleted
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: isActive ? Colors.white : Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildCustomerSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Customer',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Search for an existing customer or create a new one',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),

          // Search bar
          TextField(
            controller: _customerSearchController,
            decoration: InputDecoration(
              hintText: 'Search customers by name, email, or phone...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _customerSearchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _customerSearchController.clear();
                        setState(() {});
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {});
              _searchCustomers(value);
            },
          ),

          const SizedBox(height: 16),

          // Selected customer display
          if (_selectedCustomer != null)
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.orange,
                      child: Text(
                        _selectedCustomer!.fullName[0].toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedCustomer!.fullName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (_selectedCustomer!.email != null)
                            Text(_selectedCustomer!.email!),
                          if (_selectedCustomer!.phone != null)
                            Text(_selectedCustomer!.phone!),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        setState(() {
                          _selectedCustomer = null;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Customer list or create new
          Expanded(
            child: _selectedCustomer == null
                ? _buildCustomerList()
                : const Center(
                    child: Text(
                      'Customer selected! Click Next to continue.',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerList() {
    if (_loadingCustomers) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.orange),
      );
    }

    return Column(
      children: [
        // Create new customer button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _navigateToAddCustomer,
            icon: const Icon(Icons.person_add),
            label: const Text('Create New Customer'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: Colors.orange),
              foregroundColor: Colors.orange,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Customer list
        Expanded(
          child: _customers.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.person_search,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No customers found',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Try adjusting your search or create a new customer',
                        style: TextStyle(color: Colors.grey[500]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _customers.length,
                  itemBuilder: (context, index) {
                    final customer = _customers[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue,
                          child: Text(
                            customer.fullName[0].toUpperCase(),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(customer.fullName),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (customer.email != null) Text(customer.email!),
                            if (customer.phone != null) Text(customer.phone!),
                          ],
                        ),
                        onTap: () {
                          setState(() {
                            _selectedCustomer = customer;
                          });
                        },
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Future<void> _navigateToAddCustomer() async {
    final result = await Navigator.of(context).push<Customer>(
      MaterialPageRoute(
        builder: (context) => const AddCustomerScreen(),
      ),
    );

    // If a customer was created, add it to the list and select it
    if (result != null) {
                    setState(() {
        _selectedCustomer = result;
        // Add to the beginning of the list if not already present
        if (!_customers.any((customer) => customer.id == result.id)) {
          _customers.insert(0, result);
        }
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Customer "${result.fullName}" selected for order'),
                ),
              ],
                        ),
                        backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
                      ),
                    );
                  }
                }
  }

  Widget _buildStoreSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Stores',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose one or more stores for this order',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),

          // Selected stores display
          if (_selectedStores.isNotEmpty) ...[
            const Text(
              'Selected Stores:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedStores.map((store) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              store.displayName,
                              style: TextStyle(
                                color: Colors.orange[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              store.address,
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.orange[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Delete button
                      IconButton(
                        icon: const Icon(Icons.close, size: 16),
                        onPressed: () {
                          setState(() {
                            _selectedStores.removeWhere((s) => s.id == store.id);
                          });
                        },
                        color: Colors.orange[700],
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: const EdgeInsets.all(4),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
          ],

          // Store list
          Expanded(child: _buildStoreList()),
        ],
      ),
    );
  }

  Widget _buildStoreList() {
    if (_loadingStores) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.orange),
      );
    }

    return _stores.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.store, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No stores available',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please contact your administrator',
                  style: TextStyle(color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        : ListView.builder(
            itemCount: _stores.length,
            itemBuilder: (context, index) {
              final store = _stores[index];
              final isSelected = _selectedStores.any((s) => s.id == store.id);
              
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: isSelected ? Colors.orange : Colors.grey[300],
                    child: Icon(
                      Icons.store, 
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),
                  title: Text(
                    store.displayName,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(store.address),
                      if (store.phone != null) Text(store.phone!),
                    ],
                  ),
                  trailing: isSelected
                      ? const Icon(Icons.check_circle, color: Colors.orange)
                      : const Icon(Icons.add_circle_outline, color: Colors.grey),
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedStores.removeWhere((s) => s.id == store.id);
                      } else {
                        _selectedStores.add(store);
                      }
                    });
                  },
                ),
              );
            },
          );
  }

  Widget _buildServiceSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Services',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the laundry services you need',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),

          // Selected services display
          if (_selectedServices.isNotEmpty) ...[
            const Text(
              'Selected Services:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedServices.map((service) {
                final weight = _serviceWeights[service.id];
                final customMode = _customPricingModePerService[service.id];
                final needsWeight = service.pricingType == 'per_kg' || customMode == 'per_kg';
                final total = _calculateServiceTotal(service);

                return Container(
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 12, top: 8, bottom: 8),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              service.name,
                              style: const TextStyle(color: Colors.orange),
                            ),
                            if (needsWeight && weight != null) ...[
                              const SizedBox(height: 2),
                              Text(
                                '${weight.toStringAsFixed(1)}kg - \$${total.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.orange[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ] else if (!needsWeight && total > 0) ...[
                              const SizedBox(height: 2),
                              Text(
                                '\$${total.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.orange[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      // Edit button
                      IconButton(
                        icon: const Icon(Icons.edit, size: 16),
                        onPressed: () => _editService(service),
                        color: Colors.orange[700],
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: const EdgeInsets.all(4),
                      ),
                      // Delete button
                      IconButton(
                        icon: const Icon(Icons.close, size: 16),
                        onPressed: () {
                          setState(() {
                            _selectedServices.removeWhere((s) => s.id == service.id);
                            _serviceWeights.remove(service.id);
                            _customAmountsPerService.remove(service.id);
                            _customPricingModePerService.remove(service.id);
                          });
                        },
                        color: Colors.orange[700],
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: const EdgeInsets.all(4),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
          ],

          // Services list
          Expanded(child: _buildServicesList()),
        ],
      ),
    );
  }

  Widget _buildServicesList() {
    if (_loadingServices) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.orange),
      );
    }

    if (_services.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_laundry_service,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No services available',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Please contact your administrator',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _services.length,
      itemBuilder: (context, index) {
        final service = _services[index];
        final isSelected = _selectedServices.any((s) => s.id == service.id);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Column(
            children: [
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: isSelected ? Colors.orange : Colors.grey[300],
                  child: Icon(
                    _getServiceIcon(service.icon),
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                ),
                title: Text(
                  service.name,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(service.description ?? 'No description'),
                    const SizedBox(height: 4),
                    Text(
                      _getServicePriceText(service),
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                trailing: isSelected
                    ? const Icon(Icons.check_circle, color: Colors.orange)
                    : const Icon(Icons.add_circle_outline, color: Colors.grey),
                onTap: () async {
                  if (isSelected) {
                    setState(() {
                      _selectedServices.removeWhere((s) => s.id == service.id);
                      _serviceWeights.remove(service.id);
                    });
                  } else {
                    // Check if this is a per_kg service or has custom per_kg pricing
                    final customMode = _customPricingModePerService[service.id];
                    final needsWeight = service.pricingType == 'per_kg' || customMode == 'per_kg';

                    if (needsWeight) {
                      // Show weight input dialog
                      final weight = await _showWeightInputDialog(service);
                      if (weight != null && weight > 0) {
                        setState(() {
                          _serviceWeights[service.id] = weight;
                          _selectedServices.add(service);
                        });
                      }
                    } else {
                      // For fixed price services, add directly
                      setState(() {
                        _selectedServices.add(service);
                      });
                    }
                  }
                },
              ),

              // Custom pricing and weight display section (only show if service is selected)
              if (isSelected) ...[
                const Divider(height: 1),

                // Weight display for per_kg services
                if ((service.pricingType == 'per_kg' || _customPricingModePerService[service.id] == 'per_kg') && _serviceWeights[service.id] != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    color: Colors.green[50],
                    child: Row(
                      children: [
                        Icon(Icons.scale, size: 18, color: Colors.green[700]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Weight: ${_serviceWeights[service.id]!.toStringAsFixed(1)} kg',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.green[700],
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            'Total: \$${_calculateServiceTotal(service).toStringAsFixed(2)}',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(Icons.edit, size: 16, color: Colors.blue[600]),
                          onPressed: () async {
                            final newWeight = await _showWeightInputDialog(service);
                            if (newWeight != null && newWeight > 0) {
                              setState(() {
                                _serviceWeights[service.id] = newWeight;
                              });
                            }
                          },
                          tooltip: 'Edit weight',
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                          padding: const EdgeInsets.all(4),
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 1),
                ],

                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  color: _hasCustomPricing(service.id)
                      ? Colors.orange[50]
                      : Colors.grey[50],
                  child: Row(
                    children: [
                      Icon(
                        _hasCustomPricing(service.id) ? Icons.tune : Icons.tune,
                        size: 18,
                        color: _hasCustomPricing(service.id)
                            ? Colors.orange[700]
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _hasCustomPricing(service.id)
                              ? 'Custom pricing applied'
                              : 'Set custom pricing',
                          style: TextStyle(
                            color: _hasCustomPricing(service.id)
                                ? Colors.orange[700]
                                : Colors.grey[700],
                            fontSize: 14,
                            fontWeight: _hasCustomPricing(service.id)
                                ? FontWeight.w600
                                : FontWeight.w500,
                          ),
                        ),
                      ),
                      if (_hasCustomPricing(service.id)) ...[
                        // Clear button
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: OutlinedButton.icon(
                            onPressed: () => _clearCustomPricing(service.id),
                            icon: const Icon(Icons.clear, size: 16),
                            label: const Text('Clear'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red[600],
                              side: BorderSide(color: Colors.red[300]!),
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              minimumSize: const Size(0, 32),
                            ),
                          ),
                        ),
                        // Edit button
                        ElevatedButton.icon(
                          onPressed: () => _showCustomPricingDialog(service),
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Edit'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: const Size(0, 32),
                          ),
                        ),
                      ] else ...[
                        // Set button
                        ElevatedButton.icon(
                          onPressed: () => _showCustomPricingDialog(service),
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Set'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: const Size(0, 32),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  IconData _getServiceIcon(String? iconName) {
    switch (iconName) {
      case 'local_laundry_service':
        return Icons.local_laundry_service;
      case 'dry_cleaning':
        return Icons.dry_cleaning;
      case 'iron':
        return Icons.iron;
      case 'flash_on':
        return Icons.flash_on;
      case 'favorite':
        return Icons.favorite;
      default:
        return Icons.local_laundry_service;
    }
  }

  String _getServicePriceText(Service service) {
    // Check if custom pricing is set for this service
    final customAmount = _customAmountsPerService[service.id];
    final customMode = _customPricingModePerService[service.id];

    if (customAmount != null && customAmount > 0) {
      if (customMode == 'per_kg') {
        return '\$${customAmount.toStringAsFixed(2)} per kg (custom)';
      } else {
        return '\$${customAmount.toStringAsFixed(2)} fixed (custom)';
      }
    }

    // Default service pricing
    switch (service.pricingType) {
      case 'per_kg':
        return '\$${service.pricePerKg?.toStringAsFixed(2) ?? '0.00'} per kg';
      case 'per_item':
        return '\$${service.pricePerItem?.toStringAsFixed(2) ?? '0.00'} per item';
      case 'fixed':
        return '\$${service.basePrice.toStringAsFixed(2)} fixed price';
      default:
        return 'Price on request';
    }
  }

  bool _hasCustomPricing(String serviceId) {
    final customAmount = _customAmountsPerService[serviceId];
    return customAmount != null && customAmount > 0;
  }

  double _calculateServiceTotal(Service service) {
    final customAmount = _customAmountsPerService[service.id];
    final customMode = _customPricingModePerService[service.id];
    final weight = _serviceWeights[service.id] ?? 0.0;

    if (customAmount != null && customAmount > 0) {
      if (customMode == 'per_kg') {
        return customAmount * weight;
      } else {
        return customAmount; // fixed price
      }
    }

    // Default service pricing
    switch (service.pricingType) {
      case 'per_kg':
        return (service.pricePerKg ?? 0.0) * weight;
      case 'per_item':
        return service.pricePerItem ?? 0.0;
      case 'fixed':
        return service.basePrice;
      default:
        return 0.0;
    }
  }

  Future<double?> _showWeightInputDialog(Service service) async {
    final TextEditingController weightController = TextEditingController();
    final customAmount = _customAmountsPerService[service.id];
    final customMode = _customPricingModePerService[service.id];

    // Determine price per kg
    double pricePerKg = 0.0;
    if (customAmount != null && customMode == 'per_kg') {
      pricePerKg = customAmount;
    } else {
      pricePerKg = service.pricePerKg ?? 0.0;
    }

    return showDialog<double>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          final weight = double.tryParse(weightController.text) ?? 0.0;
          final total = pricePerKg * weight;

          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Icon(Icons.scale, color: Colors.blue[700], size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Enter Weight for ${service.name}',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How much does your laundry weigh?',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: weightController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    labelText: 'Weight',
                    hintText: 'Enter weight (e.g. 3.5)',
                    suffixText: 'kg',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
                    ),
                    prefixIcon: Icon(Icons.scale, color: Colors.blue[600]),
                  ),
                  onChanged: (value) => setDialogState(() {}),
                  autofocus: true,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Price per kg:',
                            style: TextStyle(color: Colors.grey[700]),
                          ),
                          Text(
                            '\$${pricePerKg.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Weight:',
                            style: TextStyle(color: Colors.grey[700]),
                          ),
                          Text(
                            '${weight.toStringAsFixed(1)} kg',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '\$${total.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: weight > 0
                    ? () => Navigator.of(context).pop(weight.clamp(0.1, 999.9))
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Add Service'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _clearCustomPricing(String serviceId) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning_amber, color: Colors.orange, size: 24),
            const SizedBox(width: 8),
            const Text('Clear Custom Pricing'),
          ],
        ),
        content: const Text(
          'Are you sure you want to clear the custom pricing? This will revert to the standard service pricing.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _customAmountsPerService.remove(serviceId);
        _customPricingModePerService.remove(serviceId);

        // Recalculate order items with standard pricing
        _recalculateOrderItemPricing();
      });

      // Show confirmation snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Text('Custom pricing cleared'),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    }
  }

  Future<void> _showCustomPricingDialog(Service service) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CustomPricingDialog(
        service: service,
        currentCustomAmount: _customAmountsPerService[service.id],
        currentCustomMode: _customPricingModePerService[service.id],
      ),
    );

    if (result != null) {
      if (result['customAmount'] != null) {
        final newMode = result['customMode'] as String;
        final wasPerKg = _customPricingModePerService[service.id] == 'per_kg' || service.pricingType == 'per_kg';
        final isNowPerKg = newMode == 'per_kg';

        // If switching to per_kg mode and no weight is set, ask for weight
        if (isNowPerKg && !wasPerKg && _serviceWeights[service.id] == null) {
          final weight = await _showWeightInputDialog(service);
          if (weight != null && weight > 0) {
            setState(() {
              _serviceWeights[service.id] = weight;
              _customAmountsPerService[service.id] = result['customAmount'];
              _customPricingModePerService[service.id] = newMode;
              _recalculateOrderItemPricing();
            });
          }
          // If user cancels weight input, don't apply custom pricing
          return;
        }

        setState(() {
          _customAmountsPerService[service.id] = result['customAmount'];
          _customPricingModePerService[service.id] = newMode;

          // If switching from per_kg to fixed, remove weight requirement
          if (!isNowPerKg && wasPerKg) {
            _serviceWeights.remove(service.id);
          }

          _recalculateOrderItemPricing();
        });
      } else {
        setState(() {
          // Clear custom pricing
          _customAmountsPerService.remove(service.id);
          _customPricingModePerService.remove(service.id);
          _recalculateOrderItemPricing();
        });
      }
    }
  }

  void _recalculateOrderItemPricing() {
    if (_selectedServices.isEmpty || _orderItems.isEmpty) return;

    final service = _selectedServices.first; // Assuming single service for now
    final customAmount = _customAmountsPerService[service.id];
    final customMode = _customPricingModePerService[service.id];

    for (int i = 0; i < _orderItems.length; i++) {
      final item = _orderItems[i];
      double unitPrice = 0.0;

      if (customAmount != null && customAmount > 0) {
        // Use custom pricing
        if (customMode == 'per_kg') {
          unitPrice = customAmount; // Per kg rate
        } else {
          // Fixed pricing - distribute across all items
          final totalItems = _orderItems.fold(0, (sum, item) => sum + item.quantity);
          unitPrice = totalItems > 0 ? customAmount / totalItems : 0.0;
        }
      } else {
        // Use standard service pricing
        switch (service.pricingType) {
          case 'per_item':
            unitPrice = service.pricePerItem ?? 0.0;
            break;
          case 'per_kg':
            unitPrice = service.pricePerKg ?? 0.0;
            break;
          case 'fixed':
            final totalItems = _orderItems.fold(0, (sum, item) => sum + item.quantity);
            unitPrice = totalItems > 0 ? service.basePrice / totalItems : 0.0;
            break;
        }
      }

      _orderItems[i] = OrderItem(
        garmentId: item.garmentId,
        garment: item.garment,
        quantity: item.quantity,
        unitPrice: unitPrice,
        notes: item.notes,
      );
    }
  }

  Widget _buildGarmentSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Items',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Add garments and specify quantities',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),

          // Order items summary
          if (_orderItems.isNotEmpty) ...[
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Selected Items',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${_orderItems.length} items',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ...(_orderItems
                        .take(3)
                        .map(
                          (item) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text('${item.garment.name} x${item.quantity}'),
                          ),
                        )),
                    if (_orderItems.length > 3)
                      Text(
                        '... and ${_orderItems.length - 3} more items',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Garments by category
          Expanded(child: _buildGarmentsList()),
        ],
      ),
    );
  }

  Widget _buildGarmentsList() {
    if (_loadingGarments) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.orange),
      );
    }

    if (_garmentsByCategory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.checkroom, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No garments available',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Please contact your administrator',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _garmentsByCategory.keys.length,
      itemBuilder: (context, index) {
        final category = _garmentsByCategory.keys.elementAt(index);
        final garments = _garmentsByCategory[category]!;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              _getCategoryDisplayName(category),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            leading: Icon(_getCategoryIcon(category)),
            initiallyExpanded: _expandedCategories.contains(category),
            onExpansionChanged: (expanded) {
              setState(() {
                if (expanded) {
                  _expandedCategories.add(category);
                } else {
                  _expandedCategories.remove(category);
                }
              });
            },
            children: garments.map((garment) {
              final orderItem = _orderItems.firstWhere(
                (item) => item.garment.id == garment.id,
                orElse: () => OrderItem(
                  garmentId: garment.id,
                  garment: garment,
                  quantity: 0,
                  unitPrice: 0.0, // Price will be calculated based on service
                ),
              );

              return ListTile(
                title: Text(garment.name),
                subtitle: Text(garment.description ?? 'No description'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Remove individual item pricing display since pricing is service-based
                    const SizedBox(width: 16),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove_circle_outline),
                          onPressed: orderItem.quantity > 0
                              ? () => _updateItemQuantity(
                                  garment,
                                  orderItem.quantity - 1,
                                )
                              : null,
                        ),
                        Container(
                          width: 40,
                          alignment: Alignment.center,
                          child: Text(
                            '${orderItem.quantity}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add_circle_outline),
                          onPressed: () => _updateItemQuantity(
                            garment,
                            orderItem.quantity + 1,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  void _updateItemQuantity(Garment garment, int quantity) {
    setState(() {
      if (quantity <= 0) {
        _orderItems.removeWhere((item) => item.garment.id == garment.id);
      } else {
        final existingIndex = _orderItems.indexWhere(
          (item) => item.garment.id == garment.id,
        );

        // Calculate unit price based on custom pricing or selected service
        double unitPrice = 0.0;
        if (_selectedServices.isNotEmpty) {
          final service = _selectedServices.first;
          final customAmount = _customAmountsPerService[service.id];
          final customMode = _customPricingModePerService[service.id];

          if (customAmount != null && customAmount > 0) {
            // Use custom pricing
            if (customMode == 'per_kg') {
              unitPrice = customAmount; // Per kg rate
            } else {
              // Fixed pricing - distribute across all items
              final totalItems = _orderItems.fold(0, (sum, item) => sum + item.quantity);
              unitPrice = totalItems > 0 ? customAmount / totalItems : 0.0;
            }
          } else {
            // Use standard service pricing
            switch (service.pricingType) {
              case 'per_item':
                unitPrice = service.pricePerItem ?? 0.0;
                break;
              case 'per_kg':
                // For per_kg pricing, we'll use a default weight of 1kg per item
                // In a real app, you might want to ask for weight input
                unitPrice = service.pricePerKg ?? 0.0;
                break;
              case 'fixed':
                // For fixed pricing, the price is distributed across all items
                // This is a simplified approach - you might want different logic
                unitPrice = service.basePrice;
                break;
              default:
                unitPrice = 0.0;
            }
          }
        }

        if (existingIndex >= 0) {
          _orderItems[existingIndex] = OrderItem(
            garmentId: garment.id,
            garment: garment,
            quantity: quantity,
            unitPrice: unitPrice,
          );
        } else {
          _orderItems.add(
            OrderItem(
              garmentId: garment.id,
              garment: garment,
              quantity: quantity,
              unitPrice: unitPrice,
            ),
          );
        }
      }
    });
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'clothing':
        return 'Clothing';
      case 'household':
        return 'Household Items';
      case 'special':
        return 'Special Care';
      default:
        return category.toUpperCase();
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'clothing':
        return Icons.checkroom;
      case 'household':
        return Icons.bed;
      case 'special':
        return Icons.star;
      default:
        return Icons.category;
    }
  }

  Widget _buildAddressSelection() {
    return SafeArea(
      child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            // Header with reduced size
          const Text(
            'Select Address',
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
          ),
            const SizedBox(height: 4),
          Text(
            'Choose pickup address for this order',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
            const SizedBox(height: 12),

            // Fulfillment mode selector - more compact
            Wrap(
              spacing: 8,
            children: [
              ChoiceChip(
                  label: const Text('In-store pickup', style: TextStyle(fontSize: 13)),
                selected: _fulfillmentMode == FulfillmentMode.inStorePickup,
                onSelected: (v) {
                  if (v) setState(() { _fulfillmentMode = FulfillmentMode.inStorePickup; });
                },
                  selectedColor: Colors.blue[100],
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              ChoiceChip(
                  label: const Text('Deliver to customer', style: TextStyle(fontSize: 13)),
                selected: _fulfillmentMode == FulfillmentMode.delivery,
                onSelected: (v) {
                  if (v) setState(() { _fulfillmentMode = FulfillmentMode.delivery; });
                },
                  selectedColor: Colors.orange[100],
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
            ],
          ),
            const SizedBox(height: 12),

            // Customer info card - more compact
          if (_selectedCustomer != null) ...[
            Card(
              color: Colors.orange[50],
                margin: EdgeInsets.zero,
              child: Padding(
                  padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    CircleAvatar(
                        radius: 16,
                      backgroundColor: Colors.orange,
                      child: Text(
                        _selectedCustomer!.fullName[0].toUpperCase(),
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    ),
                      const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedCustomer!.fullName,
                            style: const TextStyle(
                                fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                              overflow: TextOverflow.ellipsis,
                          ),
                          if (_selectedCustomer!.email != null)
                              Text(
                                _selectedCustomer!.email!,
                                style: const TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                          if (_selectedCustomer!.phone != null)
                              Text(
                                _selectedCustomer!.phone!,
                                style: const TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
              const SizedBox(height: 12),
          ],

          // Address or instructions depending on fulfillment mode
          Expanded(
            child: _fulfillmentMode == FulfillmentMode.inStorePickup
                ? _buildInStorePickupInfo()
                : _buildDeliveryAddressList(),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildInStorePickupInfo() {
    return SingleChildScrollView(
      child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Store selection header
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.store, color: Colors.blue[700], size: 24),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Pickup Store',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Selected store display or selection prompt
        if (_selectedStores.isNotEmpty) ...[
          // Show selected store
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.blue[300]!, width: 2),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.store, color: Colors.blue[700], size: 20),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _selectedStores.first.displayName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                            overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Selected',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          _selectedStores.first.address,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  if (_selectedStores.first.phone != null && _selectedStores.first.phone!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                          _selectedStores.first.phone!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                              ),
                              overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Change store button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showStoreSelectionDialog(),
              icon: const Icon(Icons.edit_location, size: 18),
              label: const Text('Change Store'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue[700],
                side: BorderSide(color: Colors.blue[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ] else ...[
          // No store selected - show selection prompt
          Container(
            width: double.infinity,
              padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[300]!),
            ),
            child: Column(
                mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.store_mall_directory_outlined, 
                       size: 40, color: Colors.orange[600]),
                const SizedBox(height: 12),
                Text(
                  'No pickup store selected',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please select a store where the customer can pick up their order',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showStoreSelectionDialog(),
                  icon: const Icon(Icons.store, size: 18),
                  label: const Text('Select Store'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        
          const SizedBox(height: 20),
        
        // Information about in-store pickup
        Container(
            width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                    'In-Store Pickup',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[700],
                        ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '• Customer will collect their order from the selected store\n'
                '• Order will be ready for pickup according to the schedule\n'
                '• Customer should bring ID for order verification',
                style: TextStyle(
                    fontSize: 12,
                  color: Colors.grey[700],
                    height: 1.3,
                ),
              ),
            ],
          ),
        ),
          
          // Add some bottom padding to ensure scrolling works well
          const SizedBox(height: 20),
      ],
      ),
    );
  }

  Widget _buildDeliveryAddressList() {
    if (_selectedCustomer == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_search, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No customer selected',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Please select a customer first',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return FutureBuilder<List<CustomerAddress>>(
      future: CustomerService.getCustomerAddresses(_selectedCustomer!.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(color: Colors.orange),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                const SizedBox(height: 16),
                Text(
                  'Error loading addresses',
                  style: TextStyle(fontSize: 18, color: Colors.red[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  snapshot.error.toString(),
                  style: TextStyle(color: Colors.red[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final addresses = snapshot.data ?? [];

        if (addresses.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.location_off, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No addresses found',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Customer has no saved addresses',
                  style: TextStyle(color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showAddAddressDialog(),
                  icon: const Icon(Icons.add_location),
                  label: const Text('Add Address'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Selected address display
            if (_deliveryAddress != null) ...[
              Card(
                color: Colors.green[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green[600]),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Selected Delivery Address:',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.green,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _deliveryAddress!.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(_deliveryAddress!.fullAddress),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          setState(() {
                            _deliveryAddress = null;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Address list
            Expanded(
              child: ListView.builder(
                itemCount: addresses.length,
                itemBuilder: (context, index) {
                  final address = addresses[index];
                  final isSelected = _deliveryAddress?.id == address.id;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    color: isSelected ? Colors.orange[50] : null,
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isSelected
                            ? Colors.orange
                            : Colors.grey[300],
                        child: Icon(
                          address.isDefault ? Icons.home : Icons.location_on,
                          color: isSelected ? Colors.white : Colors.grey[600],
                        ),
                      ),
                      title: Row(
                        children: [
                          Text(
                            address.title,
                            style: TextStyle(
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          if (address.isDefault) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Default',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      subtitle: Text(address.fullAddress),
                      trailing: isSelected
                          ? const Icon(Icons.check_circle, color: Colors.orange)
                          : const Icon(
                              Icons.radio_button_unchecked,
                              color: Colors.grey,
                            ),
                      onTap: () {
                        setState(() {
                          _deliveryAddress = address;
                        });
                      },
                    ),
                  );
                },
              ),
            ),

            // Add address button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showAddAddressDialog(),
                icon: const Icon(Icons.add_location),
                label: const Text('Add New Address'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  side: const BorderSide(color: Colors.orange),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            // Special Instructions
            const SizedBox(height: 16),
            const Text(
              'Special Instructions (Optional)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _specialInstructionsController,
              maxLines: 2,
              decoration: InputDecoration(
                hintText: 'Any special instructions for pickup or handling...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  // Store selection dialog for in-store pickup
  Future<void> _showStoreSelectionDialog() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.85,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) {
          return StatefulBuilder(
            builder: (context, setModalState) => Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.store, color: Colors.blue[700], size: 24),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            'Choose Pickup Store',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Subtitle
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    child: Text(
                      'Select where the customer can collect their order',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  
                  // Store list
                  Expanded(
                    child: _loadingStores
                        ? const Center(
                            child: CircularProgressIndicator(color: Colors.blue),
                          )
                        : _stores.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.store_mall_directory_outlined,
                                      size: 64,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'No stores available',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Please contact your administrator to add stores',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                controller: scrollController,
                                padding: const EdgeInsets.all(20),
                                itemCount: _stores.length,
                                itemBuilder: (context, index) {
                                  final store = _stores[index];
                                  final isSelected = _selectedStores.any((s) => s.id == store.id);
                                  
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    elevation: isSelected ? 4 : 1,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      side: BorderSide(
                                        color: isSelected 
                                            ? Colors.blue[300]! 
                                            : Colors.transparent,
                                        width: 2,
                                      ),
                                    ),
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(12),
                                      onTap: () {
                                        setState(() {
                                          // For in-store pickup, typically only one store is needed
                                          _selectedStores.clear();
                                          _selectedStores.add(store);
                                        });
                                        
                                        // Close the dialog after selection
                                        Navigator.pop(context);
                                        
                                        // Show confirmation snackbar
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Row(
                                              children: [
                                                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                                                const SizedBox(width: 8),
                                                Expanded(
                                                  child: Text('Selected ${store.displayName} for pickup'),
                                                ),
                                              ],
                                            ),
                                            backgroundColor: Colors.green[600],
                                            duration: const Duration(seconds: 2),
                                            behavior: SnackBarBehavior.floating,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                          ),
                                        );
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Row(
                                          children: [
                                            // Store icon
                                            Container(
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: isSelected 
                                                    ? Colors.blue[100] 
                                                    : Colors.grey[100],
                                                borderRadius: BorderRadius.circular(10),
                                              ),
                                              child: Icon(
                                                Icons.store,
                                                color: isSelected 
                                                    ? Colors.blue[700] 
                                                    : Colors.grey[600],
                                                size: 24,
                                              ),
                                            ),
                                            const SizedBox(width: 16),
                                            
                                            // Store details
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: Text(
                                                          store.displayName,
                                                          style: TextStyle(
                                                            fontSize: 16,
                                                            fontWeight: FontWeight.w600,
                                                            color: isSelected 
                                                                ? Colors.blue[700] 
                                                                : Colors.black87,
                                                          ),
                                                        ),
                                                      ),
                                                      if (isSelected) ...[
                                                        Container(
                                                          padding: const EdgeInsets.symmetric(
                                                            horizontal: 8, 
                                                            vertical: 4,
                                                          ),
                                                          decoration: BoxDecoration(
                                                            color: Colors.blue[100],
                                                            borderRadius: BorderRadius.circular(12),
                                                          ),
                                                          child: Text(
                                                            'Selected',
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              color: Colors.blue[700],
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ],
                                                  ),
                                                  const SizedBox(height: 8),
                                                  
                                                  // Store address
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.location_on,
                                                        size: 16,
                                                        color: Colors.grey[600],
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Expanded(
                                                        child: Text(
                                                          store.address,
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            color: Colors.grey[600],
                                                          ),
                                                          maxLines: 2,
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  
                                                  // Store phone (if available)
                                                  if (store.phone != null && store.phone!.isNotEmpty) ...[
                                                    const SizedBox(height: 4),
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.phone,
                                                          size: 16,
                                                          color: Colors.grey[600],
                                                        ),
                                                        const SizedBox(width: 4),
                                                        Text(
                                                          store.phone!,
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            color: Colors.grey[600],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            ),
                                            
                                            // Selection indicator
                                            Container(
                                              padding: const EdgeInsets.all(8),
                                              child: Icon(
                                                isSelected 
                                                    ? Icons.radio_button_checked 
                                                    : Icons.radio_button_unchecked,
                                                color: isSelected 
                                                    ? Colors.blue[700] 
                                                    : Colors.grey[400],
                                                size: 20,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                  ),
                  
                  // Bottom action (optional - for future multiple store selection)
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      border: Border(top: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Text(
                      'Tip: Select the store most convenient for the customer to pick up their order.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showAddAddressDialog() {
    final titleController = TextEditingController();
    final streetController = TextEditingController();
    final cityController = TextEditingController();
    final stateController = TextEditingController();
    final postalCodeController = TextEditingController();
    country_model.Country? selectedCountry;
    bool isDefault = false;
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.add_location, color: Colors.orange[700]),
              ),
              const SizedBox(width: 12),
              const Text(
                'Add New Address',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.9,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title field
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Address Title',
                      hintText: 'e.g. Home, Office, etc.',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.label_outline),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Country field (moved to top of location section)
                  InkWell(
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        selectedCountry: selectedCountry,
                        onSelect: (country) {
                          setDialogState(() {
                            selectedCountry = country;
                          });
                        },
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[400]!),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.flag_outlined, color: Colors.grey),
                          const SizedBox(width: 12),
                          Expanded(
                            child: selectedCountry != null
                                ? Row(
                                    children: [
                                      Text(
                                        selectedCountry!.flagEmoji ?? '🌐',
                                        style: const TextStyle(fontSize: 20),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          selectedCountry!.name,
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    'Select Country *',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 16,
                                    ),
                                  ),
                          ),
                          const Icon(Icons.arrow_drop_down, color: Colors.grey),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Street address field
                  TextField(
                    controller: streetController,
                    maxLines: 2,
                    decoration: const InputDecoration(
                      labelText: 'Street Address *',
                      hintText: 'Enter full street address',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.home_outlined),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // City field
                  TextField(
                    controller: cityController,
                    decoration: const InputDecoration(
                      labelText: 'City *',
                      hintText: 'e.g. Nairobi',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.location_city),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // State/County and Postal Code row
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                    controller: stateController,
                    decoration: const InputDecoration(
                      labelText: 'State/County',
                      hintText: 'e.g. Nairobi County',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.map_outlined),
                    ),
                  ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextField(
                          controller: postalCodeController,
                          decoration: const InputDecoration(
                            labelText: 'Postal Code',
                            hintText: '00100',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.local_post_office),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Default address checkbox
                  CheckboxListTile(
                    title: const Text('Set as default address'),
                    value: isDefault,
                    onChanged: (value) => setDialogState(() => isDefault = value ?? false),
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                    activeColor: Colors.orange,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: isLoading ? null : () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: isLoading || titleController.text.isEmpty || streetController.text.isEmpty || cityController.text.isEmpty || selectedCountry == null
                  ? null
                  : () async {
                      setDialogState(() => isLoading = true);
                      
                      // Capture context and mounted state before async operation
                      final navigator = Navigator.of(context);
                      final messenger = ScaffoldMessenger.of(context);
                      
                      try {
                        // Create the new address
                        final newAddress = await CustomerService.createCustomerAddress(
                          customerId: _selectedCustomer!.id,
                          title: titleController.text,
                          addressLine1: streetController.text,
                          city: cityController.text,
                          state: stateController.text.isNotEmpty ? stateController.text : null,
                          postalCode: postalCodeController.text.isNotEmpty ? postalCodeController.text : null,
                          country: selectedCountry!.name,
                          isDefault: isDefault,
                        );
                        
                        if (mounted) {
                          setState(() {
                            _deliveryAddress = newAddress;
                          });
                          
                          navigator.pop();
                          
                          messenger.showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.check_circle, color: Colors.white, size: 20),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text('Address "${newAddress.title}" added successfully!'),
                                  ),
                                ],
                              ),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          messenger.showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.error_outline, color: Colors.white),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text('Error adding address: $e'),
                                  ),
                                ],
                              ),
                              backgroundColor: Colors.red,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          );
                        }
                      } finally {
                        if (mounted) {
                          setDialogState(() => isLoading = false);
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text('Add Address'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Schedule Pickup & Delivery',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose your preferred pickup and delivery times',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Pickup Section
                  _buildPickupSection(),
                  const SizedBox(height: 32),

                  // Delivery Section
                  _buildDeliverySection(),
                  const SizedBox(height: 32),

                  // Summary Card
                  if (_pickupDate != null && _pickupTimeSlot != null)
                    _buildScheduleSummary(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickupSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.schedule, color: Colors.orange[700]),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Pickup Schedule',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Date Selection
            const Text(
              'Select Pickup Date',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            InkWell(
              onTap: () => _selectPickupDate(),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, color: Colors.grey[600]),
                    const SizedBox(width: 12),
                    Text(
                      _pickupDate != null
                          ? _formatDate(_pickupDate!)
                          : 'Select pickup date',
                      style: TextStyle(
                        fontSize: 16,
                        color: _pickupDate != null ? Colors.black : Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Icon(Icons.arrow_drop_down, color: Colors.grey[600]),
                  ],
                ),
              ),
            ),

            if (_pickupDate != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Select Pickup Time',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              _buildTimeSlotGrid(isPickup: true),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDeliverySection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.local_shipping, color: Colors.blue[700]),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Delivery Schedule',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Date Selection
            const Text(
              'Select Delivery Date',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            InkWell(
              onTap: () => _selectDeliveryDate(),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, color: Colors.grey[600]),
                    const SizedBox(width: 12),
                    Text(
                      _deliveryDate != null
                          ? _formatDate(_deliveryDate!)
                          : 'Select delivery date (optional)',
                      style: TextStyle(
                        fontSize: 16,
                        color: _deliveryDate != null ? Colors.black : Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Icon(Icons.arrow_drop_down, color: Colors.grey[600]),
                  ],
                ),
              ),
            ),

            if (_deliveryDate != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Select Delivery Time',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              _buildTimeSlotGrid(isPickup: false),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Order Summary',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Review your order details before placing',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Store & Customer Info
                  _buildSummarySection(
                    title: 'Store & Customer',
                    icon: Icons.store,
                    children: [
                      _buildSummaryRow('Stores', _selectedStores.isNotEmpty 
                          ? _selectedStores.map((s) => s.displayName).join(', ')
                          : 'N/A'),
                      _buildSummaryRow('Customer', _selectedCustomer?.fullName ?? 'N/A'),
                      if (_selectedCustomer?.email != null)
                        _buildSummaryRow('Email', _selectedCustomer!.email!),
                      if (_selectedCustomer?.phone != null)
                        _buildSummaryRow('Phone', _selectedCustomer!.phone!),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Services
                  _buildSummarySection(
                    title: 'Services',
                    icon: Icons.local_laundry_service,
                    children: _selectedServices.map((service) =>
                      _buildSummaryRow(service.name, _getServicePriceText(service))
                    ).toList(),
                  ),

                  const SizedBox(height: 20),

                  // Items
                  _buildSummarySection(
                    title: 'Items',
                    icon: Icons.checkroom,
                    children: _orderItems.map((item) =>
                      _buildSummaryRow(
                        '${item.garment.name} x${item.quantity}',
                        '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}'
                      )
                    ).toList(),
                  ),

                  const SizedBox(height: 20),

                  // Address
                  _buildSummarySection(
                    title: 'Fulfillment',
                    icon: Icons.local_shipping,
                    children: [
                      if (_fulfillmentMode == FulfillmentMode.inStorePickup) ...[
                        _buildSummaryRow('Type', 'In-store pickup'),
                        _buildSummaryRow('Stores', _selectedStores.isNotEmpty 
                            ? _selectedStores.map((s) => s.displayName).join(', ')
                            : 'N/A'),
                        if (_selectedStores.isNotEmpty)
                          _buildSummaryRow('Store Addresses', 
                              _selectedStores.map((s) => s.address).join('\n')),
                      ] else ...[
                        _buildSummaryRow('Type', 'Delivery to customer'),
                        _buildSummaryRow('Address', _deliveryAddress?.title ?? 'N/A'),
                        if (_deliveryAddress != null)
                          _buildSummaryRow('Details', _deliveryAddress!.fullAddress),
                      ],
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Schedule
                  _buildSummarySection(
                    title: 'Schedule',
                    icon: Icons.schedule,
                    children: [
                      if (_pickupDate != null && _pickupTimeSlot != null)
                        _buildSummaryRow('Pickup', '${_formatDate(_pickupDate!)} at $_pickupTimeSlot'),
                      if (_deliveryDate != null && _deliveryTimeSlot != null)
                        _buildSummaryRow('Delivery', '${_formatDate(_deliveryDate!)} at $_deliveryTimeSlot'),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Special Instructions
                  if (_specialInstructionsController.text.isNotEmpty)
                    _buildSummarySection(
                      title: 'Special Instructions',
                      icon: Icons.note,
                      children: [
                        Text(
                          _specialInstructionsController.text,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),

                  if (_specialInstructionsController.text.isNotEmpty)
                    const SizedBox(height: 20),

                  // Total
                  _buildTotalSection(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  setState(() {
                    _currentStep--;
                  });
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Text('Previous'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceedToNext() ? _handleNext : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(_currentStep == 6 ? 'Create Order' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceedToNext() {
    switch (_currentStep) {
      case 0: // Store selection
        return _selectedStores.isNotEmpty;
      case 1: // Customer selection
        return _selectedCustomer != null;
      case 2: // Service selection
        return _selectedServices.isNotEmpty;
      case 3: // Garment selection
        return _orderItems.isNotEmpty || _selectedServices.isNotEmpty;
      case 4: // Address selection
        return _fulfillmentMode == FulfillmentMode.inStorePickup ? true : _deliveryAddress != null;
      case 5: // Time slot selection
        return _pickupDate != null && _pickupTimeSlot != null;
      case 6: // Order summary
        return true;
      default:
        return false;
    }
  }

  bool _validateOrderData() {
    // Check if all required data is present
    if (_selectedStores.isEmpty) {
      _showErrorSnackBar('Please select at least one store');
      return false;
    }

    if (_selectedCustomer == null) {
      _showErrorSnackBar('Please select a customer');
      return false;
    }

    if (_selectedServices.isEmpty) {
      _showErrorSnackBar('Please select at least one service');
      return false;
    }

    // Check if we have either order items or selected services
    // (services can create generic order items automatically)
    if (_orderItems.isEmpty && _selectedServices.isEmpty) {
      _showErrorSnackBar('Please add at least one garment or select a service');
      return false;
    }

    if (_fulfillmentMode == FulfillmentMode.delivery && _deliveryAddress == null) {
      _showErrorSnackBar('Please select a delivery address');
      return false;
    }

    if (_pickupDate == null || _pickupTimeSlot == null) {
      _showErrorSnackBar('Please select pickup date and time');
      return false;
    }

    return true;
  }

  Future<void> _editService(Service service) async {
    final needsWeight = service.pricingType == 'per_kg' ||
                       _customPricingModePerService[service.id] == 'per_kg';

    if (needsWeight) {
      // Show weight input dialog for per_kg services
      final newWeight = await _showWeightInputDialog(service);

      if (newWeight != null && newWeight > 0) {
        setState(() {
          _serviceWeights[service.id] = newWeight;
        });
      }
    } else {
      // For fixed price services, show custom pricing dialog
      await _showCustomPricingDialog(service);
    }
  }



  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _handleNext() {
    if (_currentStep == 6) {
      // Create order
      _createOrder();
    } else {
      // Go to next step
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Widget _buildTimeSlotGrid({required bool isPickup}) {
    final timeSlots = [
      '09:00-12:00',
      '12:00-15:00',
      '15:00-18:00',
      '18:00-21:00',
    ];

    final selectedSlot = isPickup ? _pickupTimeSlot : _deliveryTimeSlot;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: timeSlots.length,
      itemBuilder: (context, index) {
        final slot = timeSlots[index];
        final isSelected = selectedSlot == slot;

        return InkWell(
          onTap: () {
            setState(() {
              if (isPickup) {
                _pickupTimeSlot = slot;
              } else {
                _deliveryTimeSlot = slot;
              }
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? (isPickup ? Colors.orange[100] : Colors.blue[100])
                  : Colors.grey[50],
              border: Border.all(
                color: isSelected
                    ? (isPickup ? Colors.orange : Colors.blue)
                    : Colors.grey[300]!,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                slot,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? (isPickup ? Colors.orange[700] : Colors.blue[700])
                      : Colors.grey[700],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildScheduleSummary() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'Schedule Summary',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Pickup Summary
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.orange[600], size: 20),
                const SizedBox(width: 8),
                const Text('Pickup: ', style: TextStyle(fontWeight: FontWeight.w600)),
                Text('${_formatDate(_pickupDate!)} at $_pickupTimeSlot'),
              ],
            ),

            if (_deliveryDate != null && _deliveryTimeSlot != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.local_shipping, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  const Text('Delivery: ', style: TextStyle(fontWeight: FontWeight.w600)),
                  Text('${_formatDate(_deliveryDate!)} at $_deliveryTimeSlot'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _pickupDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.orange,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _pickupDate = picked;
        _pickupTimeSlot = null; // Reset time slot when date changes
      });
    }
  }

  Future<void> _selectDeliveryDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _deliveryDate ?? (_pickupDate?.add(const Duration(days: 1)) ?? DateTime.now().add(const Duration(days: 2))),
      firstDate: _pickupDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _deliveryDate = picked;
        _deliveryTimeSlot = null; // Reset time slot when date changes
      });
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  Widget _buildSummarySection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.orange[700], size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSection() {
    double subtotal = 0.0;

    // Calculate subtotal from order items
    for (final item in _orderItems) {
      subtotal += item.quantity * item.unitPrice;
    }

    final taxAmount = subtotal * 0.1; // 10% tax
    final deliveryFee = _deliveryDate != null ? 5.0 : 0.0; // $5 delivery fee if delivery is selected
    final total = subtotal + taxAmount + deliveryFee;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'Order Total',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildTotalRow('Subtotal', subtotal),
            _buildTotalRow('Tax (10%)', taxAmount),
            if (deliveryFee > 0) _buildTotalRow('Delivery Fee', deliveryFee),

            const Divider(thickness: 2),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '\$${total.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
