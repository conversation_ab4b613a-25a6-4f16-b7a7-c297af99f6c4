import 'package:flutter/material.dart';
import '../../models/service_model.dart';
import '../../models/garment_model.dart';
import '../../services/service_service.dart';
import '../../services/garment_service.dart';

class ServiceDiscoveryScreen extends StatefulWidget {
  const ServiceDiscoveryScreen({super.key});

  @override
  State<ServiceDiscoveryScreen> createState() => _ServiceDiscoveryScreenState();
}

class _ServiceDiscoveryScreenState extends State<ServiceDiscoveryScreen> {
  List<Service> _services = [];
  Map<String, List<Garment>> _garmentsByCategory = {};
  bool _loadingServices = false;
  bool _loadingGarments = false;
  String _selectedCategory = 'all';
  String _selectedServiceType = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadServices(),
      _loadGarments(),
    ]);
  }

  Future<void> _loadServices() async {
    setState(() => _loadingServices = true);
    try {
      final services = await ServiceService.getAllServices();
      setState(() {
        _services = services;
        _loadingServices = false;
      });
    } catch (e) {
      setState(() => _loadingServices = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading services: $e')),
        );
      }
    }
  }

  Future<void> _loadGarments() async {
    setState(() => _loadingGarments = true);
    try {
      final garments = await GarmentService.getGarmentsByCategory();
      setState(() {
        _garmentsByCategory = garments;
        _loadingGarments = false;
      });
    } catch (e) {
      setState(() => _loadingGarments = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading garments: $e')),
        );
      }
    }
  }

  List<Service> get _filteredServices {
    return _services.where((service) {
      // Category filter
      if (_selectedCategory != 'all' && service.category != _selectedCategory) {
        return false;
      }
      
      // Service type filter
      if (_selectedServiceType != 'all' && service.serviceType != _selectedServiceType) {
        return false;
      }
      
      // Search query filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return service.name.toLowerCase().contains(query) ||
               (service.description?.toLowerCase().contains(query) ?? false);
      }
      
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Service Discovery'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: _loadingServices || _loadingGarments
                ? const Center(child: CircularProgressIndicator())
                : _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Search bar
          TextField(
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: 'Search services...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => setState(() => _searchQuery = ''),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          const SizedBox(height: 16),
          
          // Category filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'All Categories'),
                _buildFilterChip('basic', 'Basic'),
                _buildFilterChip('premium', 'Premium'),
                _buildFilterChip('luxury', 'Luxury'),
              ],
            ),
          ),
          const SizedBox(height: 12),
          
          // Service type filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'All Services'),
                _buildFilterChip('wash_fold', 'Wash & Fold'),
                _buildFilterChip('dry_cleaning', 'Dry Cleaning'),
                _buildFilterChip('ironing', 'Ironing'),
                _buildFilterChip('express', 'Express'),
                _buildFilterChip('delicate', 'Delicate Care'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _getFilterType(value) == _getFilterType(_selectedCategory) ||
                       _getFilterType(value) == _getFilterType(_selectedServiceType);
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            if (_getFilterType(value) == 'category') {
              _selectedCategory = value;
            } else {
              _selectedServiceType = value;
            }
          });
        },
        selectedColor: Colors.blue[100],
        checkmarkColor: Colors.blue[700],
        labelStyle: TextStyle(
          color: isSelected ? Colors.blue[700] : Colors.grey[700],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  String _getFilterType(String value) {
    if (['all', 'basic', 'premium', 'luxury'].contains(value)) {
      return 'category';
    } else {
      return 'serviceType';
    }
  }

  Widget _buildContent() {
    if (_filteredServices.isEmpty) {
      return _buildEmptyState();
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildServicesOverview(),
        const SizedBox(height: 24),
        _buildServicesList(),
        const SizedBox(height: 24),
        _buildGarmentCategories(),
      ],
    );
  }

  Widget _buildServicesOverview() {
    final totalServices = _filteredServices.length;
    final basicServices = _filteredServices.where((s) => s.category == 'basic').length;
    final premiumServices = _filteredServices.where((s) => s.category == 'premium').length;
    final luxuryServices = _filteredServices.where((s) => s.category == 'luxury').length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Services Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Services',
                    totalServices.toString(),
                    Colors.blue,
                    Icons.local_laundry_service,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Basic',
                    basicServices.toString(),
                    Colors.green,
                    Icons.star_outline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Premium',
                    premiumServices.toString(),
                    Colors.orange,
                    Icons.star_half,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Luxury',
                    luxuryServices.toString(),
                    Colors.purple,
                    Icons.star,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServicesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Available Services',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ..._filteredServices.map((service) => _buildServiceCard(service)),
      ],
    );
  }

  Widget _buildServiceCard(Service service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getServiceColor(service.serviceType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    _getServiceIcon(service.icon),
                    color: _getServiceColor(service.serviceType),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              service.name,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          _buildCategoryBadge(service.category),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        service.serviceTypeDisplayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Service description
            if (service.description != null) ...[
              Text(
                service.description!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Pricing information
            _buildPricingSection(service),
            const SizedBox(height: 16),
            
            // Service details
            _buildServiceDetails(service),
            const SizedBox(height: 16),
            
            // Suitable garments
            _buildSuitableGarments(service),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBadge(String category) {
    Color color;
    String label;
    
    switch (category) {
      case 'basic':
        color = Colors.green;
        label = 'Basic';
        break;
      case 'premium':
        color = Colors.orange;
        label = 'Premium';
        break;
      case 'luxury':
        color = Colors.purple;
        label = 'Luxury';
        break;
      default:
        color = Colors.grey;
        label = category;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildPricingSection(Service service) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.attach_money, color: Colors.green[700], size: 20),
              const SizedBox(width: 8),
              const Text(
                'Pricing Information',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child:                 _buildPricingItem(
                  'Base Price',
                  '\$${service.basePrice.toStringAsFixed(2)}',
                  Icons.attach_money,
                ),
              ),
              if (service.pricePerKg != null)
                Expanded(
                  child: _buildPricingItem(
                    'Per KG',
                    '\$${service.pricePerKg!.toStringAsFixed(2)}',
                    Icons.scale,
                  ),
                ),
              if (service.pricePerItem != null)
                Expanded(
                  child: _buildPricingItem(
                    'Per Item',
                    '\$${service.pricePerItem!.toStringAsFixed(2)}',
                    Icons.inventory,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPricingItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.green[600], size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.green[700],
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.green[600],
          ),
        ),
      ],
    );
  }

  Widget _buildServiceDetails(Service service) {
    return Row(
      children: [
        Expanded(
          child: _buildDetailItem(
            'Processing Time',
            service.processingTimeRange,
            Icons.schedule,
            Colors.blue,
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            'Express Service',
            service.isExpress ? 'Yes' : 'No',
            Icons.flash_on,
            service.isExpress ? Colors.orange : Colors.grey,
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            'Special Care',
            service.needsSpecialCare ? 'Required' : 'Standard',
            Icons.favorite,
            service.needsSpecialCare ? Colors.red : Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuitableGarments(Service service) {
    final suitableGarments = _garmentsByCategory.entries
        .where((entry) => service.isSuitableForGarment(entry.key))
        .toList();

    if (suitableGarments.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.checkroom, color: Colors.blue[600], size: 20),
            const SizedBox(width: 8),
            const Text(
              'Suitable for:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suitableGarments.map((entry) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Text(
                GarmentCategory.getDisplayName(entry.key),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildGarmentCategories() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Garment Categories',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ..._garmentsByCategory.entries.map((entry) => _buildGarmentCategoryCard(entry.key, entry.value)),
      ],
    );
  }

  Widget _buildGarmentCategoryCard(String category, List<Garment> garments) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Row(
          children: [
            Icon(_getCategoryIcon(category), color: _getCategoryColor(category)),
            const SizedBox(width: 12),
            Text(
              GarmentCategory.getDisplayName(category),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getCategoryColor(category).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${garments.length} items',
                style: TextStyle(
                  fontSize: 12,
                  color: _getCategoryColor(category),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        children: garments.map((garment) => _buildGarmentItem(garment)).toList(),
      ),
    );
  }

  Widget _buildGarmentItem(Garment garment) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: garment.isSpecialCare ? Colors.red[100] : Colors.grey[100],
        child: Icon(
          _getGarmentIcon(garment.icon),
          color: garment.isSpecialCare ? Colors.red[600] : Colors.grey[600],
          size: 20,
        ),
      ),
      title: Text(garment.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (garment.description != null) Text(garment.description!),
          if (garment.material != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.fiber_manual_record, size: 8, color: Colors.grey[500]),
                const SizedBox(width: 4),
                Text(
                  garment.materialDisplayName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
          if (garment.isSpecialCare) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.favorite, size: 12, color: Colors.red[600]),
                const SizedBox(width: 4),
                Text(
                  'Special Care Required',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      trailing: garment.isSpecialCare
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Special',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.red[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No services found',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or search query',
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getServiceColor(String serviceType) {
    switch (serviceType) {
      case 'wash_fold':
        return Colors.blue;
      case 'dry_cleaning':
        return Colors.green;
      case 'ironing':
        return Colors.orange;
      case 'express':
        return Colors.purple;
      case 'delicate':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getServiceIcon(String? iconName) {
    switch (iconName) {
      case 'local_laundry_service':
        return Icons.local_laundry_service;
      case 'dry_cleaning':
        return Icons.dry_cleaning;
      case 'iron':
        return Icons.iron;
      case 'flash_on':
        return Icons.flash_on;
      case 'favorite':
        return Icons.favorite;
      default:
        return Icons.local_laundry_service;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'clothing':
        return Icons.checkroom;
      case 'household':
        return Icons.bed;
      case 'special':
        return Icons.star;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'clothing':
        return Colors.blue;
      case 'household':
        return Colors.green;
      case 'special':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getGarmentIcon(String? iconName) {
    switch (iconName) {
      case 'checkroom':
        return Icons.checkroom;
      case 'bed':
        return Icons.bed;
      case 'shower':
        return Icons.shower;
      case 'window':
        return Icons.window;
      case 'favorite':
        return Icons.favorite;
      case 'work':
        return Icons.work;
      case 'star':
        return Icons.star;
      default:
        return Icons.checkroom;
    }
  }
}
