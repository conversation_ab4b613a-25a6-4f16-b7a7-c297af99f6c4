import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:currency_picker/currency_picker.dart';
import '../services/auth_service.dart';
import '../services/settings_service.dart';
import '../services/currency_service.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/staff_auth_provider.dart';
import 'support/help_support_page.dart';
import 'support/privacy_policy_page.dart';
import 'support/terms_of_service_page.dart';
import 'support/changelog_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _biometricEnabled = false;
  bool _biometricAvailable = false;
  String _selectedTheme = 'System';
  String _companyName = '';
  Currency? _selectedCurrency;
  bool _isLoadingSettings = true;
  late final AuthService authService;
  late final SettingsService settingsService;
  late final AppCurrencyService currencyService;
  final LocalAuthentication localAuth = LocalAuthentication();

  @override
  void initState() {
    super.initState();
    authService = AuthService(supabase: Supabase.instance.client);
    settingsService = SettingsService(supabase: Supabase.instance.client);
    currencyService = AppCurrencyService();
    _initializeBiometric();
    _loadUserSettings();
    // Initialize theme from provider after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _selectedTheme = context.read<ThemeProvider>().themeModeString;
        });
      }
    });
  }

  void _setTheme(String theme) async {
    final themeProvider = context.read<ThemeProvider>();
    await themeProvider.setThemeModeFromString(theme);

    if (mounted) {
      setState(() {
        _selectedTheme = themeProvider.themeModeString;
      });

      // Show feedback to user
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Theme changed to $theme'),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  String _getCurrencySymbolFromCode(String code) {
    // Common currency symbols - you can expand this list
    const currencySymbols = {
      'KES': 'KSh', // Kenyan Shilling (default)
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'INR': '₹',
      'AUD': 'A\$',
      'CAD': 'C\$',
      'CHF': 'CHF',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł',
      'CZK': 'Kč',
      'HUF': 'Ft',
      'RUB': '₽',
      'TRY': '₺',
      'BRL': 'R\$',
      'MXN': '\$',
      'ARS': '\$',
      'CLP': '\$',
      'COP': '\$',
      'PEN': 'S/',
      'UYU': '\$U',
    };

    return currencySymbols[code] ?? code;
  }

  Future<void> _loadUserSettings() async {
    try {
      setState(() {
        _isLoadingSettings = true;
      });

      // Load settings from database
      final userSettings = await settingsService.getSettings();

      // Load current currency from currency service
      final currentCurrency = await currencyService.getSelectedCurrency();

      if (mounted) {
        setState(() {
          _companyName = userSettings?.companyName ?? '';

          // If user has currency setting in database, use it, otherwise use current currency
          if (userSettings?.currencyCode != null) {
            // Try to get currency from code
            try {
              _selectedCurrency = Currency.from(
                json: {
                  'code': userSettings!.currencyCode!,
                  'name': userSettings.currencyCode!,
                  'symbol': _getCurrencySymbolFromCode(
                    userSettings.currencyCode!,
                  ),
                  'flag': '',
                  'number': 0,
                  'decimal_digits': 2,
                  'name_plural': userSettings.currencyCode!,
                },
              );
            } catch (e) {
              // Fallback to current currency if code not found
              _selectedCurrency = currentCurrency;
            }
          } else {
            _selectedCurrency = currentCurrency;
          }

          _isLoadingSettings = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user settings: $e');
      if (mounted) {
        setState(() {
          _isLoadingSettings = false;
        });
      }
    }
  }

  Future<void> _initializeBiometric() async {
    try {
      // Check if biometric authentication is available
      _biometricAvailable = await localAuth.canCheckBiometrics;

      // Also check if device has enrolled biometrics
      if (_biometricAvailable) {
        final availableBiometrics = await localAuth.getAvailableBiometrics();
        _biometricAvailable = availableBiometrics.isNotEmpty;
      }

      // Check if biometric is currently enabled in preferences
      if (_biometricAvailable) {
        _biometricEnabled = await authService.isBiometricEnabled();
      } else {
        _biometricEnabled = false;
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Biometric initialization error: $e');
      _biometricAvailable = false;
      _biometricEnabled = false;
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StaffAuthProvider>(
      builder: (context, staffAuthProvider, child) {
        final isStaff = staffAuthProvider.userType == UserType.staff;

        return Scaffold(
          appBar: AppBar(
            title: const Text('Settings'),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          body: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Security Settings Section
              _buildSectionHeader('Security Settings'),
              Card(child: Column(children: [_buildBiometricTile()])),
              const SizedBox(height: 20),

              // App Preferences Section
              _buildSectionHeader('App Preferences'),
              Card(
                child: Column(
                  children: [
                    // Show company name and currency only for staff
                    if (isStaff) ...[
                      _buildSettingTile(
                        icon: Icons.business,
                        title: 'Company Name',
                        subtitle: _isLoadingSettings
                            ? 'Loading...'
                            : (_companyName.isEmpty
                                  ? 'Contact admin to set company name'
                                  : '$_companyName\n• Contact admin to change'),
                        trailing: const Icon(
                          Icons.lock,
                          size: 16,
                          color: Colors.grey,
                        ),
                        onTap: () => _showCompanyNameLockedDialog(),
                      ),
                      const Divider(height: 1),
                      _buildSettingTile(
                        icon: Icons.attach_money,
                        title: 'Currency',
                        subtitle: _isLoadingSettings
                            ? 'Loading...'
                            : (_selectedCurrency != null
                                  ? '${_selectedCurrency!.name} (${_selectedCurrency!.code})'
                                  : 'KES (Kenyan Shilling)'),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: _isLoadingSettings
                            ? null
                            : () => _showCurrencyPicker(),
                      ),
                      const Divider(height: 1),
                    ],
                    // Theme setting for both staff and customers
                    _buildSettingTile(
                      icon: Icons.palette,
                      title: 'Theme',
                      subtitle: _selectedTheme,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => _showThemeSelector(),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Support & Info Section
              _buildSectionHeader('Support & Information'),
              Card(
                child: Column(
                  children: [
                    _buildSettingTile(
                      icon: Icons.help_outline,
                      title: 'Help & Support',
                      subtitle: 'Get help and contact support',
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const HelpSupportPage(),
                        ),
                      ),
                    ),
                    const Divider(height: 1),
                    _buildSettingTile(
                      icon: Icons.privacy_tip_outlined,
                      title: 'Privacy Policy',
                      subtitle: 'Read our privacy policy',
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const PrivacyPolicyPage(),
                        ),
                      ),
                    ),
                    const Divider(height: 1),
                    _buildSettingTile(
                      icon: Icons.description_outlined,
                      title: 'Terms of Service',
                      subtitle: 'Read our terms of service',
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const TermsOfServicePage(),
                        ),
                      ),
                    ),
                    const Divider(height: 1),
                    _buildSettingTile(
                      icon: Icons.update,
                      title: 'Change Log',
                      subtitle: 'View app updates and changes',
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ChangelogPage(),
                        ),
                      ),
                    ),
                    const Divider(height: 1),
                    _buildSettingTile(
                      icon: Icons.info_outline,
                      title: 'About',
                      subtitle: 'App version and information',
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () => _showAbout(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildBiometricTile() {
    String subtitle;
    Color statusColor;
    IconData statusIcon;

    if (!_biometricAvailable) {
      subtitle =
          'Not available - Device not supported or no biometrics enrolled';
      statusColor = Colors.grey;
      statusIcon = Icons.block;
    } else if (_biometricEnabled) {
      subtitle = 'Enabled - Use fingerprint/face unlock to sign in';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else {
      subtitle = 'Disabled - Tap to enable biometric authentication';
      statusColor = Colors.orange;
      statusIcon = Icons.radio_button_unchecked;
    }

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: statusColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.fingerprint, color: statusColor),
      ),
      title: const Text(
        'Biometric Authentication',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Text(subtitle, style: TextStyle(color: Colors.grey[600])),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(statusIcon, size: 16, color: statusColor),
              const SizedBox(width: 4),
              Text(
                _biometricAvailable
                    ? (_biometricEnabled ? 'Active' : 'Inactive')
                    : 'Unavailable',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                ),
              ),
            ],
          ),
        ],
      ),
      trailing: _biometricAvailable
          ? Switch(
              value: _biometricEnabled,
              onChanged: _toggleBiometric,
              activeColor: Colors.blue,
            )
          : Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'N/A',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  Widget _buildSettingTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
      trailing: trailing,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Future<void> _updateUserSettings({
    String? companyName,
    String? currencyCode,
  }) async {
    try {
      await settingsService.upsertSettings(
        companyName: companyName,
        currencyCode: currencyCode,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Settings updated successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error updating settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Error updating settings: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showCompanyNameLockedDialog() {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 6),
            Expanded(
              child: Text(
                'Company Name Locked',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company name settings are managed by your administrator for security and consistency purposes.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'To update your company name, please:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8),
            Text('• Contact your system administrator'),
            Text('• Provide your business registration details'),
            Text('• Allow 1-2 business days for verification'),
            SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.blue),
                SizedBox(width: 6),
                Expanded(
                  child: Text(
                    'This ensures accurate invoicing and legal compliance.',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                    softWrap: true,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Understood'),
          ),
        ],
      ),
    );
  }

  void _showCurrencyPicker() {
    showCurrencyPicker(
      context: context,
      onSelect: (Currency currency) async {
        setState(() {
          _selectedCurrency = currency;
        });

        // Update currency in local storage (for immediate app usage)
        await currencyService.saveSelectedCurrency(currency);

        // Update currency in database (for persistence across devices)
        await _updateUserSettings(currencyCode: currency.code);
      },
      favorite: _selectedCurrency != null ? [_selectedCurrency!.code] : ['KES'],
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
    );
  }

  void _showThemeSelector() {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('Select Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildThemeOption(
              ctx,
              'Light',
              'Use light theme',
              Icons.light_mode,
            ),
            _buildThemeOption(ctx, 'Dark', 'Use dark theme', Icons.dark_mode),
            _buildThemeOption(
              ctx,
              'System',
              'Follow system theme',
              Icons.settings_system_daydream,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext ctx,
    String value,
    String description,
    IconData icon,
  ) {
    return InkWell(
      onTap: () {
        _setTheme(value);
        Navigator.of(ctx).pop();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Icon(
              icon,
              color: _selectedTheme == value ? Colors.blue : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _selectedTheme == value
                          ? Colors.blue
                          : Colors.black87,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            if (_selectedTheme == value)
              const Icon(Icons.check_circle, color: Colors.blue, size: 20),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleBiometric(bool value) async {
    if (!_biometricAvailable) {
      _showBiometricUnavailableDialog();
      return;
    }

    try {
      if (value) {
        // Enabling biometric - authenticate first
        final bool didAuthenticate = await localAuth.authenticate(
          localizedReason: 'Please authenticate to enable biometric login',
          options: const AuthenticationOptions(
            biometricOnly: false,
            stickyAuth: true,
          ),
        );

        if (didAuthenticate) {
          // Get current user credentials to save for biometric
          final user = authService.currentUser;
          if (user?.email != null) {
            final saved = await authService.getSavedCredentials();
            String? passwordToStore = saved?['password'];
            if (passwordToStore == null || passwordToStore.isEmpty) {
              // Ask the user to confirm their password to complete setup
              passwordToStore = await _promptPasswordForBiometric(user!.email!);
            }

            if (passwordToStore != null && passwordToStore.isNotEmpty) {
              await authService.enableBiometricAuth(
                user!.email!,
                passwordToStore,
              );

              if (mounted) {
                setState(() {
                  _biometricEnabled = true;
                });

                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.white),
                        SizedBox(width: 8),
                        Text('Biometric authentication enabled successfully!'),
                      ],
                    ),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            } else {
              throw Exception('Password required to enable biometric login.');
            }
          } else {
            throw Exception('User not found. Please log in again.');
          }
        } else {
          // Authentication failed or was cancelled
          if (mounted) {
            if (!mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Authentication cancelled'),
                  ],
                ),
                backgroundColor: Colors.orange,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } else {
        // Disabling biometric
        await authService.disableBiometricAuth();

        if (mounted) {
          setState(() {
            _biometricEnabled = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.info, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Biometric authentication disabled'),
                ],
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } on PlatformException catch (e) {
      debugPrint('PlatformException: ${e.code} - ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'no_fragment_activity':
          errorMessage =
              'Device configuration issue. Please restart the app and try again.';
          break;
        case 'NotAvailable':
          errorMessage =
              'Biometric authentication is not available on this device.';
          break;
        case 'NotEnrolled':
          errorMessage =
              'No biometric credentials are enrolled. Please set up fingerprint or face unlock in device settings.';
          break;
        case 'LockedOut':
          errorMessage =
              'Too many failed attempts. Biometric authentication is temporarily locked.';
          break;
        case 'PermanentlyLockedOut':
          errorMessage =
              'Biometric authentication is permanently locked. Please unlock with PIN/Password first.';
          break;
        default:
          errorMessage =
              'Biometric authentication error: ${e.message ?? 'Unknown error'}';
      }

      if (mounted) {
        _showBiometricErrorDialog(errorMessage);
      }
    } catch (e) {
      debugPrint('General biometric error: $e');
      if (mounted) {
        _showBiometricErrorDialog(
          'An unexpected error occurred: ${e.toString()}',
        );
      }
    }
  }

  Future<String?> _promptPasswordForBiometric(String email) async {
    final controller = TextEditingController();
    String? password;
    if (!mounted) return null;
    await showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Confirm Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter your password to enable biometric login for this account.',
            ),
            const SizedBox(height: 12),
            TextField(
              controller: controller,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final pwd = controller.text;
              try {
                await authService.signIn(email, pwd);
                password = pwd;
                if (ctx.mounted) Navigator.of(ctx).pop();
              } catch (e) {
                if (!ctx.mounted) return;
                ScaffoldMessenger.of(ctx).showSnackBar(
                  SnackBar(
                    content: Text('Invalid password: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return password;
  }

  void _showBiometricUnavailableDialog() {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('Biometric Unavailable'),
          ],
        ),
        content: const Text(
          'Biometric authentication is not available on this device or no biometric credentials are enrolled.\n\n'
          'Please set up fingerprint or face unlock in your device settings first.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showBiometricErrorDialog(String message) {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Biometric Error'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'laundry Hub',
      applicationVersion: '1.0.102',
      applicationIcon: const Icon(
        Icons.local_laundry_service,
        size: 48,
        color: Colors.blue,
      ),
      children: [
        const Text('A professional laundry management application.'),
        const SizedBox(height: 10),
        const Text('First Release: 10/08/2025'),
        const SizedBox(height: 8),
        const Text('Developed with Flutter'),
        const SizedBox(height: 8),
        const Text('© 2025 laundry Hub. All rights reserved.'),
      ],
    );
  }
}
