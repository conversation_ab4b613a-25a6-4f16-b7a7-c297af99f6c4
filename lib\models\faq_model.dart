import 'package:flutter/material.dart';

class FAQ {
  final String id;
  final String question;
  final String answer;
  final String categoryId;
  final int order;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> tags;

  const FAQ({
    required this.id,
    required this.question,
    required this.answer,
    required this.categoryId,
    this.order = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.tags = const [],
  });

  factory FAQ.fromJson(Map<String, dynamic> json) {
    return FAQ(
      id: json['id'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      categoryId: json['category_id'] as String,
      order: json['order'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'answer': answer,
      'category_id': categoryId,
      'order': order,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'tags': tags,
    };
  }

  FAQ copyWith({
    String? id,
    String? question,
    String? answer,
    String? categoryId,
    int? order,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
  }) {
    return FAQ(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      categoryId: categoryId ?? this.categoryId,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FAQ && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FAQ(id: $id, question: $question, categoryId: $categoryId)';
  }
}

class FAQCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color? color;
  final int order;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<FAQ> faqs;

  const FAQCategory({
    required this.id,
    required this.name,
    this.description = '',
    this.icon = Icons.help_outline,
    this.color,
    this.order = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.faqs = const [],
  });

  factory FAQCategory.fromJson(Map<String, dynamic> json) {
    // Convert icon name to IconData
    IconData categoryIcon = Icons.help_outline;
    try {
      final iconName = json['icon'] as String?;
      if (iconName != null) {
        categoryIcon = _getIconFromName(iconName);
      }
    } catch (e) {
      // Use default icon if parsing fails
    }

    // Convert color
    Color? categoryColor;
    try {
      final colorValue = json['color'] as int?;
      if (colorValue != null) {
        categoryColor = Color(colorValue);
      }
    } catch (e) {
      // Use null color if parsing fails
    }

    // Parse FAQs if included
    List<FAQ> categoryFAQs = [];
    if (json['faqs'] is List) {
      categoryFAQs = (json['faqs'] as List)
          .map((faqJson) => FAQ.fromJson(faqJson as Map<String, dynamic>))
          .toList();
    }

    return FAQCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      icon: categoryIcon,
      color: categoryColor,
      order: json['order'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      faqs: categoryFAQs,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': _getIconName(icon),
      'color': color?.toARGB32(),
      'order': order,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'faqs': faqs.map((faq) => faq.toJson()).toList(),
    };
  }

  FAQCategory copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    int? order,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<FAQ>? faqs,
  }) {
    return FAQCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      faqs: faqs ?? this.faqs,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FAQCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FAQCategory(id: $id, name: $name, faqCount: ${faqs.length})';
  }

  // Helper methods for icon conversion
  static IconData _getIconFromName(String iconName) {
    switch (iconName) {
      case 'rocket_launch':
        return Icons.rocket_launch;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'account_circle':
        return Icons.account_circle;
      case 'help_outline':
        return Icons.help_outline;
      case 'local_laundry_service':
        return Icons.local_laundry_service;
      case 'payment':
        return Icons.payment;
      case 'delivery_dining':
        return Icons.delivery_dining;
      case 'settings':
        return Icons.settings;
      case 'support_agent':
        return Icons.support_agent;
      default:
        return Icons.help_outline;
    }
  }

  static String _getIconName(IconData icon) {
    if (icon == Icons.rocket_launch) return 'rocket_launch';
    if (icon == Icons.shopping_cart) return 'shopping_cart';
    if (icon == Icons.account_circle) return 'account_circle';
    if (icon == Icons.help_outline) return 'help_outline';
    if (icon == Icons.local_laundry_service) return 'local_laundry_service';
    if (icon == Icons.payment) return 'payment';
    if (icon == Icons.delivery_dining) return 'delivery_dining';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.support_agent) return 'support_agent';
    return 'help_outline';
  }
}
