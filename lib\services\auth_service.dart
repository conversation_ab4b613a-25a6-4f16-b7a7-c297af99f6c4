import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  final SupabaseClient _supabase;
  final LocalAuthentication _localAuth = LocalAuthentication();

  AuthService({required SupabaseClient supabase}) : _supabase = supabase;

  // Get current user
  User? get currentUser => _supabase.auth.currentUser;

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Sign out
  Future<void> signOut() async {
    await _supabase.auth.signOut();
  }

  // Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  // Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  // Authenticate with biometrics
  Future<bool> authenticateWithBiometrics({
    String localizedReason = 'Please authenticate to access the app',
  }) async {
    try {
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      return didAuthenticate;
    } catch (e) {
      return false;
    }
  }

  // Save biometric preference
  Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('biometric_enabled', enabled);
  }

  // Get biometric preference
  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('biometric_enabled') ?? false;
  }

  // Enable biometric authentication (save credentials securely)
  Future<void> enableBiometricAuth(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    // Save credentials for biometric login (consider encryption in production)
    await prefs.setString('biometric_email', email);
    await prefs.setString('biometric_password', password);
    await prefs.setBool('biometric_enabled', true);
  }

  // Disable biometric authentication (clear saved credentials)
  Future<void> disableBiometricAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('biometric_email');
    await prefs.remove('biometric_password');
    await prefs.setBool('biometric_enabled', false);
  }

  // Get saved credentials for biometric login
  Future<Map<String, String>?> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString('biometric_email');
    final password = prefs.getString('biometric_password');
    
    if (email != null && password != null) {
      return {'email': email, 'password': password};
    }
    return null;
  }

  // Sign in with email and password
  Future<AuthResponse> signIn(String email, String password) async {
    return await _supabase.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  // Update user profile
  Future<void> updateProfile({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  }) async {
    final updates = <String, dynamic>{};
    
    if (email != null) updates['email'] = email;
    if (password != null) updates['password'] = password;
    if (data != null) updates['data'] = data;

    if (updates.isNotEmpty) {
      await _supabase.auth.updateUser(UserAttributes(
        email: email,
        password: password,
        data: data,
      ));
    }
  }

  // Delete user account
  Future<void> deleteAccount() async {
    // Implementation depends on your backend setup
    // This is a placeholder - you'll need to implement based on your requirements
    throw UnimplementedError('Account deletion not implemented');
  }

  // Get user metadata
  Map<String, dynamic>? get userMetadata => currentUser?.userMetadata;

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;
}
