import 'package:flutter/material.dart';
import '../../models/order_model.dart';
import '../../models/garment_model.dart';
import '../../models/country_model.dart';
import '../../services/order_service.dart';
import '../../widgets/country_picker_dialog.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CustomerEditOrderScreen extends StatefulWidget {
  final Order order;

  const CustomerEditOrderScreen({super.key, required this.order});

  @override
  State<CustomerEditOrderScreen> createState() =>
      _CustomerEditOrderScreenState();
}

class _CustomerEditOrderScreenState extends State<CustomerEditOrderScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final OrderService _orderService = OrderService(
    supabase: Supabase.instance.client,
  );

  late TabController _tabController;
  late TextEditingController _specialInstructionsController;
  late DateTime? _pickupDate;
  late String? _pickupTimeSlot;
  late DateTime? _deliveryDate;
  late String? _deliveryTimeSlot;

  // Address management
  final _addressTitleController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  Country? _selectedCountry;

  // Additional order editing fields
  late List<OrderItem> _orderItems;
  late double _discountAmount;
  late bool _payOnDelivery;
  final _discountController = TextEditingController();

  bool _isLoading = false;
  bool _editingAddress = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    _specialInstructionsController = TextEditingController(
      text: widget.order.specialInstructions ?? '',
    );
    _pickupDate = widget.order.pickupDate;
    _pickupTimeSlot = widget.order.pickupTimeSlot;
    _deliveryDate = widget.order.deliveryDate;
    _deliveryTimeSlot = widget.order.deliveryTimeSlot;

    // Initialize additional fields
    _orderItems = List.from(widget.order.items);
    _discountAmount = widget.order.discountAmount;
    _payOnDelivery = widget.order.payOnDelivery;
    _discountController.text = _discountAmount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _specialInstructionsController.dispose();
    _addressTitleController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  Future<void> _updateOrder() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Parse discount amount
      final discountValue = double.tryParse(_discountController.text) ?? 0.0;

      // Recalculate totals
      final subtotal = _orderItems.fold(
        0.0,
        (sum, item) => sum + item.totalPrice,
      );
      final taxAmount = subtotal * 0.1; // 10% tax
      final deliveryFee = _deliveryDate != null ? 5.0 : 0.0;
      final totalAmount = subtotal + taxAmount + deliveryFee - discountValue;

      // Create updated order data
      final updateData = {
        'pickup_date': _pickupDate?.toIso8601String().split('T')[0],
        'pickup_time_slot': _pickupTimeSlot,
        'delivery_date': _deliveryDate?.toIso8601String().split('T')[0],
        'delivery_time_slot': _deliveryTimeSlot,
        'special_instructions':
            _specialInstructionsController.text.trim().isEmpty
            ? null
            : _specialInstructionsController.text.trim(),
        'discount_amount': discountValue,
        'pay_on_delivery': _payOnDelivery,
        'subtotal': subtotal,
        'tax_amount': taxAmount,
        'delivery_fee': deliveryFee,
        'total_amount': totalAmount,
      };

      final updatedOrder = await _orderService.updateOrder(
        widget.order.id,
        updateData,
      );

      if (mounted) {
        Navigator.of(context).pop(updatedOrder);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Order updated successfully'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Failed to update order: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showAddressForm() {
    setState(() {
      _editingAddress = true;
      _addressTitleController.text = 'Home';
    });
  }

  Future<void> _saveAddress() async {
    if (_selectedCountry == null ||
        _streetController.text.trim().isEmpty ||
        _cityController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required address fields'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Address saved successfully'),
        backgroundColor: Colors.green,
      ),
    );

    setState(() {
      _editingAddress = false;
      _addressTitleController.clear();
      _streetController.clear();
      _cityController.clear();
      _stateController.clear();
      _postalCodeController.clear();
      _selectedCountry = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.indigo.shade800,
              Colors.blue.shade700,
              Colors.purple.shade600,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Edit Order #${widget.order.orderNumber}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    if (_isLoading)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                  ],
                ),
              ),

              // Tab Bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicatorColor: Colors.white,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  indicatorWeight: 3,
                  tabs: const [
                    Tab(icon: Icon(Icons.schedule), text: 'Schedule'),
                    Tab(icon: Icon(Icons.edit), text: 'Details'),
                    Tab(icon: Icon(Icons.home), text: 'Address'),
                  ],
                ),
              ),

              // Tab Content
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Form(
                      key: _formKey,
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildScheduleTab(),
                          _buildDetailsTab(),
                          _buildAddressTab(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Action Buttons
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updateOrder,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.indigo.shade700,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.indigo.shade700,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text(
                                'Update Order',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pickup Schedule Card
          _buildSectionCard(
            title: 'Pickup Schedule',
            icon: Icons.schedule,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildDatePickerField(
                        label: 'Pickup Date',
                        date: _pickupDate,
                        onDateSelected: (date) {
                          setState(() => _pickupDate = date);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTimeSlotDropdown(
                        label: 'Time Slot',
                        value: _pickupTimeSlot,
                        onChanged: (value) {
                          setState(() => _pickupTimeSlot = value);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Delivery Schedule Card (if applicable)
          if (widget.order.deliveryDate != null) ...[
            _buildSectionCard(
              title: 'Delivery Schedule',
              icon: Icons.local_shipping,
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDatePickerField(
                          label: 'Delivery Date',
                          date: _deliveryDate,
                          onDateSelected: (date) {
                            setState(() => _deliveryDate = date);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTimeSlotDropdown(
                          label: 'Time Slot',
                          value: _deliveryTimeSlot,
                          onChanged: (value) {
                            setState(() => _deliveryTimeSlot = value);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
          ],

          // Schedule Information Card
          _buildInfoCard(),

          const SizedBox(height: 20),

          // Current Order Details Card
          _buildOrderInfoCard(),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Items Card
          _buildSectionCard(
            title: 'Order Items',
            icon: Icons.shopping_bag,
            child: _buildOrderItemsList(),
          ),

          const SizedBox(height: 20),

          // Special Instructions Card
          _buildSectionCard(
            title: 'Special Instructions',
            icon: Icons.note_add,
            child: TextFormField(
              controller: _specialInstructionsController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Enter any special instructions...',
                helperText: 'Additional notes for this order',
              ),
              maxLines: 3,
            ),
          ),

          const SizedBox(height: 20),

          // Pricing Adjustments Card
          _buildSectionCard(
            title: 'Pricing Adjustments',
            icon: Icons.monetization_on,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _discountController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          labelText: 'Discount Amount',
                          prefixText: '\$',
                          hintText: '0.00',
                        ),
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final amount = double.tryParse(value!);
                            if (amount == null || amount < 0) {
                              return 'Enter valid amount';
                            }
                          }
                          return null;
                        },
                        onChanged: (value) {
                          setState(() {}); // Trigger rebuild
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'New Total',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.green.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _calculateNewTotal(),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('Pay on Delivery'),
                  subtitle: const Text(
                    'Customer will pay when order is delivered',
                  ),
                  value: _payOnDelivery,
                  onChanged: (value) {
                    setState(() {
                      _payOnDelivery = value ?? false;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                  activeColor: Colors.indigo,
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Order Summary Card
          _buildOrderSummaryCard(),
        ],
      ),
    );
  }

  Widget _buildAddressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            title: 'Address Management',
            icon: Icons.home,
            child: Column(
              children: [
                if (!_editingAddress) ...[
                  ElevatedButton.icon(
                    onPressed: _showAddressForm,
                    icon: const Icon(Icons.add),
                    label: const Text('Add New Address'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 24,
                      ),
                    ),
                  ),
                ] else ...[
                  _buildAddressForm(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItemsList() {
    return Column(
      children: [
        // Header with Add Item button
        Row(
          children: [
            Expanded(
              child: Text(
                'Items (${_orderItems.length})',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: _showAddItemDialog,
              icon: const Icon(Icons.add, size: 18),
              label: const Text('Add Item'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        if (_orderItems.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.shopping_bag_outlined,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 8),
                Text(
                  'No items in this order',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(
                  'Tap "Add Item" to add products',
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
                ),
              ],
            ),
          ),
        ] else ...[
          // Items list - New Card Layout
          Column(
            children: List.generate(_orderItems.length, (index) {
              final item = _orderItems[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade100,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Top row: Item name and Total price
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.garment.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                ),
                                if (item.garment.description != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    item.garment.description!,
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),
                          // Total price (prominent)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.indigo.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.indigo.shade200),
                            ),
                            child: Text(
                              '\$${item.totalPrice.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.indigo.shade700,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Delete button
                          IconButton(
                            onPressed: () => _removeItem(index),
                            icon: Icon(
                              Icons.delete_outline,
                              color: Colors.red.shade400,
                              size: 20,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                            tooltip: 'Remove item',
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Bottom row: Quantity and Unit Price
                      Row(
                        children: [
                          // Quantity (editable)
                          Expanded(
                            child: GestureDetector(
                              onTap: () => _showEditQuantityDialog(index),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.blue.shade200,
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.edit_outlined,
                                      size: 16,
                                      color: Colors.blue.shade600,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Qty: ${item.quantity}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          // Unit price
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.attach_money,
                                    size: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '\$${item.unitPrice.toStringAsFixed(2)} each',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          // Summary
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.indigo.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Total Items: ${_orderItems.length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo.shade700,
                    ),
                  ),
                ),
                Text(
                  'Subtotal: \$${_orderItems.fold(0.0, (sum, item) => sum + item.totalPrice).toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 1,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.indigo.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  'Schedule Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• Orders can be rescheduled up to 24 hours before pickup',
              style: TextStyle(color: Colors.grey.shade700),
            ),
            const SizedBox(height: 4),
            Text(
              '• Delivery dates are automatically set 2-3 days after pickup',
              style: TextStyle(color: Colors.grey.shade700),
            ),
            const SizedBox(height: 4),
            Text(
              '• Service time may vary based on order complexity',
              style: TextStyle(color: Colors.grey.shade700),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderInfoCard() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  'Current Order Details',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Order Number:', '#${widget.order.orderNumber}'),
            _buildDetailRow('Status:', widget.order.status.displayName),
            _buildDetailRow('Items:', '${widget.order.items.length} items'),
            _buildDetailRow(
              'Total Amount:',
              '\$${widget.order.totalAmount.toStringAsFixed(2)}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.indigo.shade50, Colors.blue.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: Colors.indigo.shade700),
                const SizedBox(width: 8),
                Text(
                  'Updated Order Summary',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Items:', '${_orderItems.length} items'),
            _buildSummaryRow(
              'Subtotal:',
              '\$${_orderItems.fold(0.0, (sum, item) => sum + item.totalPrice).toStringAsFixed(2)}',
            ),
            _buildSummaryRow(
              'Tax (10%):',
              '\$${(_orderItems.fold(0.0, (sum, item) => sum + item.totalPrice) * 0.1).toStringAsFixed(2)}',
            ),
            if (_deliveryDate != null)
              _buildSummaryRow('Delivery Fee:', '\$5.00'),
            if ((double.tryParse(_discountController.text) ?? 0.0) > 0)
              _buildSummaryRow(
                'Discount:',
                '-\$${(double.tryParse(_discountController.text) ?? 0.0).toStringAsFixed(2)}',
                isDiscount: true,
              ),
            const Divider(),
            _buildSummaryRow('Total:', _calculateNewTotal(), isTotal: true),
            if (_payOnDelivery)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Payment on delivery',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    bool isTotal = false,
    bool isDiscount = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.indigo.shade700 : Colors.grey.shade700,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal
                  ? Colors.indigo.shade700
                  : isDiscount
                  ? Colors.red.shade600
                  : Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressForm() {
    return Column(
      children: [
        TextFormField(
          controller: _addressTitleController,
          decoration: const InputDecoration(
            labelText: 'Address Title',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 12),
        InkWell(
          onTap: () async {
            await showCountryPicker(
              context: context,
              selectedCountry: _selectedCountry,
              onSelect: (country) => setState(() => _selectedCountry = country),
            );
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.flag, color: Colors.grey),
                const SizedBox(width: 12),
                Expanded(
                  child: _selectedCountry != null
                      ? Row(
                          children: [
                            Text(
                              _selectedCountry!.flagEmoji ?? '🌐',
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(_selectedCountry!.name)),
                          ],
                        )
                      : const Text(
                          'Select Country',
                          style: TextStyle(color: Colors.grey),
                        ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _streetController,
          decoration: const InputDecoration(
            labelText: 'Street Address',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _cityController,
                decoration: const InputDecoration(
                  labelText: 'City',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _stateController,
                decoration: const InputDecoration(
                  labelText: 'State (Optional)',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _postalCodeController,
          decoration: const InputDecoration(
            labelText: 'Postal Code (Optional)',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            ElevatedButton(
              onPressed: _saveAddress,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Save Address'),
            ),
            const SizedBox(width: 12),
            TextButton(
              onPressed: () {
                setState(() {
                  _editingAddress = false;
                  _addressTitleController.clear();
                  _streetController.clear();
                  _cityController.clear();
                  _stateController.clear();
                  _postalCodeController.clear();
                  _selectedCountry = null;
                });
              },
              child: const Text('Cancel'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildDatePickerField({
    required String label,
    required DateTime? date,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime.now(),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade50,
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: Colors.indigo.shade600),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    date != null
                        ? '${date.day}/${date.month}/${date.year}'
                        : 'Select $label',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotDropdown({
    required String label,
    required String? value,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        labelText: label,
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      items: ['09:00-12:00', '12:00-15:00', '15:00-18:00', '18:00-21:00']
          .map((slot) => DropdownMenuItem(value: slot, child: Text(slot)))
          .toList(),
      onChanged: onChanged,
    );
  }

  String _calculateNewTotal() {
    final subtotal = _orderItems.fold(
      0.0,
      (sum, item) => sum + item.totalPrice,
    );
    final taxAmount = subtotal * 0.1;
    final deliveryFee = _deliveryDate != null ? 5.0 : 0.0;
    final discountValue = double.tryParse(_discountController.text) ?? 0.0;
    final total = subtotal + taxAmount + deliveryFee - discountValue;
    return '\$${total.toStringAsFixed(2)}';
  }

  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _AddItemDialog(
          onItemAdded: (garment, quantity, unitPrice) {
            setState(() {
              _orderItems.add(
                OrderItem(
                  garmentId: garment.id,
                  garment: garment,
                  quantity: quantity,
                  unitPrice: unitPrice,
                ),
              );
            });
          },
        );
      },
    );
  }

  void _showEditQuantityDialog(int index) {
    final item = _orderItems[index];
    final quantityController = TextEditingController(
      text: item.quantity.toString(),
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Edit Quantity'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.garment.name,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Quantity',
                  border: OutlineInputBorder(),
                  helperText: 'Enter quantity (minimum 1)',
                ),
                validator: (value) {
                  final quantity = int.tryParse(value ?? '');
                  if (quantity == null || quantity < 1) {
                    return 'Please enter a valid quantity';
                  }
                  return null;
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final quantity = int.tryParse(quantityController.text);
                if (quantity != null && quantity > 0) {
                  setState(() {
                    _orderItems[index] = OrderItem(
                      garmentId: item.garmentId,
                      garment: item.garment,
                      quantity: quantity,
                      unitPrice: item.unitPrice,
                    );
                  });
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  void _removeItem(int index) {
    final item = _orderItems[index];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove Item'),
          content: Text(
            'Are you sure you want to remove "${item.garment.name}" from your order?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _orderItems.removeAt(index);
                });
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${item.garment.name} removed from order'),
                    backgroundColor: Colors.orange,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }
}

class _AddItemDialog extends StatefulWidget {
  final Function(Garment, int, double) onItemAdded;

  const _AddItemDialog({required this.onItemAdded});

  @override
  State<_AddItemDialog> createState() => _AddItemDialogState();
}

class _AddItemDialogState extends State<_AddItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _garmentNameController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  final _priceController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Sample garment types for demo
  final List<Map<String, dynamic>> _garmentTypes = [
    {'name': 'Shirt', 'price': 8.99},
    {'name': 'Pants', 'price': 12.99},
    {'name': 'Dress', 'price': 15.99},
    {'name': 'Jacket', 'price': 18.99},
    {'name': 'Suit', 'price': 25.99},
    {'name': 'Blouse', 'price': 10.99},
    {'name': 'Skirt', 'price': 9.99},
    {'name': 'T-Shirt', 'price': 6.99},
    {'name': 'Jeans', 'price': 14.99},
    {'name': 'Sweater', 'price': 16.99},
  ];

  String? _selectedGarmentType;

  @override
  void dispose() {
    _garmentNameController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _onGarmentTypeSelected(String? garmentType) {
    if (garmentType != null) {
      final garmentData = _garmentTypes.firstWhere(
        (g) => g['name'] == garmentType,
        orElse: () => {'name': garmentType, 'price': 0.0},
      );
      setState(() {
        _selectedGarmentType = garmentType;
        _garmentNameController.text = garmentType;
        _priceController.text = garmentData['price'].toString();
      });
    }
  }

  void _addItem() {
    if (!_formKey.currentState!.validate()) return;

    final quantity = int.parse(_quantityController.text);
    final unitPrice = double.parse(_priceController.text);

    final garment = Garment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _garmentNameController.text.trim(),
      category: 'custom',
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      garmentType: 'custom',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    widget.onItemAdded(garment, quantity, unitPrice);
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${garment.name} added to order'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Add Item to Order',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Garment Type Dropdown
              DropdownButtonFormField<String>(
                value: _selectedGarmentType,
                decoration: const InputDecoration(
                  labelText: 'Garment Type',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.checkroom),
                ),
                items: _garmentTypes
                    .map(
                      (garment) => DropdownMenuItem<String>(
                        value: garment['name'],
                        child: Row(
                          children: [
                            Text(garment['name']),
                            const Spacer(),
                            Text(
                              '\$${garment['price']}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
                onChanged: _onGarmentTypeSelected,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a garment type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Custom Name Field
              TextFormField(
                controller: _garmentNameController,
                decoration: const InputDecoration(
                  labelText: 'Item Name',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.edit),
                  helperText: 'You can customize the item name',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an item name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description Field
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                  helperText: 'Additional notes about this item',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // Quantity and Price Row
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _quantityController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Quantity',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.numbers),
                      ),
                      validator: (value) {
                        final quantity = int.tryParse(value ?? '');
                        if (quantity == null || quantity < 1) {
                          return 'Min qty: 1';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      decoration: const InputDecoration(
                        labelText: 'Unit Price',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                        prefixText: '\$',
                      ),
                      validator: (value) {
                        final price = double.tryParse(value ?? '');
                        if (price == null || price <= 0) {
                          return 'Enter valid price';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Total Preview
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Total for this item: \$${_calculateItemTotal()}',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _addItem,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.indigo.shade600,
            foregroundColor: Colors.white,
          ),
          child: const Text('Add Item'),
        ),
      ],
    );
  }

  String _calculateItemTotal() {
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    final price = double.tryParse(_priceController.text) ?? 0.0;
    final total = quantity * price;
    return total.toStringAsFixed(2);
  }
}
