import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/staff_auth_provider.dart';
import '../screens/profile_screen.dart';
import '../screens/orders/orders_screen.dart';
import '../screens/orders/create_order_screen.dart';
import '../screens/orders/active_orders_screen.dart';
import '../screens/orders/customer_orders_screen.dart';
import '../screens/orders/customer_create_order_screen.dart';
import '../screens/customers/customers_screen.dart';
import '../screens/reports/reports_screen.dart';
import '../screens/payments/payment_dashboard.dart';
import '../screens/settings_page.dart';
import '../screens/customer_addresses_screen.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, StaffAuthProvider>(
      builder: (context, authProvider, staffAuthProvider, child) {
        final user = authProvider.user;
        final isStaff = staffAuthProvider.userType == UserType.staff;
        final staffProfile = staffAuthProvider.staffProfile;

        return Drawer(
          child: Column(
            children: [
              // Header
              UserAccountsDrawerHeader(
                decoration: BoxDecoration(color: Colors.blue),
                accountName: Text(
                  isStaff && staffProfile != null
                      ? staffProfile['full_name'] ?? 'Staff Member'
                      : user?.email?.split('@')[0] ?? 'User',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                accountEmail: Text(user?.email ?? ''),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: Colors.white,
                  child: Icon(
                    isStaff ? Icons.badge : Icons.person,
                    color: Colors.blue,
                    size: 40,
                  ),
                ),
                otherAccountsPictures: [
                  if (isStaff)
                    Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.verified,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                ],
              ),

              // Navigation Items
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    // All Orders (renamed from Orders)
                    ListTile(
                      leading: const Icon(Icons.list_alt),
                      title: const Text('All Orders'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const OrderHistoryPage(),
                          ),
                        );
                      },
                    ),

                    // Create Order
                    if (isStaff)
                      ListTile(
                        leading: const Icon(Icons.add_business),
                        title: const Text('Create Order'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CreateOrderScreen(),
                            ),
                          );
                        },
                      ),

                    // Active Orders
                    ListTile(
                      leading: const Icon(Icons.pending_actions),
                      title: const Text('Active Orders'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ActiveOrdersScreen(),
                          ),
                        );
                      },
                    ),

                    // Customers (staff only)
                    if (isStaff)
                      ListTile(
                        leading: const Icon(Icons.people),
                        title: const Text('Customers'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CustomersScreen(),
                            ),
                          );
                        },
                      ),

                    // Payment Dashboard (staff only)
                    if (isStaff)
                      ListTile(
                        leading: const Icon(Icons.payment),
                        title: const Text('Payments'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const PaymentDashboard(),
                            ),
                          );
                        },
                      ),

                    // Customer section (customer only)
                    if (!isStaff) ...[
                      ListTile(
                        leading: const Icon(Icons.add_circle_outline),
                        title: const Text('New Order'),
                        subtitle: const Text('Book a laundry service'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const CustomerCreateOrderScreen(),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.track_changes),
                        title: const Text('Track Orders'),
                        subtitle: const Text('View order status'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const CustomerOrdersScreen(),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.history),
                        title: const Text('Order History'),
                        subtitle: const Text('View past orders'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const CustomerOrdersScreen(),
                            ),
                          );
                        },
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.location_on),
                        title: const Text('My Addresses'),
                        subtitle: const Text('Manage delivery addresses'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const CustomerAddressesScreen(),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.account_balance_wallet),
                        title: const Text('Payment Methods'),
                        subtitle: const Text('Manage payment options'),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Payment methods coming soon!'),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.local_offer),
                        title: const Text('My Offers'),
                        subtitle: const Text('View special discounts'),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Offers and discounts coming soon!',
                              ),
                            ),
                          );
                        },
                      ),
                    ],

                    // Reports
                    if (isStaff)
                      ListTile(
                        leading: const Icon(Icons.analytics),
                        title: const Text('Reports'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ReportsScreen(),
                            ),
                          );
                        },
                      ),

                    const Divider(),

                    // Settings
                    ListTile(
                      leading: const Icon(Icons.settings),
                      title: const Text('Settings'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettingsPage(),
                          ),
                        );
                      },
                    ),

                    // Profile
                    ListTile(
                      leading: const Icon(Icons.person),
                      title: const Text('Profile'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ProfileScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              // Logout
              Container(
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Colors.grey, width: 0.5),
                  ),
                ),
                child: ListTile(
                  leading: const Icon(Icons.logout, color: Colors.red),
                  title: const Text(
                    'Logout',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    await authProvider.signOut();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
