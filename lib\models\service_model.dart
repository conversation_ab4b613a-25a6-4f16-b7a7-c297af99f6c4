class Service {
  final String id;
  final String name;
  final String? description;
  final double basePrice;
  final double? pricePerKg;
  final double? pricePerItem;
  final String pricingType; // 'per_kg', 'per_item', 'fixed'
  final int estimatedHours;
  final bool isActive;
  final String? icon;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Enhanced service categorization
  final String serviceType; // 'wash_fold', 'dry_cleaning', 'ironing', 'express', 'delicate'
  final String category; // 'basic', 'premium', 'luxury'
  final List<String> suitableGarments; // List of garment categories this service is suitable for
  final String? specialInstructions;
  final bool requiresSpecialCare;
  final int minProcessingTime; // in hours
  final int maxProcessingTime; // in hours

  Service({
    required this.id,
    required this.name,
    this.description,
    required this.basePrice,
    this.pricePerKg,
    this.pricePerItem,
    required this.pricingType,
    this.estimatedHours = 24,
    this.isActive = true,
    this.icon,
    required this.createdAt,
    required this.updatedAt,
    required this.serviceType,
    this.category = 'basic',
    this.suitableGarments = const ['clothing'],
    this.specialInstructions,
    this.requiresSpecialCare = false,
    this.minProcessingTime = 24,
    this.maxProcessingTime = 72,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      basePrice: (json['base_price'] as num).toDouble(),
      pricePerKg: json['price_per_kg'] != null ? (json['price_per_kg'] as num).toDouble() : null,
      pricePerItem: json['price_per_item'] != null ? (json['price_per_item'] as num).toDouble() : null,
      pricingType: json['pricing_type'] as String,
      estimatedHours: json['estimated_hours'] as int? ?? 24,
      isActive: json['is_active'] as bool? ?? true,
      icon: json['icon'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      serviceType: json['service_type'] as String? ?? 'wash_fold',
      category: json['category'] as String? ?? 'basic',
      suitableGarments: json['suitable_garments'] != null 
          ? List<String>.from(json['suitable_garments'])
          : ['clothing'],
      specialInstructions: json['special_instructions'] as String?,
      requiresSpecialCare: json['requires_special_care'] as bool? ?? false,
      minProcessingTime: json['min_processing_time'] as int? ?? 24,
      maxProcessingTime: json['max_processing_time'] as int? ?? 72,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'base_price': basePrice,
      'price_per_kg': pricePerKg,
      'price_per_item': pricePerItem,
      'pricing_type': pricingType,
      'estimated_hours': estimatedHours,
      'is_active': isActive,
      'icon': icon,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'service_type': serviceType,
      'category': category,
      'suitable_garments': suitableGarments,
      'special_instructions': specialInstructions,
      'requires_special_care': requiresSpecialCare,
      'min_processing_time': minProcessingTime,
      'max_processing_time': maxProcessingTime,
    };
  }

  String get estimatedTimeText {
    if (estimatedHours <= 24) {
      return '${estimatedHours}h';
    } else {
      final days = (estimatedHours / 24).ceil();
      return '${days}d';
    }
  }

  String get serviceTypeDisplayName {
    switch (serviceType) {
      case 'wash_fold':
        return 'Wash & Fold';
      case 'dry_cleaning':
        return 'Dry Cleaning';
      case 'ironing':
        return 'Ironing';
      case 'express':
        return 'Express Service';
      case 'delicate':
        return 'Delicate Care';
      default:
        return name;
    }
  }

  String get categoryDisplayName {
    switch (category) {
      case 'basic':
        return 'Basic';
      case 'premium':
        return 'Premium';
      case 'luxury':
        return 'Luxury';
      default:
        return category;
    }
  }

  String getPricingText() {
    switch (pricingType) {
      case 'per_kg':
        return '\$${pricePerKg?.toStringAsFixed(2) ?? '0.00'}/kg';
      case 'per_item':
        return '\$${pricePerItem?.toStringAsFixed(2) ?? '0.00'}/item';
      case 'fixed':
        return '\$${basePrice.toStringAsFixed(2)} fixed';
      default:
        return 'Custom pricing';
    }
  }

  double calculatePrice({double? weight, int? itemCount}) {
    switch (pricingType) {
      case 'per_kg':
        return basePrice + (pricePerKg ?? 0.0) * (weight ?? 1.0);
      case 'per_item':
        return basePrice + (pricePerItem ?? 0.0) * (itemCount ?? 1);
      case 'fixed':
        return basePrice;
      default:
        return basePrice;
    }
  }

  // Check if service is suitable for a specific garment category
  bool isSuitableForGarment(String garmentCategory) {
    return suitableGarments.contains(garmentCategory);
  }

  // Get processing time range
  String get processingTimeRange {
    if (minProcessingTime == maxProcessingTime) {
      return '$minProcessingTime h';
    }
    return '$minProcessingTime-$maxProcessingTime h';
  }

  // Check if service is express
  bool get isExpress => serviceType == 'express';

  // Check if service requires special care
  bool get needsSpecialCare => requiresSpecialCare || serviceType == 'delicate';
}

class ServiceSelection {
  final Service service;
  final double? customAmount;
  final String? customPricingMode; // 'fixed' or 'per_kg'
  final double? weight; // for per_kg services
  final int? itemCount; // for per_item services

  ServiceSelection({
    required this.service,
    this.customAmount,
    this.customPricingMode,
    this.weight,
    this.itemCount,
  });

  double get totalPrice {
    if (customAmount != null && customAmount! > 0) {
      if (customPricingMode == 'per_kg') {
        return customAmount! * (weight ?? 1.0);
      } else {
        return customAmount!;
      }
    }
    return service.calculatePrice(weight: weight, itemCount: itemCount);
  }

  String get priceText {
    if (customAmount != null && customAmount! > 0) {
      if (customPricingMode == 'per_kg') {
        return '\$${customAmount!.toStringAsFixed(2)}/kg (custom)';
      } else {
        return '\$${customAmount!.toStringAsFixed(2)} (custom)';
      }
    }
    return service.getPricingText();
  }

  ServiceSelection copyWith({
    Service? service,
    double? customAmount,
    String? customPricingMode,
    double? weight,
    int? itemCount,
  }) {
    return ServiceSelection(
      service: service ?? this.service,
      customAmount: customAmount ?? this.customAmount,
      customPricingMode: customPricingMode ?? this.customPricingMode,
      weight: weight ?? this.weight,
      itemCount: itemCount ?? this.itemCount,
    );
  }
}
