import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/order_model.dart';
import '../../models/payment_model.dart';
import '../../models/invoice_model.dart';
import '../../widgets/loading_overlay.dart';

class PaymentScreen extends StatefulWidget {
  final Order order;

  const PaymentScreen({
    super.key,
    required this.order,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _receiptController = TextEditingController();
  final _transactionCodeController = TextEditingController();
  final _channelTargetController = TextEditingController();
  final _payerPhoneController = TextEditingController();
  final _notesController = TextEditingController();

  PaymentMethod _selectedMethod = PaymentMethod.cash;
  bool _isLoading = false;
  bool _isFullPayment = true;

  @override
  void initState() {
    super.initState();
    // Default to outstanding amount for full payment
    _amountController.text = widget.order.outstandingAmount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _receiptController.dispose();
    _transactionCodeController.dispose();
    _channelTargetController.dispose();
    _payerPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Record Payment - ${widget.order.orderNumber}'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Summary Card
                _buildOrderSummaryCard(),
                const SizedBox(height: 24),

                // Payment Amount Section
                _buildPaymentAmountSection(),
                const SizedBox(height: 24),

                // Payment Method Section
                _buildPaymentMethodSection(),
                const SizedBox(height: 24),

                // Payment Details Section
                _buildPaymentDetailsSection(),
                const SizedBox(height: 24),

                // Notes Section
                _buildNotesSection(),
                const SizedBox(height: 32),

                // Action Buttons
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Order Summary',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Order Number', widget.order.orderNumber),
            _buildSummaryRow('Service', widget.order.service?.name ?? 'Unknown'),
            _buildSummaryRow('Customer', widget.order.customer?.fullName ?? 'Unknown'),
            const Divider(height: 24),
            _buildSummaryRow('Total Amount', '\$${widget.order.totalAmount.toStringAsFixed(2)}', isBold: true),
            _buildSummaryRow('Amount Paid', '\$${widget.order.amountPaid.toStringAsFixed(2)}'),
            _buildSummaryRow(
              'Outstanding',
              '\$${widget.order.outstandingAmount.toStringAsFixed(2)}',
              isBold: true,
              color: Colors.red[700],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getPaymentStatusColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getPaymentStatusColor()),
              ),
              child: Text(
                widget.order.paymentStatusDisplay,
                style: TextStyle(
                  color: _getPaymentStatusColor(),
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isBold = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color ?? Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentAmountSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.green[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Payment Amount',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: Text('Full Payment (\$${widget.order.outstandingAmount.toStringAsFixed(2)})'),
                    value: true,
                    groupValue: _isFullPayment,
                    onChanged: (value) {
                      setState(() {
                        _isFullPayment = value!;
                        if (_isFullPayment) {
                          _amountController.text = widget.order.outstandingAmount.toStringAsFixed(2);
                        }
                      });
                    },
                    activeColor: Colors.green,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Partial Payment'),
                    value: false,
                    groupValue: _isFullPayment,
                    onChanged: (value) {
                      setState(() {
                        _isFullPayment = value!;
                        if (!_isFullPayment) {
                          _amountController.clear();
                        }
                      });
                    },
                    activeColor: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              enabled: !_isFullPayment,
              decoration: InputDecoration(
                labelText: 'Payment Amount',
                prefixText: '\$',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.green[700]!, width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter payment amount';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter a valid amount';
                }
                if (amount > widget.order.outstandingAmount) {
                  return 'Amount cannot exceed outstanding balance';
                }
                return null;
              },
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}$')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Payment Method',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...PaymentMethod.values.map((method) => RadioListTile<PaymentMethod>(
              title: Text(method.displayName),
              value: method,
              groupValue: _selectedMethod,
              onChanged: (value) {
                setState(() {
                  _selectedMethod = value!;
                  // Clear method-specific fields when switching
                  _receiptController.clear();
                  _transactionCodeController.clear();
                  _channelTargetController.clear();
                  _payerPhoneController.clear();
                });
              },
              activeColor: Colors.blue,
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetailsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Payment Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Receipt Number (for all methods)
            TextFormField(
              controller: _receiptController,
              decoration: InputDecoration(
                labelText: 'Receipt Number (Optional)',
                hintText: 'Internal receipt reference',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
            const SizedBox(height: 16),

            // M-Pesa specific fields
            if (_selectedMethod != PaymentMethod.cash) ...[
              TextFormField(
                controller: _transactionCodeController,
                decoration: InputDecoration(
                  labelText: 'M-Pesa Transaction Code *',
                  hintText: 'e.g., QA12B3C4D5',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                validator: _selectedMethod != PaymentMethod.cash ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Transaction code is required for M-Pesa payments';
                  }
                  return null;
                } : null,
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _channelTargetController,
                decoration: InputDecoration(
                  labelText: _getChannelTargetLabel(),
                  hintText: _getChannelTargetHint(),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _payerPhoneController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'Payer Phone Number (Optional)',
                  hintText: '+254700000000',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note, color: Colors.purple[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Additional Notes',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Any additional information about this payment...',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _processPayment,
            icon: const Icon(Icons.check_circle, color: Colors.white),
            label: Text(
              _isLoading ? 'Processing...' : 'Record Payment',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[700],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            icon: const Icon(Icons.cancel),
            label: const Text('Cancel'),
            style: OutlinedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getChannelTargetLabel() {
    switch (_selectedMethod) {
      case PaymentMethod.mpesaTill:
        return 'Till Number';
      case PaymentMethod.mpesaPaybill:
        return 'Paybill Number';
      case PaymentMethod.mpesaSendMoney:
        return 'Recipient Phone';
      default:
        return 'Channel Target';
    }
  }

  String _getChannelTargetHint() {
    switch (_selectedMethod) {
      case PaymentMethod.mpesaTill:
        return 'e.g., 123456';
      case PaymentMethod.mpesaPaybill:
        return 'e.g., 400200';
      case PaymentMethod.mpesaSendMoney:
        return '+254700000000';
      default:
        return '';
    }
  }

  Color _getPaymentStatusColor() {
    switch (widget.order.paymentStatus) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.partial:
        return Colors.orange;
      case PaymentStatus.pending:
      default:
        return Colors.red;
    }
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      // TODO: Implement actual payment processing
      // This will be replaced with actual OrderService.recordPayment() call
      
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment of \$${amount.toStringAsFixed(2)} recorded successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to record payment: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
