import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../services/invoice_service.dart';
import 'invoice_preview_page.dart';

class InvoiceCreationPage extends StatefulWidget {
  final String? orderId; // Optional - if provided, will create invoice for specific order

  const InvoiceCreationPage({super.key, this.orderId});

  @override
  State<InvoiceCreationPage> createState() => _InvoiceCreationPageState();
}

class _InvoiceCreationPageState extends State<InvoiceCreationPage> {
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);

  bool _isLoading = true;
  bool _isCreating = false;
  String _currencySymbol = '';
  
  // Selected order
  Order? _selectedOrder;
  
  // Available orders for selection
  List<Order> _availableOrders = [];
  List<Order> _filteredOrders = [];
  
  // Search and filter
  final TextEditingController _searchController = TextEditingController();
  OrderStatus? _statusFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      // Load currency symbol
      _currencySymbol = await _orderService.getCurrencySymbol();
      
      if (widget.orderId != null) {
        // Load specific order
        _selectedOrder = await _orderService.getOrderById(widget.orderId!);
      } else {
        // Load all orders that don't have invoices yet
        final allOrders = await _orderService.getAllOrders();
        _availableOrders = [];
        
        for (final order in allOrders) {
          try {
            await _invoiceService.getInvoiceByOrderId(order.id);
            // Invoice exists, don't include in available orders
          } catch (e) {
            // No invoice exists, include in available orders
            _availableOrders.add(order);
          }
        }
        
        _filteredOrders = List.from(_availableOrders);
      }
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _filterOrders() {
    final query = _searchController.text.toLowerCase().trim();
    
    setState(() {
      _filteredOrders = _availableOrders.where((order) {
        final matchesSearch = query.isEmpty ||
            order.orderNumber.toLowerCase().contains(query) ||
            (order.customer?.fullName != null && order.customer!.fullName.toLowerCase().contains(query));
        
        final matchesStatus = _statusFilter == null || order.status == _statusFilter;
        
        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  Future<void> _createInvoice() async {
    if (_selectedOrder == null) return;
    
    setState(() => _isCreating = true);
    
    try {
      final invoice = await _invoiceService.getOrCreateInvoiceForOrder(_selectedOrder!.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate to invoice preview
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => InvoicePreviewPage(
              orderId: _selectedOrder!.id,
              invoiceId: invoice.id,
            ),
          ),
        );
      }
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isCreating = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Invoice'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        actions: [
          if (_selectedOrder != null)
            IconButton(
              onPressed: _isCreating ? null : _createInvoice,
              icon: _isCreating 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.check),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : widget.orderId != null
              ? _buildSpecificOrderView()
              : _buildOrderSelectionView(),
    );
  }

  Widget _buildSpecificOrderView() {
    if (_selectedOrder == null) {
      return const Center(
        child: Text('Order not found'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Create Invoice for Order',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildOrderCard(_selectedOrder!, isSelected: true),
          const SizedBox(height: 24),
          _buildInvoicePreview(),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isCreating ? null : _createInvoice,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isCreating
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Creating Invoice...'),
                      ],
                    )
                  : const Text(
                      'Create Invoice',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSelectionView() {
    return Column(
      children: [
        // Search and Filter Section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: const Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
          ),
          child: Column(
            children: [
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search by order number or customer name',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                onChanged: (_) => _filterOrders(),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<OrderStatus?>(
                      value: _statusFilter,
                      decoration: InputDecoration(
                        labelText: 'Filter by Status',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      items: [
                        const DropdownMenuItem<OrderStatus?>(
                          value: null,
                          child: Text('All Statuses'),
                        ),
                        ...OrderStatus.values.map((status) =>
                          DropdownMenuItem<OrderStatus?>(
                            value: status,
                            child: Text(status.displayName),
                          ),
                        ),
                      ],
                      onChanged: (status) {
                        setState(() => _statusFilter = status);
                        _filterOrders();
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  IconButton(
                    onPressed: () {
                      _searchController.clear();
                      setState(() => _statusFilter = null);
                      _filterOrders();
                    },
                    icon: const Icon(Icons.clear),
                    tooltip: 'Clear filters',
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Results Section
        Expanded(
          child: _filteredOrders.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No orders available for invoice creation',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Orders must not have existing invoices',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredOrders.length,
                  itemBuilder: (context, index) {
                    final order = _filteredOrders[index];
                    return _buildOrderCard(
                      order,
                      isSelected: _selectedOrder?.id == order.id,
                      onTap: () => setState(() => _selectedOrder = order),
                    );
                  },
                ),
        ),
        
        // Create Button
        if (_selectedOrder != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey, width: 0.5)),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCreating ? null : _createInvoice,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isCreating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('Creating Invoice...'),
                        ],
                      )
                    : const Text(
                        'Create Invoice for Selected Order',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildOrderCard(Order order, {bool isSelected = false, VoidCallback? onTap}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? Colors.blue.shade600 : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.orderNumber,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (order.customer?.fullName != null)
                          Text(
                            order.customer!.fullName,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '$_currencySymbol${order.totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
          color: _getStatusColor(order.status).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
            color: _getStatusColor(order.status).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          order.status.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getStatusColor(order.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Created: ${order.createdAt.toString().substring(0, 10)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Colors.blue.shade600,
                      size: 20,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoicePreview() {
    if (_selectedOrder == null) return const SizedBox();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Preview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPreviewRow('Order Number:', _selectedOrder!.orderNumber),
            _buildPreviewRow('Customer:', _selectedOrder!.customer?.fullName ?? 'N/A'),
            _buildPreviewRow('Service:', _selectedOrder!.service?.name ?? 'N/A'),
            _buildPreviewRow('Total Amount:', '$_currencySymbol${_selectedOrder!.totalAmount.toStringAsFixed(2)}'),
            _buildPreviewRow('Status:', _selectedOrder!.status.displayName),
            _buildPreviewRow('Invoice Date:', DateTime.now().toString().substring(0, 10)),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.purple;
      case OrderStatus.inProcess:
        return Colors.indigo;
      case OrderStatus.readyForPickup:
        return Colors.teal;
      case OrderStatus.outForDelivery:
        return Colors.cyan;
      case OrderStatus.completed:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }
}
