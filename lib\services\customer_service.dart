import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/customer_model.dart';
import 'supabase_service.dart';

class CustomerService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Search customers by name, email, or phone
  static Future<List<Customer>> searchCustomers(String query) async {
    try {
      if (query.isEmpty) {
        // Return all active customers if no search query
        return await getAllCustomers();
      }

      final response = await _supabase
          .from('customers')
          .select()
          .eq('is_active', true)
          .or('full_name.ilike.%$query%,email.ilike.%$query%,phone.ilike.%$query%')
          .order('full_name');

      return (response as List)
          .map((json) => Customer.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to search customers: $e');
    }
  }

  // Get all active customers
  static Future<List<Customer>> getAllCustomers() async {
    try {
      final response = await _supabase
          .from('customers')
          .select()
          .eq('is_active', true)
          .order('full_name');

      return (response as List)
          .map((json) => Customer.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get customers: $e');
    }
  }

  // Get customer by ID
  static Future<Customer?> getCustomerById(String id) async {
    try {
      final response = await _supabase
          .from('customers')
          .select()
          .eq('id', id)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get customer: $e');
    }
  }

  // Get current authenticated user's customer profile
  static Future<Customer?> getCurrentUserCustomer() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return null;

      final response = await _supabase
          .from('customers')
          .select()
          .eq('id', currentUser.id)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get current user customer: $e');
    }
  }

  // Create new customer with enhanced fields
  static Future<Customer> createCustomer({
    required String fullName,
    String? email,
    String? phone,
    String? country,
    String? notes,
    String? profilePicture,
    String? dateOfBirth,
    String? gender,
    String? preferredLanguage,
    String theme = 'system',
    bool emailNotifications = true,
    bool smsNotifications = false,
    bool pushNotifications = true,
    String preferredCurrency = 'USD',
    String? timezone,
    String? preferredDetergent,
    String? preferredFabricSoftener,
    bool ecoFriendly = false,
    String? specialInstructions,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User must be authenticated to create a customer');
      }

      // Check if email is already taken (if provided)
      if (email != null && email.isNotEmpty) {
        final emailExists = await isEmailTaken(email);
        if (emailExists) {
          throw Exception('Email is already registered');
        }
      }

      // Check if current user already has a customer profile
      final existingCustomer = await _supabase
          .from('customers')
          .select()
          .eq('id', currentUser.id)
          .maybeSingle();

      if (existingCustomer != null) {
        throw Exception('Customer profile already exists for this user');
      }

      final response = await _supabase
          .from('customers')
          .insert({
            'id': currentUser.id, // Use auth user ID as customer ID
            'full_name': fullName,
            'email': email ?? currentUser.email, // Use auth email if not provided
            'phone': phone,
            'country': country,
            'notes': notes,
            'profile_picture': profilePicture,
            'date_of_birth': dateOfBirth,
            'gender': gender,
            'preferred_language': preferredLanguage,
            'theme': theme,
            'email_notifications': emailNotifications,
            'sms_notifications': smsNotifications,
            'push_notifications': pushNotifications,
            'preferred_currency': preferredCurrency,
            'timezone': timezone,
            'preferred_detergent': preferredDetergent,
            'preferred_fabric_softener': preferredFabricSoftener,
            'eco_friendly': ecoFriendly,
            'special_instructions': specialInstructions,
            'created_by': currentUser.id,
          })
          .select()
          .single();

      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  // Create new customer with authentication (for staff use)
  static Future<Map<String, dynamic>> createCustomerWithAuth({
    required String fullName,
    required String email,
    String? phone,
    String? country,
    String? notes,
    String? profilePicture,
    String? dateOfBirth,
    String? gender,
    String? preferredLanguage,
    String theme = 'system',
    bool emailNotifications = true,
    bool smsNotifications = false,
    bool pushNotifications = true,
    String preferredCurrency = 'USD',
    String? timezone,
    String? preferredDetergent,
    String? preferredFabricSoftener,
    bool ecoFriendly = false,
    String? specialInstructions,
  }) async {
    try {
      // Check if email is already taken
      final emailExists = await isEmailTaken(email);
      if (emailExists) {
        throw Exception('Email is already registered');
      }

      // Create authenticated user account
      final supabaseService = SupabaseService.instance;
      final accountResult = await supabaseService.createCustomerAccount(
        email: email,
        fullName: fullName,
        phone: phone,
        country: country,
      );

      final userId = accountResult['user_id'] as String;
      final tempPassword = accountResult['temp_password'] as String;

      // Create customer profile linked to the new user
      final response = await _supabase
          .from('customers')
          .insert({
            'id': userId, // Use the new auth user ID
            'full_name': fullName,
            'email': email,
            'phone': phone,
            'country': country,
            'notes': notes,
            'profile_picture': profilePicture,
            'date_of_birth': dateOfBirth,
            'gender': gender,
            'preferred_language': preferredLanguage,
            'theme': theme,
            'email_notifications': emailNotifications,
            'sms_notifications': smsNotifications,
            'push_notifications': pushNotifications,
            'preferred_currency': preferredCurrency,
            'timezone': timezone,
            'preferred_detergent': preferredDetergent,
            'preferred_fabric_softener': preferredFabricSoftener,
            'eco_friendly': ecoFriendly,
            'special_instructions': specialInstructions,
            'created_by': userId, // Use the new user ID as creator
          })
          .select()
          .single();

      final customer = Customer.fromJson(response);

      return {
        'customer': customer,
        'credentials': {
          'email': email,
          'password': tempPassword,
          'full_name': fullName,
        },
      };
    } catch (e) {
      throw Exception('Failed to create customer with authentication: $e');
    }
  }

  // Update customer with enhanced fields
  static Future<Customer> updateCustomer(Customer customer) async {
    try {
      final response = await _supabase
          .from('customers')
          .update(customer.toJson())
          .eq('id', customer.id)
          .select()
          .single();

      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update customer: $e');
    }
  }

  // Create or update current user's customer profile
  static Future<Customer> createOrUpdateCurrentUserCustomer({
    required String fullName,
    String? email,
    String? phone,
    String? country,
    String? notes,
    String? profilePicture,
    String? dateOfBirth,
    String? gender,
    String? preferredLanguage,
    String theme = 'system',
    bool emailNotifications = true,
    bool smsNotifications = false,
    bool pushNotifications = true,
    String preferredCurrency = 'USD',
    String? timezone,
    String? preferredDetergent,
    String? preferredFabricSoftener,
    bool ecoFriendly = false,
    String? specialInstructions,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User must be authenticated to create or update customer profile');
      }

      // Check if current user already has a customer profile
      final existingCustomer = await getCurrentUserCustomer();
      
      if (existingCustomer != null) {
        // Update existing profile
        final updatedCustomer = existingCustomer.copyWith(
          fullName: fullName,
          email: email ?? existingCustomer.email,
          phone: phone ?? existingCustomer.phone,
          country: country ?? existingCustomer.country,
          notes: notes ?? existingCustomer.notes,
          profilePicture: profilePicture ?? existingCustomer.profilePicture,
          dateOfBirth: dateOfBirth ?? existingCustomer.dateOfBirth,
          gender: gender ?? existingCustomer.gender,
          preferredLanguage: preferredLanguage ?? existingCustomer.preferredLanguage,
          theme: theme,
          emailNotifications: emailNotifications,
          smsNotifications: smsNotifications,
          pushNotifications: pushNotifications,
          preferredCurrency: preferredCurrency,
          timezone: timezone ?? existingCustomer.timezone,
          preferredDetergent: preferredDetergent ?? existingCustomer.preferredDetergent,
          preferredFabricSoftener: preferredFabricSoftener ?? existingCustomer.preferredFabricSoftener,
          ecoFriendly: ecoFriendly,
          specialInstructions: specialInstructions ?? existingCustomer.specialInstructions,
        );
        
        return await updateCustomer(updatedCustomer);
      } else {
        // Create new profile
        return await createCustomer(
          fullName: fullName,
          email: email,
          phone: phone,
          country: country,
          notes: notes,
          profilePicture: profilePicture,
          dateOfBirth: dateOfBirth,
          gender: gender,
          preferredLanguage: preferredLanguage,
          theme: theme,
          emailNotifications: emailNotifications,
          smsNotifications: smsNotifications,
          pushNotifications: pushNotifications,
          preferredCurrency: preferredCurrency,
          timezone: timezone,
          preferredDetergent: preferredDetergent,
          preferredFabricSoftener: preferredFabricSoftener,
          ecoFriendly: ecoFriendly,
          specialInstructions: specialInstructions,
        );
      }
    } catch (e) {
      throw Exception('Failed to create or update customer profile: $e');
    }
  }

  // Update specific customer preferences
  static Future<Customer> updateCustomerPreferences({
    required String customerId,
    String? theme,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    String? preferredCurrency,
    String? timezone,
    String? preferredLanguage,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (theme != null) updateData['theme'] = theme;
      if (emailNotifications != null) updateData['email_notifications'] = emailNotifications;
      if (smsNotifications != null) updateData['sms_notifications'] = smsNotifications;
      if (pushNotifications != null) updateData['push_notifications'] = pushNotifications;
      if (preferredCurrency != null) updateData['preferred_currency'] = preferredCurrency;
      if (timezone != null) updateData['timezone'] = timezone;
      if (preferredLanguage != null) updateData['preferred_language'] = preferredLanguage;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('customers')
          .update(updateData)
          .eq('id', customerId)
          .select()
          .single();

      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update customer preferences: $e');
    }
  }

  // Update customer laundry preferences
  static Future<Customer> updateLaundryPreferences({
    required String customerId,
    String? preferredDetergent,
    String? preferredFabricSoftener,
    bool? ecoFriendly,
    String? specialInstructions,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (preferredDetergent != null) updateData['preferred_detergent'] = preferredDetergent;
      if (preferredFabricSoftener != null) updateData['preferred_fabric_softener'] = preferredFabricSoftener;
      if (ecoFriendly != null) updateData['eco_friendly'] = ecoFriendly;
      if (specialInstructions != null) updateData['special_instructions'] = specialInstructions;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('customers')
          .update(updateData)
          .eq('id', customerId)
          .select()
          .single();

      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update laundry preferences: $e');
    }
  }

  // Update customer profile information
  static Future<Customer> updateProfileInfo({
    required String customerId,
    String? fullName,
    String? email,
    String? phone,
    String? country,
    String? notes,
    String? profilePicture,
    String? dateOfBirth,
    String? gender,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (fullName != null) updateData['full_name'] = fullName;
      if (email != null) updateData['email'] = email;
      if (phone != null) updateData['phone'] = phone;
      if (country != null) updateData['country'] = country;
      if (notes != null) updateData['notes'] = notes;
      if (profilePicture != null) updateData['profile_picture'] = profilePicture;
      if (dateOfBirth != null) updateData['date_of_birth'] = dateOfBirth;
      if (gender != null) updateData['gender'] = gender;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('customers')
          .update(updateData)
          .eq('id', customerId)
          .select()
          .single();

      return Customer.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update profile info: $e');
    }
  }

  // Check if email is already taken
  static Future<bool> isEmailTaken(String email) async {
    try {
      final response = await _supabase
          .rpc('is_customer_email_available', params: {'p_email': email});
      
      return !(response as bool); // Function returns true if available, we want opposite
    } catch (e) {
      // Fallback to manual check if function doesn't exist
      try {
        final response = await _supabase
            .from('customers')
            .select('id')
            .eq('email', email.toLowerCase())
            .eq('is_active', true)
            .maybeSingle();
        
        return response != null;
      } catch (e2) {
        throw Exception('Failed to check email availability: $e2');
      }
    }
  }

  // Get customer addresses
  static Future<List<CustomerAddress>> getCustomerAddresses(String customerId) async {
    try {
      final response = await _supabase
          .from('customer_addresses')
          .select()
          .eq('customer_id', customerId)
          .eq('is_active', true)
          .order('is_default', ascending: false)
          .order('title');

      return (response as List)
          .map((json) => CustomerAddress.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get customer addresses: $e');
    }
  }

  // Create customer address
  static Future<CustomerAddress> createCustomerAddress({
    required String customerId,
    required String title,
    required String addressLine1,
    String? addressLine2,
    required String city,
    String? state,
    String? postalCode,
    required String country,
    double? latitude,
    double? longitude,
    bool isDefault = false,
  }) async {
    try {
      // If this is set as default, unset other default addresses
      if (isDefault) {
        await _supabase
            .from('customer_addresses')
            .update({'is_default': false})
            .eq('customer_id', customerId);
      }

      final response = await _supabase
          .from('customer_addresses')
          .insert({
            'customer_id': customerId,
            'title': title,
            'address_line_1': addressLine1,
            'address_line_2': addressLine2,
            'city': city,
            'state': state,
            'postal_code': postalCode,
            'country': country,
            'latitude': latitude,
            'longitude': longitude,
            'is_default': isDefault,
          })
          .select()
          .single();

      return CustomerAddress.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create customer address: $e');
    }
  }

  // Update customer address
  static Future<CustomerAddress> updateCustomerAddress(CustomerAddress address) async {
    try {
      // If this is set as default, unset other default addresses
      if (address.isDefault) {
        await _supabase
            .from('customer_addresses')
            .update({'is_default': false})
            .eq('customer_id', address.customerId)
            .neq('id', address.id);
      }

      final response = await _supabase
          .from('customer_addresses')
          .update(address.toJson())
          .eq('id', address.id)
          .select()
          .single();

      return CustomerAddress.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update customer address: $e');
    }
  }

  // Delete customer (soft delete)
  static Future<void> deleteCustomer(String customerId) async {
    try {
      await _supabase
          .from('customers')
          .update({'is_active': false})
          .eq('id', customerId);
    } catch (e) {
      throw Exception('Failed to delete customer: $e');
    }
  }

  // Delete customer address (soft delete)
  static Future<void> deleteCustomerAddress(String addressId) async {
    try {
      await _supabase
          .from('customer_addresses')
          .update({'is_active': false})
          .eq('id', addressId);
    } catch (e) {
      throw Exception('Failed to delete customer address: $e');
    }
  }

  // Get customers by preferences
  static Future<List<Customer>> getCustomersByPreferences({
    String? theme,
    String? preferredCurrency,
    bool? ecoFriendly,
  }) async {
    try {
      var query = _supabase
          .from('customers')
          .select()
          .eq('is_active', true);

      if (theme != null) query = query.eq('theme', theme);
      if (preferredCurrency != null) query = query.eq('preferred_currency', preferredCurrency);
      if (ecoFriendly != null) query = query.eq('eco_friendly', ecoFriendly);

      final response = await query.order('full_name');
      return (response as List)
          .map((json) => Customer.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get customers by preferences: $e');
    }
  }

  // Get customers with special care requirements
  static Future<List<Customer>> getCustomersWithSpecialCare() async {
    try {
      final response = await _supabase
          .from('customers')
          .select()
          .eq('is_active', true)
          .not('special_instructions', 'is', null)
          .order('full_name');

      return (response as List)
          .map((json) => Customer.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get customers with special care: $e');
    }
  }

  // Bulk update customer preferences (for admin use)
  static Future<void> bulkUpdatePreferences({
    required List<String> customerIds,
    required Map<String, dynamic> preferences,
  }) async {
    try {
      await _supabase
          .from('customers')
          .update({
            ...preferences,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .inFilter('id', customerIds);
    } catch (e) {
      throw Exception('Failed to bulk update preferences: $e');
    }
  }
}
