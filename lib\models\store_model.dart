import 'dart:math';

class Store {
  final String id;
  final String? storeNumber;
  final String name;
  final String address;
  final String? phone;
  final String? email;
  final String? description;
  final String? country;
  final bool isActive;
  final String? createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Enhanced location and service fields
  final String? city;
  final String? state;
  final String? postalCode;
  final double? latitude;
  final double? longitude;
  final String? timezone;
  
  // Operating hours
  final Map<String, String> operatingHours; // Day -> Hours (e.g., 'monday' -> '9:00 AM - 6:00 PM')
  final bool isOpen24Hours;
  final bool isOpenOnWeekends;
  
  // Service availability
  final List<String> availableServices; // List of service types available at this store
  final List<String> availableGarmentCategories; // List of garment categories this store can handle
  final bool hasExpressService;
  final bool hasDryCleaning;
  final bool hasIroning;
  final bool hasPickupService;
  final bool hasDeliveryService;
  
  // Store features
  final String? storeImage;
  final int? maxOrderCapacity; // Maximum orders the store can handle per day
  final String? specialInstructions;
  final double? averageProcessingTime; // in hours

  Store({
    required this.id,
    this.storeNumber,
    required this.name,
    required this.address,
    this.phone,
    this.email,
    this.description,
    this.country,
    this.isActive = true,
    this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.city,
    this.state,
    this.postalCode,
    this.latitude,
    this.longitude,
    this.timezone,
    this.operatingHours = const {},
    this.isOpen24Hours = false,
    this.isOpenOnWeekends = true,
    this.availableServices = const ['wash_fold'],
    this.availableGarmentCategories = const ['clothing', 'household', 'special'],
    this.hasExpressService = false,
    this.hasDryCleaning = false,
    this.hasIroning = false,
    this.hasPickupService = true,
    this.hasDeliveryService = false,
    this.storeImage,
    this.maxOrderCapacity,
    this.specialInstructions,
    this.averageProcessingTime = 24.0,
  });

  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'] as String,
      storeNumber: json['store_number'] as String?,
      name: json['name'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      description: json['description'] as String?,
      country: json['country'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdBy: json['created_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      city: json['city'] as String?,
      state: json['state'] as String?,
      postalCode: json['postal_code'] as String?,
      latitude: json['latitude'] != null ? (json['latitude'] as num).toDouble() : null,
      longitude: json['longitude'] != null ? (json['longitude'] as num).toDouble() : null,
      timezone: json['timezone'] as String?,
      operatingHours: json['operating_hours'] != null 
          ? Map<String, String>.from(json['operating_hours'])
          : {},
      isOpen24Hours: json['is_open_24_hours'] as bool? ?? false,
      isOpenOnWeekends: json['is_open_on_weekends'] as bool? ?? true,
      availableServices: json['available_services'] != null 
          ? List<String>.from(json['available_services'])
          : ['wash_fold'],
      availableGarmentCategories: json['available_garment_categories'] != null 
          ? List<String>.from(json['available_garment_categories'])
          : ['clothing', 'household', 'special'],
      hasExpressService: json['has_express_service'] as bool? ?? false,
      hasDryCleaning: json['has_dry_cleaning'] as bool? ?? false,
      hasIroning: json['has_ironing'] as bool? ?? false,
      hasPickupService: json['has_pickup_service'] as bool? ?? true,
      hasDeliveryService: json['has_delivery_service'] as bool? ?? false,
      storeImage: json['store_image'] as String?,
      maxOrderCapacity: json['max_order_capacity'] as int?,
      specialInstructions: json['special_instructions'] as String?,
      averageProcessingTime: json['average_processing_time'] != null 
          ? (json['average_processing_time'] as num).toDouble()
          : 24.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store_number': storeNumber,
      'name': name,
      'address': address,
      'phone': phone,
      'email': email,
      'description': description,
      'country': country,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'operating_hours': operatingHours,
      'is_open_24_hours': isOpen24Hours,
      'is_open_on_weekends': isOpenOnWeekends,
      'available_services': availableServices,
      'available_garment_categories': availableGarmentCategories,
      'has_express_service': hasExpressService,
      'has_dry_cleaning': hasDryCleaning,
      'has_ironing': hasIroning,
      'has_pickup_service': hasPickupService,
      'has_delivery_service': hasDeliveryService,
      'store_image': storeImage,
      'max_order_capacity': maxOrderCapacity,
      'special_instructions': specialInstructions,
      'average_processing_time': averageProcessingTime,
    };
  }

  Store copyWith({
    String? id,
    String? storeNumber,
    String? name,
    String? address,
    String? phone,
    String? email,
    String? description,
    String? country,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? city,
    String? state,
    String? postalCode,
    double? latitude,
    double? longitude,
    String? timezone,
    Map<String, String>? operatingHours,
    bool? isOpen24Hours,
    bool? isOpenOnWeekends,
    List<String>? availableServices,
    List<String>? availableGarmentCategories,
    bool? hasExpressService,
    bool? hasDryCleaning,
    bool? hasIroning,
    bool? hasPickupService,
    bool? hasDeliveryService,
    String? storeImage,
    int? maxOrderCapacity,
    String? specialInstructions,
    double? averageProcessingTime,
  }) {
    return Store(
      id: id ?? this.id,
      storeNumber: storeNumber ?? this.storeNumber,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      description: description ?? this.description,
      country: country ?? this.country,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      operatingHours: operatingHours ?? this.operatingHours,
      isOpen24Hours: isOpen24Hours ?? this.isOpen24Hours,
      isOpenOnWeekends: isOpenOnWeekends ?? this.isOpenOnWeekends,
      availableServices: availableServices ?? this.availableServices,
      availableGarmentCategories: availableGarmentCategories ?? this.availableGarmentCategories,
      hasExpressService: hasExpressService ?? this.hasExpressService,
      hasDryCleaning: hasDryCleaning ?? this.hasDryCleaning,
      hasIroning: hasIroning ?? this.hasIroning,
      hasPickupService: hasPickupService ?? this.hasPickupService,
      hasDeliveryService: hasDeliveryService ?? this.hasDeliveryService,
      storeImage: storeImage ?? this.storeImage,
      maxOrderCapacity: maxOrderCapacity ?? this.maxOrderCapacity,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      averageProcessingTime: averageProcessingTime ?? this.averageProcessingTime,
    );
  }

  String get displayName {
    if (storeNumber != null) {
      return '$storeNumber - $name';
    }
    return name;
  }

  String get fullAddress {
    final parts = <String>[address];
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (state != null && state!.isNotEmpty) parts.add(state!);
    if (postalCode != null && postalCode!.isNotEmpty) parts.add(postalCode!);
    if (country != null && country!.isNotEmpty) parts.add(country!);
    return parts.join(', ');
  }

  // Helper methods for store features
  String get operatingHoursDisplay {
    if (isOpen24Hours) return 'Open 24/7';
    
    if (operatingHours.isEmpty) {
      return 'Hours not specified';
    }
    
    final today = DateTime.now().weekday;
    final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    final todayName = dayNames[today - 1];
    
    return operatingHours[todayName] ?? 'Closed today';
  }

  bool get isCurrentlyOpen {
    if (isOpen24Hours) return true;
    
    final now = DateTime.now();
    final today = now.weekday;
    final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    final todayName = dayNames[today - 1];
    
    if (!operatingHours.containsKey(todayName)) return false;
    
    // Simple check - in a real app you'd parse the time strings
    return true;
  }

  String get servicesDisplay {
    final services = <String>[];
    if (hasExpressService) services.add('Express');
    if (hasDryCleaning) services.add('Dry Cleaning');
    if (hasIroning) services.add('Ironing');
    if (hasPickupService) services.add('Pickup');
    if (hasDeliveryService) services.add('Delivery');
    
    if (services.isEmpty) return 'Basic Services';
    return services.join(', ');
  }

  bool canHandleService(String serviceType) {
    return availableServices.contains(serviceType);
  }

  bool canHandleGarmentCategory(String category) {
    return availableGarmentCategories.contains(category);
  }

  // Calculate distance from a given location (in km)
  double? calculateDistance(double? userLat, double? userLng) {
    if (latitude == null || longitude == null || userLat == null || userLng == null) {
      return null;
    }
    
    // Simple distance calculation using Haversine formula
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final lat1 = latitude! * (pi / 180);
    final lat2 = userLat * (pi / 180);
    final deltaLat = (userLat - latitude!) * (pi / 180);
    final deltaLng = (userLng - longitude!) * (pi / 180);
    
    final a = sin(deltaLat / 2) * sin(deltaLat / 2) +
        cos(lat1) * cos(lat2) * sin(deltaLng / 2) * sin(deltaLng / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }
}

class Country {
  final String id;
  final String name;
  final String code;
  final String? phoneCode;
  final String? flagEmoji;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Country({
    required this.id,
    required this.name,
    required this.code,
    this.phoneCode,
    this.flagEmoji,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      phoneCode: json['phone_code'] as String?,
      flagEmoji: json['flag_emoji'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'phone_code': phoneCode,
      'flag_emoji': flagEmoji,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get displayName {
    if (flagEmoji != null) {
      return '$flagEmoji $name';
    }
    return name;
  }

  String get displayNameWithCode {
    if (flagEmoji != null) {
      return '$flagEmoji $name ($code)';
    }
    return '$name ($code)';
  }
}
