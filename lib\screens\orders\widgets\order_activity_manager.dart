import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../models/order_model.dart';

// Simple activity entry class for order history
class OrderActivityEntry {
  final String title;
  final String actor;
  final DateTime at;
  final String? subtitle;
  final IconData icon;
  final Color color;

  OrderActivityEntry({
    required this.title,
    required this.actor,
    required this.at,
    this.subtitle,
    required this.icon,
    required this.color,
  });
}

class OrderActivityManager {
  final String orderId;
  final SupabaseClient supabase;

  OrderActivityManager({required this.orderId, required this.supabase});

  Future<List<OrderActivityEntry>> loadActivities() async {
    final statusRows = await supabase
        .from('order_status_log')
        .select()
        .eq('order_id', orderId)
        .order('created_at', ascending: false);

    final paymentRows = await supabase
        .from('payments')
        .select()
        .eq('order_id', orderId)
        .order('created_at', ascending: false);

    final userIds = <String>{};
    for (final row in (statusRows as List)) {
      final id = row['changed_by'] as String?;
      if (id != null) userIds.add(id);
    }
    for (final row in (paymentRows as List)) {
      final id = row['created_by'] as String?;
      if (id != null) userIds.add(id);
    }

    Map<String, String> userIdToName = {};
    if (userIds.isNotEmpty) {
      final profiles = await supabase
          .from('staff_profiles')
          .select('id, full_name')
          .inFilter('id', userIds.toList());
      for (final p in (profiles as List)) {
        userIdToName[p['id'] as String] = (p['full_name'] as String?) ?? 'User';
      }
    }

    final List<OrderActivityEntry> entries = [];

    for (final row in (statusRows as List)) {
      final at = DateTime.parse(row['created_at'] as String);
      final oldStatus = row['old_status'] as String?;
      final newStatus = row['new_status'] as String?;
      final notes = (row['notes'] as String?) ?? '';
      final actorId = (row['changed_by'] as String?) ?? '';
      final actor = userIdToName[actorId] ?? 'System';

      final title = oldStatus == null && newStatus != null
          ? 'Order created (status: $newStatus)'
          : 'Status changed: ${oldStatus ?? ''} → ${newStatus ?? ''}';

      entries.add(
        OrderActivityEntry(
          at: at,
          actor: actor,
          title: title,
          subtitle: notes.isNotEmpty ? notes : null,
          icon: Icons.update,
          color: Colors.blue,
        ),
      );
    }

    for (final row in (paymentRows as List)) {
      final at = DateTime.parse(row['created_at'] as String);
      final amount = (row['amount'] as num).toDouble();
      final method = (row['method'] as String?) ?? 'payment';
      final tx = row['transaction_code'] as String?;
      final receipt = row['receipt_number'] as String?;
      final actorId = (row['created_by'] as String?) ?? '';
      final actor = userIdToName[actorId] ?? 'System';

      final ref = tx ?? receipt;
      entries.add(
        OrderActivityEntry(
          at: at,
          actor: actor,
          title: 'Payment received: \$${amount.toStringAsFixed(2)}',
          subtitle: ref != null ? '$method • $ref' : method,
          icon: Icons.payment,
          color: Colors.green,
        ),
      );
    }

    entries.sort((a, b) => b.at.compareTo(a.at));
    return entries;
  }

  List<OrderStatus> getAvailableStatuses(Order order) {
    // Check if this is an in-store order (no pickup address)
    bool isInStoreOrder = order.pickupAddressId == null;

    switch (order.status) {
      case OrderStatus.pending:
        return [OrderStatus.accepted, OrderStatus.cancelled];
      case OrderStatus.accepted:
        // For in-store orders, skip the pickup step
        return isInStoreOrder
            ? [OrderStatus.inProcess, OrderStatus.cancelled]
            : [OrderStatus.pickedUp, OrderStatus.cancelled];
      case OrderStatus.pickedUp:
        return [OrderStatus.inProcess];
      case OrderStatus.inProcess:
        return [OrderStatus.readyForPickup];
      case OrderStatus.readyForPickup:
        // Ready for pickup/delivery - choice between customer pickup or delivery
        return [OrderStatus.completed, OrderStatus.outForDelivery];
      case OrderStatus.outForDelivery:
        // Out for delivery - can only mark as completed
        return [OrderStatus.completed];
      case OrderStatus.completed:
      case OrderStatus.cancelled:
        // No further status changes possible
        return [];
    }
  }

  /// Validate order before accepting
  Map<String, dynamic> validateOrderForAcceptance(Order order) {
    List<String> issues = [];

    // Check if order has items
    if (order.items.isEmpty) {
      issues.add('Order has no items');
    } else {
      // Check each item for completeness
      for (final item in order.items) {
        if (item.quantity <= 0) {
          issues.add('"${item.garment.name}" has invalid quantity');
        }
        if (item.unitPrice <= 0) {
          issues.add('"${item.garment.name}" has no unit price');
        }
      }
    }

    // Check if order has valid totals
    if (order.totalAmount <= 0) {
      issues.add('Order has no total amount');
    }

    return {
      'isValid': issues.isEmpty,
      'message': issues.join('\n'),
      'issues': issues,
    };
  }

  Color getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.purple;
      case OrderStatus.inProcess:
        return Colors.indigo;
      case OrderStatus.readyForPickup:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.amber;
      case OrderStatus.completed:
        return Colors.teal;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  String formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Widget for displaying activity feed
class OrderActivityWidget extends StatelessWidget {
  final List<OrderActivityEntry> activities;
  final bool isLoading;
  final OrderActivityManager activityManager;

  const OrderActivityWidget({
    super.key,
    required this.activities,
    required this.isLoading,
    required this.activityManager,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 8),
            Text('No activity yet', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: activities.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final activity = activities[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: activity.color.withValues(alpha: 0.15),
            child: Icon(activity.icon, color: activity.color, size: 18),
          ),
          title: Text(
            activity.title,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          subtitle: Text(
            '${activity.actor} • ${activityManager.formatDateTime(activity.at)}${activity.subtitle != null ? '\n${activity.subtitle}' : ''}',
          ),
          isThreeLine: activity.subtitle != null,
        );
      },
    );
  }
}
