class EmailFormatter {
  /// Formats email to show only first 3 characters and last 4 characters
  /// Example: "<EMAIL>" becomes "pet***@gmail.com"
  static String formatEmail(String email) {
    if (email.isEmpty) return email;
    
    // Split email into local part and domain
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final localPart = parts[0];
    final domain = parts[1];
    
    // Format local part (first 3 chars + ***)
    String formattedLocalPart;
    if (localPart.length <= 3) {
      formattedLocalPart = localPart;
    } else {
      formattedLocalPart = '${localPart.substring(0, 3)}***';
    }
    
    // Return formatted email
    return '$formattedLocalPart@$domain';
  }
  
  /// Alternative format: shows first 3 and last 4 characters of the entire email
  /// Example: "<EMAIL>" becomes "pet***@gmail.com"
  /// This is the same as formatEmail but more explicit
  static String truncateEmail(String email) {
    return formatEmail(email);
  }
  
  /// Shows only the domain part of the email
  /// Example: "<EMAIL>" becomes "@gmail.com"
  static String showDomainOnly(String email) {
    if (email.isEmpty) return email;
    
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    return '@${parts[1]}';
  }
  
  /// Shows first 3 characters of local part + domain
  /// Example: "<EMAIL>" becomes "<EMAIL>"
  static String showFirstThreeAndDomain(String email) {
    if (email.isEmpty) return email;
    
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final localPart = parts[0];
    final domain = parts[1];
    
    if (localPart.length <= 3) {
      return email;
    } else {
      return '${localPart.substring(0, 3)}@$domain';
    }
  }
}
