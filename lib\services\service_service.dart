import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/service_model.dart';

class ServiceService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Get all active services
  static Future<List<Service>> getAllServices() async {
    try {
      final response = await _supabase
          .from('services')
          .select()
          .eq('is_active', true)
          .order('name');

      return (response as List)
          .map((json) => Service.fromJson(json))
          .toList();
    } catch (e) {
      // Return mock data if services table doesn't exist yet
      return _getMockServices();
    }
  }

  // Search services by name or description
  static Future<List<Service>> searchServices(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllServices();
      }

      final response = await _supabase
          .from('services')
          .select()
          .eq('is_active', true)
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .order('name');

      return (response as List)
          .map((json) => Service.fromJson(json))
          .toList();
    } catch (e) {
      // Return filtered mock data if services table doesn't exist yet
      final mockServices = _getMockServices();
      final lowerQuery = query.toLowerCase();
      return mockServices.where((service) =>
        service.name.toLowerCase().contains(lowerQuery) ||
        service.description!.toLowerCase().contains(lowerQuery)
      ).toList();
    }
  }

  // Get service by ID
  static Future<Service?> getServiceById(String id) async {
    try {
      final response = await _supabase
          .from('services')
          .select()
          .eq('id', id)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      return Service.fromJson(response);
    } catch (e) {
      // Return from mock data if services table doesn't exist yet
      final mockServices = _getMockServices();
      try {
        return mockServices.firstWhere((service) => service.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Create new service
  static Future<Service> createService({
    required String name,
    required String description,
    required String pricingType,
    double? basePrice,
    double? pricePerKg,
    double? pricePerItem,
    int? estimatedHours,
    String? icon,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      final response = await _supabase
          .from('services')
          .insert({
            'name': name,
            'description': description,
            'pricing_type': pricingType,
            'base_price': basePrice ?? 0.0,
            'price_per_kg': pricePerKg,
            'price_per_item': pricePerItem,
            'estimated_hours': estimatedHours ?? 24,
            'icon': icon,
            'created_by': currentUser?.id,
          })
          .select()
          .single();

      return Service.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create service: $e');
    }
  }

  // Update service
  static Future<Service> updateService(Service service) async {
    try {
      final response = await _supabase
          .from('services')
          .update(service.toJson())
          .eq('id', service.id)
          .select()
          .single();

      return Service.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update service: $e');
    }
  }

  // Delete service (soft delete)
  static Future<void> deleteService(String serviceId) async {
    try {
      await _supabase
          .from('services')
          .update({'is_active': false})
          .eq('id', serviceId);
    } catch (e) {
      throw Exception('Failed to delete service: $e');
    }
  }

  // Mock data for when services table doesn't exist yet
  static List<Service> _getMockServices() {
    return [
      Service(
        id: '1',
        name: 'Wash & Fold',
        description: 'Regular washing and folding service',
        basePrice: 0.00,
        pricePerKg: 5.00,
        pricingType: 'per_kg',
        estimatedHours: 24,
        icon: 'local_laundry_service',
        serviceType: 'wash_fold',
        category: 'basic',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Service(
        id: '2',
        name: 'Dry Cleaning',
        description: 'Professional dry cleaning service',
        basePrice: 0.00,
        pricePerItem: 15.00,
        pricingType: 'per_item',
        estimatedHours: 48,
        icon: 'dry_cleaning',
        serviceType: 'dry_cleaning',
        category: 'premium',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Service(
        id: '3',
        name: 'Ironing',
        description: 'Professional ironing and pressing',
        basePrice: 0.00,
        pricePerItem: 3.00,
        pricingType: 'per_item',
        estimatedHours: 12,
        icon: 'iron',
        serviceType: 'ironing',
        category: 'basic',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Service(
        id: '4',
        name: 'Express Service',
        description: 'Same day wash and fold',
        basePrice: 0.00,
        pricePerKg: 8.00,
        pricingType: 'per_kg',
        estimatedHours: 8,
        icon: 'flash_on',
        serviceType: 'express',
        category: 'premium',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Service(
        id: '5',
        name: 'Delicate Care',
        description: 'Special care for delicate items',
        basePrice: 0.00,
        pricePerItem: 20.00,
        pricingType: 'per_item',
        estimatedHours: 72,
        icon: 'favorite',
        serviceType: 'delicate',
        category: 'luxury',
        requiresSpecialCare: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}
