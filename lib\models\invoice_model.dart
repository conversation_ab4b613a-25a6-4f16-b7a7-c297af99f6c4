import 'package:flutter/foundation.dart';
import 'payment_model.dart';

enum PaymentStatus {
  pending,
  partial,
  paid,
  refunded,
  failed;

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.partial:
        return 'Partially Paid';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.failed:
        return 'Failed';
    }
  }

  String get dbValue {
    switch (this) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.partial:
        return 'partial';
      case PaymentStatus.paid:
        return 'paid';
      case PaymentStatus.refunded:
        return 'refunded';
      case PaymentStatus.failed:
        return 'failed';
    }
  }

  static PaymentStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return PaymentStatus.pending;
      case 'partial':
        return PaymentStatus.partial;
      case 'paid':
        return PaymentStatus.paid;
      case 'refunded':
        return PaymentStatus.refunded;
      case 'failed':
        return PaymentStatus.failed;
      default:
        return PaymentStatus.pending;
    }
  }
}

@immutable
class Invoice {
  final String id;
  final String invoiceNumber;
  final int invoiceSequenceNumber;
  final String orderId;
  final String userId;
  final double subtotal;
  final double taxRate;
  final double taxAmount;
  final double deliveryFee;
  final double discountAmount;
  final double totalAmount;
  final PaymentStatus paymentStatus;
  final String? paymentMethod;
  final DateTime? paidAt;
  final DateTime invoiceDate;
  final DateTime? dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Extended fields for payment summary
  final double? totalPaid;
  final double? outstandingAmount;
  final int? paymentsCount;
  final List<Payment>? payments;

  const Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.invoiceSequenceNumber,
    required this.orderId,
    required this.userId,
    required this.subtotal,
    required this.taxRate,
    required this.taxAmount,
    required this.deliveryFee,
    required this.discountAmount,
    required this.totalAmount,
    required this.paymentStatus,
    this.paymentMethod,
    this.paidAt,
    required this.invoiceDate,
    this.dueDate,
    required this.createdAt,
    required this.updatedAt,
    this.totalPaid,
    this.outstandingAmount,
    this.paymentsCount,
    this.payments,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: (json['id'] as String?) ?? '',
      invoiceNumber: (json['invoice_number'] as String?) ?? '',
      invoiceSequenceNumber: (json['invoice_sequence_number'] as int?) ?? 0,
      orderId: (json['order_id'] as String?) ?? '',
      userId: (json['user_id'] as String?) ?? '',
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      taxRate: (json['tax_rate'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['tax_amount'] as num?)?.toDouble() ?? 0.0,
      deliveryFee: (json['delivery_fee'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      paymentStatus: PaymentStatus.fromString((json['payment_status'] as String?) ?? 'pending'),
      paymentMethod: json['payment_method'] as String?,
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at'] as String) : null,
      invoiceDate: json['invoice_date'] != null 
          ? DateTime.parse(json['invoice_date'] as String)
          : DateTime.now(),
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date'] as String) : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      totalPaid: (json['total_paid'] as num?)?.toDouble(),
      outstandingAmount: (json['outstanding_amount'] as num?)?.toDouble(),
      paymentsCount: json['payments_count'] as int?,
      payments: json['payments'] != null 
          ? (json['payments'] as List).map((p) => Payment.fromJson(p)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'invoice_sequence_number': invoiceSequenceNumber,
      'order_id': orderId,
      'user_id': userId,
      'subtotal': subtotal,
      'tax_rate': taxRate,
      'tax_amount': taxAmount,
      'delivery_fee': deliveryFee,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'payment_status': paymentStatus.dbValue,
      'payment_method': paymentMethod,
      'paid_at': paidAt?.toIso8601String(),
      'invoice_date': invoiceDate.toIso8601String().split('T')[0],
      'due_date': dueDate?.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (totalPaid != null) 'total_paid': totalPaid,
      if (outstandingAmount != null) 'outstanding_amount': outstandingAmount,
      if (paymentsCount != null) 'payments_count': paymentsCount,
      if (payments != null) 'payments': payments!.map((p) => p.toJson()).toList(),
    };
  }

  double get effectiveOutstandingAmount {
    return outstandingAmount ?? (totalAmount - (totalPaid ?? 0.0));
  }

  bool get isPaid {
    return paymentStatus == PaymentStatus.paid;
  }

  bool get hasOutstandingAmount {
    return effectiveOutstandingAmount > 0.01; // Account for floating point precision
  }

  bool get isOverdue {
    return dueDate != null && DateTime.now().isAfter(dueDate!) && !isPaid;
  }

  String get displayStatus {
    return paymentStatus.displayName;
  }

  Invoice copyWith({
    String? id,
    String? invoiceNumber,
    int? invoiceSequenceNumber,
    String? orderId,
    String? userId,
    double? subtotal,
    double? taxRate,
    double? taxAmount,
    double? deliveryFee,
    double? discountAmount,
    double? totalAmount,
    PaymentStatus? paymentStatus,
    String? paymentMethod,
    DateTime? paidAt,
    DateTime? invoiceDate,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? totalPaid,
    double? outstandingAmount,
    int? paymentsCount,
    List<Payment>? payments,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      invoiceSequenceNumber: invoiceSequenceNumber ?? this.invoiceSequenceNumber,
      orderId: orderId ?? this.orderId,
      userId: userId ?? this.userId,
      subtotal: subtotal ?? this.subtotal,
      taxRate: taxRate ?? this.taxRate,
      taxAmount: taxAmount ?? this.taxAmount,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paidAt: paidAt ?? this.paidAt,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      totalPaid: totalPaid ?? this.totalPaid,
      outstandingAmount: outstandingAmount ?? this.outstandingAmount,
      paymentsCount: paymentsCount ?? this.paymentsCount,
      payments: payments ?? this.payments,
    );
  }
}
