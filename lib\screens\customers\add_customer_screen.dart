import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/country_model.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import '../../widgets/country_picker_dialog.dart';
import '../../providers/staff_auth_provider.dart';

// Email validation status class
class EmailValidationStatus {
  final String message;
  final IconData icon;
  final Color color;
  final bool isAvailable;

  const EmailValidationStatus({
    required this.message,
    required this.icon,
    required this.color,
    required this.isAvailable,
  });
}

class AddCustomerScreen extends StatefulWidget {
  final Customer? customer; // Optional customer for editing
  
  const AddCustomerScreen({super.key, this.customer});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  
  // Address fields
  final _addressTitleController = TextEditingController();
  final _streetAddressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  
  Country? _selectedCountry;
  Country? _selectedAddressCountry;
  bool _isLoading = false;
  bool _emailChecking = false;
  bool _addDefaultAddress = false;

  // Email validation status
  EmailValidationStatus? _emailValidationStatus;

  // Form validation status
  bool _hasValidationErrors = false;

  @override
  void initState() {
    super.initState();
    _initializeFormForEditing();
  }

  void _initializeFormForEditing() {
    if (widget.customer != null) {
      final customer = widget.customer!;
      _fullNameController.text = customer.fullName;
      _emailController.text = customer.email ?? '';
      _phoneController.text = customer.phone ?? '';
      _notesController.text = customer.notes ?? '';
      
      // Set country if available
      if (customer.country != null) {
        // Note: We'll need to fetch the actual Country object
        // For now, we'll create a basic Country object
        _selectedCountry = Country(
          id: '', // We don't have the ID from customer model
          name: customer.country!,
          code: '', // We don't have the code
          flagEmoji: '', // We don't have the flag emoji
        );
      }
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _addressTitleController.dispose();
    _streetAddressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    super.dispose();
  }

  Future<void> _checkEmailAvailability() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) return;

    // Skip email check if editing and email hasn't changed
    if (widget.customer != null && widget.customer!.email == email) {
      return;
    }

    setState(() => _emailChecking = true);
    
    try {
      final isEmailTaken = await CustomerService.isEmailTaken(email);
      if (mounted) {
        if (isEmailTaken) {
          setState(() {
            _emailValidationStatus = const EmailValidationStatus(
              message: 'This email is already registered by another customer',
              icon: Icons.warning,
              color: Colors.orange,
              isAvailable: false,
            );
          });
          _showEmailStatus(
            isAvailable: false,
            message: 'This email is already registered by another customer',
            icon: Icons.warning,
            color: Colors.orange,
          );
        } else {
          setState(() {
            _emailValidationStatus = const EmailValidationStatus(
              message: 'Email is available for registration',
              icon: Icons.check_circle,
              color: Colors.green,
              isAvailable: true,
            );
          });
          _showEmailStatus(
            isAvailable: true,
            message: 'Email is available for registration',
            icon: Icons.check_circle,
            color: Colors.green,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _emailValidationStatus = EmailValidationStatus(
            message:
                'Error checking email availability: ${e.toString().replaceAll('Exception: ', '')}',
            icon: Icons.error,
            color: Colors.red,
            isAvailable: false,
          );
        });
        _showEmailStatus(
          isAvailable: false,
          message:
              'Error checking email availability: ${e.toString().replaceAll('Exception: ', '')}',
          icon: Icons.error,
          color: Colors.red,
        );
      }
    } finally {
      if (mounted) {
        setState(() => _emailChecking = false);
      }
    }
  }

  void _showEmailStatus({
    required bool isAvailable,
    required String message,
    required IconData icon,
    required Color color,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: color,
        duration: Duration(seconds: isAvailable ? 3 : 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _updateValidationStatus() {
    // Check if there are any validation errors
    bool hasErrors = false;

    // Check required fields
    if (_fullNameController.text.trim().isEmpty ||
        _fullNameController.text.trim().length < 2) {
      hasErrors = true;
    }

    if (_emailController.text.trim().isEmpty) {
      hasErrors = true;
    }

    // Check email format if provided
    if (_emailController.text.trim().isNotEmpty) {
      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
      if (!emailRegex.hasMatch(_emailController.text.trim())) {
        hasErrors = true;
      }
    }

    // Check address validation if requested
    if (_addDefaultAddress) {
      if (_addressTitleController.text.trim().isEmpty ||
          _streetAddressController.text.trim().isEmpty ||
          _cityController.text.trim().isEmpty ||
          _selectedAddressCountry == null) {
        hasErrors = true;
      }
    }

    setState(() {
      _hasValidationErrors = hasErrors;
    });
  }

  void _showEmailRegisteredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.email, color: Colors.orange),
              const SizedBox(width: 8),
              Text('Email Already Registered'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'This email address is already registered in our system.',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'What you can do:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Ask the customer to use a different email address\n'
                      '• Contact the existing customer to update their information\n'
                      '• Use the customer search to find the existing account\n'
                      '• Contact support if you need assistance',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Clear the email field to help user enter a different email
                _emailController.clear();
                setState(() {
                  _emailValidationStatus = null;
                });
              },
              child: Text('Clear Email'),
            ),
          ],
        );
      },
    );
  }

  void _showDetailedError(String error, String action) {
    // Parse common error messages and provide user-friendly feedback
    String userMessage = 'Failed to $action customer';
    Color backgroundColor = Colors.red;
    IconData icon = Icons.error;

    if (error.contains('Email is already registered')) {
      userMessage = 'This email is already registered by another customer';
      backgroundColor = Colors.orange;
      icon = Icons.warning;
    } else if (error.contains('Failed to create customer account')) {
      userMessage = 'Unable to create customer account. Please try again.';
      backgroundColor = Colors.red;
      icon = Icons.account_circle;
    } else if (error.contains('network') || error.contains('connection')) {
      userMessage =
          'Network error. Please check your connection and try again.';
      backgroundColor = Colors.red;
      icon = Icons.wifi_off;
    } else if (error.contains('permission') || error.contains('not allowed')) {
      userMessage = 'Permission denied. Please contact your administrator.';
      backgroundColor = Colors.red;
      icon = Icons.lock;
    }

    // Show detailed error dialog for complex errors
    if (error.length > 100 || error.contains('Exception:')) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(icon, color: backgroundColor),
                const SizedBox(width: 8),
                Text('Error Details'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userMessage,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Technical Details:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        error.replaceAll('Exception: ', ''),
                        style: const TextStyle(
                          fontSize: 11,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    } else {
      // Show simple snackbar for simple errors
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(icon, color: Colors.white, size: 20),
              const SizedBox(width: 12),
              Expanded(child: Text(userMessage)),
            ],
          ),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }

  void _showSuccessMessage(Customer customer, String action) {
    final isNewCustomer = action == 'created';
    final hasAddress =
        _addDefaultAddress &&
        _addressTitleController.text.trim().isNotEmpty &&
        _streetAddressController.text.trim().isNotEmpty &&
        _cityController.text.trim().isNotEmpty &&
        _selectedAddressCountry != null;

    String message = 'Customer "${customer.fullName}" $action successfully';
    if (isNewCustomer && hasAddress) {
      message += ' with default address';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Could navigate to customer details here
            Navigator.of(context).pop(customer);
          },
        ),
      ),
    );
  }

  void _showCredentialsDialog(Map<String, dynamic> credentials) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.account_circle, color: Colors.blue),
              SizedBox(width: 8),
              Text('Customer Account Created'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'A customer account has been created for:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Name: ${credentials['full_name']}'),
              Text('Email: ${credentials['email']}'),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Temporary Password:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      credentials['password'],
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade900,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Important:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              Text(
                '• Share these credentials with the customer\n'
                '• Customer should check email and confirm account\n'
                '• Customer should change password on first login\n'
                '• Account will be active after email confirmation',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveCustomer() async {
    // Update validation status
    _updateValidationStatus();

    if (!_formKey.currentState!.validate()) {
      setState(() {
        _hasValidationErrors = true;
      });
      return;
    }

    setState(() {
      _hasValidationErrors = false;
    });

    // Additional validation for address if requested
    if (_addDefaultAddress) {
      if (_selectedAddressCountry == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a country for the address'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    // Check for staff authentication
    final staffAuthProvider = Provider.of<StaffAuthProvider>(
      context,
      listen: false,
    );
    if (staffAuthProvider.userType != UserType.staff) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only staff members can add customers'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final Customer customer;
      
      if (widget.customer != null) {
        // Update existing customer
        final updatedCustomer = widget.customer!.copyWith(
          fullName: _fullNameController.text.trim(),
          email: _emailController.text.trim().isEmpty
              ? null
              : _emailController.text.trim(),
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          country: _selectedCountry?.name,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );
        customer = await CustomerService.updateCustomer(updatedCustomer);
      } else {
        // Create new customer with authentication if email is provided
        final email = _emailController.text.trim();
        if (email.isNotEmpty) {
          // Create customer with authentication
          final result = await CustomerService.createCustomerWithAuth(
            fullName: _fullNameController.text.trim(),
            email: email,
            phone: _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
            country: _selectedCountry?.name,
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
          );

          customer = result['customer'] as Customer;
          final credentials = result['credentials'] as Map<String, dynamic>;

          // Show credentials dialog
          if (mounted) {
            _showCredentialsDialog(credentials);
          }
        } else {
          // Create customer without authentication (walk-in customer)
        customer = await CustomerService.createCustomer(
          fullName: _fullNameController.text.trim(),
            email: null,
            phone: _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
          country: _selectedCountry?.name,
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        );
        }
      }

      // Create default address if requested (only for new customers)
      if (widget.customer == null &&
          _addDefaultAddress &&
          _addressTitleController.text.trim().isNotEmpty &&
          _streetAddressController.text.trim().isNotEmpty &&
          _cityController.text.trim().isNotEmpty &&
          _selectedAddressCountry != null) {
        try {
          await CustomerService.createCustomerAddress(
            customerId: customer.id,
            title: _addressTitleController.text.trim(),
            addressLine1: _streetAddressController.text.trim(),
            city: _cityController.text.trim(),
            state: _stateController.text.trim().isEmpty
                ? null
                : _stateController.text.trim(),
            postalCode: _postalCodeController.text.trim().isEmpty
                ? null
                : _postalCodeController.text.trim(),
            country: _selectedAddressCountry!.name,
            isDefault: true,
          );
        } catch (addressError) {
          // If address creation fails, still show success for customer creation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Customer created but failed to add address: $addressError',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }

      if (mounted) {
        Navigator.of(
          context,
        ).pop(customer); // Return the created/updated customer
        _showSuccessMessage(
          customer,
          widget.customer != null ? 'updated' : 'created',
        );
      }
    } catch (e) {
      if (mounted) {
        _showDetailedError(
          e.toString(),
          widget.customer != null ? 'update' : 'create',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required for customer accounts';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }

    // Check if email is already registered (if we have validation status)
    if (_emailValidationStatus != null &&
        _emailValidationStatus!.isAvailable == false) {
      return 'This email is already registered by another customer';
    }
    
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Phone is optional
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.customer != null ? 'Edit Customer' : 'Add New Customer',
        ),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveCustomer,
              child: const Text(
                'SAVE',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Consumer<StaffAuthProvider>(
        builder: (context, staffAuthProvider, child) {
          if (staffAuthProvider.userType != UserType.staff) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Access Denied',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text('Only staff members can add customers.'),
                ],
              ),
            );
          }

          return Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Information Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    border: Border.all(color: Colors.blue.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade700),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Customer Account Creation',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade800,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Providing an email address will create a customer account with login credentials. The customer will receive a confirmation email and can then log in to view their orders and manage their profile.',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Form Validation Status
                if (_hasValidationErrors) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      border: Border.all(color: Colors.red.shade200),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Please fix the validation errors below to continue',
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // Full Name (Required)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.person, color: Colors.indigo[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'Customer Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _fullNameController,
                          decoration: const InputDecoration(
                            labelText: 'Full Name *',
                            hintText: 'Enter customer full name',
                            prefixIcon: Icon(Icons.person_outline),
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Full name is required';
                            }
                            if (value.trim().length < 2) {
                              return 'Full name must be at least 2 characters';
                            }
                            return null;
                          },
                          textCapitalization: TextCapitalization.words,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Contact Information
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.contact_phone,
                              color: Colors.indigo[600],
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'Contact Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Email
                        TextFormField(
                          controller: _emailController,
                          decoration: InputDecoration(
                            labelText: 'Email Address *',
                            hintText: '<EMAIL>',
                            prefixIcon: const Icon(Icons.email_outlined),
                            suffixIcon: _emailChecking
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  )
                                : _emailValidationStatus != null &&
                                      _emailValidationStatus!.isAvailable ==
                                          false
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.warning,
                                        color: Colors.orange,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 4),
                                      IconButton(
                                        onPressed: () {
                                          _emailController.clear();
                                          setState(() {
                                            _emailValidationStatus = null;
                                          });
                                        },
                                        icon: Icon(
                                          Icons.clear,
                                          color: Colors.orange,
                                          size: 16,
                                        ),
                                        tooltip: 'Clear email',
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(
                                          minWidth: 24,
                                          minHeight: 24,
                                        ),
                                      ),
                                    ],
                                  )
                                : null,
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color:
                                    _emailValidationStatus != null &&
                                        _emailValidationStatus!.isAvailable ==
                                            false
                                    ? Colors.orange
                                    : Colors.grey.shade400,
                                width:
                                    _emailValidationStatus != null &&
                                        _emailValidationStatus!.isAvailable ==
                                            false
                                    ? 2
                                    : 1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color:
                                    _emailValidationStatus != null &&
                                        _emailValidationStatus!.isAvailable ==
                                            false
                                    ? Colors.orange
                                    : Colors.indigo,
                                width: 2,
                              ),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.red,
                                width: 2,
                              ),
                            ),
                            focusedErrorBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.red,
                                width: 2,
                              ),
                            ),
                            helperText:
                                'Required - Creates customer account with login credentials',
                            helperStyle: TextStyle(
                              color:
                                  _emailValidationStatus != null &&
                                      _emailValidationStatus!.isAvailable ==
                                          false
                                  ? Colors.orange
                                  : Colors.grey.shade600,
                            ),
                          ),
                          keyboardType: TextInputType.emailAddress,
                          validator: _validateEmail,
                          onChanged: (value) {
                            // Clear previous validation status
                            setState(() {
                              _emailValidationStatus = null;
                            });

                            // Debounce email checking
                            Future.delayed(
                              const Duration(milliseconds: 800),
                              () {
                                if (_emailController.text.trim() ==
                                        value.trim() &&
                                    value.trim().isNotEmpty) {
                                _checkEmailAvailability();
                              }
                              },
                            );
                          },
                        ),

                        // Email validation status indicator
                        if (_emailValidationStatus != null) ...[
                          Builder(
                            builder: (context) {
                              final status = _emailValidationStatus!;
                              return Container(
                                margin: const EdgeInsets.only(top: 8),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: status.color.withValues(alpha: 0.1),
                                  border: Border.all(
                                    color: status.color.withValues(alpha: 0.3),
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      status.icon,
                                      color: status.color,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            status.message,
                                            style: TextStyle(
                                              color: status.color,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          if (!status.isAvailable) ...[
                                            const SizedBox(height: 4),
                                            Text(
                                              'Please use a different email address or contact the existing customer',
                                              style: TextStyle(
                                                color: status.color.withValues(
                                                  alpha: 0.8,
                                                ),
                                                fontSize: 11,
                                                fontStyle: FontStyle.italic,
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                    if (!status.isAvailable) ...[
                                      IconButton(
                                        onPressed: () {
                                          _showEmailRegisteredDialog();
                                        },
                                        icon: Icon(
                                          Icons.info_outline,
                                          color: status.color,
                                          size: 16,
                                        ),
                                        tooltip: 'More information',
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(
                                          minWidth: 24,
                                          minHeight: 24,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                        
                        const SizedBox(height: 16),
                        
                        // Phone
                        TextFormField(
                          controller: _phoneController,
                          decoration: const InputDecoration(
                            labelText: 'Phone Number',
                            hintText: '+****************',
                            prefixIcon: Icon(Icons.phone_outlined),
                            border: OutlineInputBorder(),
                            helperText:
                                'Optional - Include country code if international',
                          ),
                          keyboardType: TextInputType.phone,
                          validator: _validatePhone,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Country Selection
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.location_on, color: Colors.indigo[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'Location',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        InkWell(
                          onTap: () {
                            showCountryPicker(
                              context: context,
                              selectedCountry: _selectedCountry,
                              onSelect: (country) {
                                setState(() {
                                  _selectedCountry = country;
                                });
                              },
                            );
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[400]!),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.flag_outlined,
                                  color: Colors.grey,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: _selectedCountry != null
                                      ? Row(
                                          children: [
                                            Text(
                                              _selectedCountry!.flagEmoji ??
                                                  '🌐',
                                              style: const TextStyle(
                                                fontSize: 20,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                _selectedCountry!.name,
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ),
                                          ],
                                        )
                                      : Text(
                                          'Select Country (Optional)',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 16,
                                          ),
                                        ),
                                ),
                                const Icon(
                                  Icons.arrow_drop_down,
                                  color: Colors.grey,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Notes
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.note, color: Colors.indigo[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'Additional Notes',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _notesController,
                          decoration: const InputDecoration(
                            labelText: 'Notes',
                            hintText:
                                'Any special instructions or customer preferences...',
                            prefixIcon: Icon(Icons.note_outlined),
                            border: OutlineInputBorder(),
                            helperText:
                                'Optional - Special instructions, preferences, etc.',
                          ),
                          maxLines: 3,
                          textCapitalization: TextCapitalization.sentences,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Save Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveCustomer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.save),
                              const SizedBox(width: 8),
                              Text(
                                widget.customer != null
                                    ? 'Update Customer'
                                    : 'Create Customer',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Default Address Section (only for new customers)
                if (widget.customer == null) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                              Icon(
                                Icons.home_outlined,
                                color: Colors.indigo[600],
                              ),
                            const SizedBox(width: 8),
                            const Text(
                              'Default Address (Optional)',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add a default address for this customer',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Toggle to add address
                        CheckboxListTile(
                          title: const Text('Add default address'),
                            subtitle: const Text(
                              'This will be the customer\'s primary address',
                            ),
                          value: _addDefaultAddress,
                          onChanged: (value) {
                            setState(() {
                              _addDefaultAddress = value ?? false;
                              if (!_addDefaultAddress) {
                                // Clear address fields when unchecked
                                _addressTitleController.clear();
                                _streetAddressController.clear();
                                _cityController.clear();
                                _stateController.clear();
                                _postalCodeController.clear();
                                _selectedAddressCountry = null;
                              } else {
                                // Set default title
                                _addressTitleController.text = 'Home';
                              }
                            });
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                          activeColor: Colors.indigo,
                          contentPadding: EdgeInsets.zero,
                        ),
                        
                        // Address fields (shown when checkbox is checked)
                        if (_addDefaultAddress) ...[
                          const SizedBox(height: 16),
                          
                          // Address Title
                          TextFormField(
                            controller: _addressTitleController,
                            decoration: const InputDecoration(
                              labelText: 'Address Title',
                              hintText: 'e.g. Home, Office',
                              prefixIcon: Icon(Icons.label_outline),
                              border: OutlineInputBorder(),
                            ),
                              validator: _addDefaultAddress
                                  ? (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                return 'Address title is required';
                              }
                              return null;
                                    }
                                  : null,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Country Selection (for address)
                          InkWell(
                            onTap: () {
                              showCountryPicker(
                                context: context,
                                selectedCountry: _selectedAddressCountry,
                                onSelect: (country) {
                                  setState(() {
                                    _selectedAddressCountry = country;
                                  });
                                },
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[400]!),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                    const Icon(
                                      Icons.flag_outlined,
                                      color: Colors.grey,
                                    ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _selectedAddressCountry != null
                                        ? Row(
                                            children: [
                                              Text(
                                                  _selectedAddressCountry!
                                                          .flagEmoji ??
                                                      '🌐',
                                                  style: const TextStyle(
                                                    fontSize: 20,
                                                  ),
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                    _selectedAddressCountry!
                                                        .name,
                                                    style: const TextStyle(
                                                      fontSize: 16,
                                                    ),
                                                ),
                                              ),
                                            ],
                                          )
                                        : Text(
                                            'Select Country for Address',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 16,
                                            ),
                                          ),
                                  ),
                                    const Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.grey,
                                    ),
                                ],
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Street Address
                          TextFormField(
                            controller: _streetAddressController,
                            decoration: const InputDecoration(
                              labelText: 'Street Address',
                              hintText: 'Enter full street address',
                              prefixIcon: Icon(Icons.home_outlined),
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 2,
                              validator: _addDefaultAddress
                                  ? (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                return 'Street address is required';
                              }
                              return null;
                                    }
                                  : null,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // City
                          TextFormField(
                            controller: _cityController,
                            decoration: const InputDecoration(
                              labelText: 'City',
                              hintText: 'e.g. Nairobi',
                              prefixIcon: Icon(Icons.location_city),
                              border: OutlineInputBorder(),
                            ),
                              validator: _addDefaultAddress
                                  ? (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                return 'City is required';
                              }
                              return null;
                                    }
                                  : null,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // State and Postal Code
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _stateController,
                                  decoration: const InputDecoration(
                                    labelText: 'State/County',
                                    hintText: 'Optional',
                                    prefixIcon: Icon(Icons.map_outlined),
                                    border: OutlineInputBorder(),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: TextFormField(
                                  controller: _postalCodeController,
                                  decoration: const InputDecoration(
                                    labelText: 'Postal Code',
                                    hintText: 'Optional',
                                    prefixIcon: Icon(Icons.local_post_office),
                                    border: OutlineInputBorder(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                ],
                
                const SizedBox(height: 24),
                
                // Required fields note
                Text(
                  '* Required fields',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
