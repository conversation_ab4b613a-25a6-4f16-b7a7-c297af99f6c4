import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/faq_model.dart';
import 'package:flutter/material.dart';

class FAQService {
  final SupabaseClient _supabase;

  FAQService({required SupabaseClient supabase}) : _supabase = supabase;

  // Get all FAQ categories with their FAQs
  Future<List<FAQCategory>> getAllCategories() async {
    try {
      final response = await _supabase
          .from('faq_categories')
          .select('*, faqs(*)')
          .eq('is_active', true)
          .order('order');

      return (response as List)
          .map((data) => FAQCategory.fromJson(data as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // Return default categories if backend is not available
      return _getDefaultCategories();
    }
  }

  // Get FAQs by category
  Future<List<FAQ>> getFAQsByCategory(String categoryId) async {
    try {
      final response = await _supabase
          .from('faqs')
          .select('*')
          .eq('category_id', categoryId)
          .eq('is_active', true)
          .order('order');

      return (response as List)
          .map((data) => FAQ.fromJson(data as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // Return default FAQs for the category
      return _getDefaultFAQsForCategory(categoryId);
    }
  }

  // Search FAQs by query
  Future<List<FAQ>> searchFAQs(String query) async {
    try {
      final response = await _supabase
          .from('faqs')
          .select('*')
          .or('question.ilike.%$query%,answer.ilike.%$query%,tags.cs.{$query}')
          .eq('is_active', true)
          .order('order');

      return (response as List)
          .map((data) => FAQ.fromJson(data as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // Return empty list if search fails
      return [];
    }
  }

  // Get popular/featured FAQs
  Future<List<FAQ>> getPopularFAQs({int limit = 5}) async {
    try {
      final response = await _supabase
          .from('faqs')
          .select('*')
          .eq('is_active', true)
          .order('order')
          .limit(limit);

      return (response as List)
          .map((data) => FAQ.fromJson(data as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // Return default popular FAQs
      return _getDefaultPopularFAQs();
    }
  }

  // Get FAQ by ID
  Future<FAQ?> getFAQById(String id) async {
    try {
      final response = await _supabase
          .from('faqs')
          .select('*')
          .eq('id', id)
          .eq('is_active', true)
          .single();

      return FAQ.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Get category by ID
  Future<FAQCategory?> getCategoryById(String id) async {
    try {
      final response = await _supabase
          .from('faq_categories')
          .select('*, faqs(*)')
          .eq('id', id)
          .eq('is_active', true)
          .single();

      return FAQCategory.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Submit feedback or question (for future FAQ creation)
  Future<void> submitQuestion({
    required String question,
    required String userEmail,
    String? category,
    String? additionalInfo,
  }) async {
    try {
      await _supabase.from('faq_submissions').insert({
        'question': question,
        'user_email': userEmail,
        'category': category,
        'additional_info': additionalInfo,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Handle error or ignore if table doesn't exist
      rethrow;
    }
  }

  // Default categories and FAQs (fallback data)
  List<FAQCategory> _getDefaultCategories() {
    final now = DateTime.now();

    return [
      FAQCategory(
        id: 'getting-started',
        name: 'Getting Started',
        description: 'Everything you need to know to get started',
        icon: Icons.rocket_launch,
        color: Colors.blue,
        order: 1,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('getting-started'),
      ),
      FAQCategory(
        id: 'orders',
        name: 'Orders & Booking',
        description: 'How to create and manage your orders',
        icon: Icons.shopping_cart,
        color: Colors.green,
        order: 2,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('orders'),
      ),
      FAQCategory(
        id: 'profile',
        name: 'Profile & Account',
        description: 'Managing your profile and account settings',
        icon: Icons.account_circle,
        color: Colors.purple,
        order: 3,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('profile'),
      ),
      FAQCategory(
        id: 'services',
        name: 'Services',
        description: 'Questions about our laundry and dry cleaning services',
        icon: Icons.local_laundry_service,
        color: Colors.teal,
        order: 4,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('services'),
      ),
      FAQCategory(
        id: 'pricing',
        name: 'Pricing & Payment',
        description: 'Questions about pricing and payment',
        icon: Icons.payment,
        color: Colors.orange,
        order: 5,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('pricing'),
      ),
      FAQCategory(
        id: 'delivery',
        name: 'Pickup & Delivery',
        description: 'Questions about pickup and delivery service',
        icon: Icons.delivery_dining,
        color: Colors.indigo,
        order: 6,
        createdAt: now,
        updatedAt: now,
        faqs: _getDefaultFAQsForCategory('delivery'),
      ),
    ];
  }

  List<FAQ> _getDefaultFAQsForCategory(String categoryId) {
    final now = DateTime.now();

    switch (categoryId) {
      case 'getting-started':
        return [
          FAQ(
            id: 'getting-started-1',
            question: 'How do I create an account?',
            answer:
                'Creating an account is easy! Download our mobile app, tap "Sign Up", and provide your email, phone number, and basic information. You\'ll receive an OTP verification code to confirm your account.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: ['account', 'registration', 'signup', 'otp'],
          ),
          FAQ(
            id: 'getting-started-2',
            question: 'What are your operating hours?',
            answer:
                'We operate from 7:00 AM to 10:00 PM, Monday through Sunday. Pickup and delivery services are available during these hours. You can place orders 24/7 through our app.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['hours', 'schedule', 'time', 'availability'],
          ),
          FAQ(
            id: 'getting-started-3',
            question: 'Do I need to download the app?',
            answer:
                'While our mobile app provides the best experience with features like order tracking and push notifications, you can also access our services through our website.',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['app', 'mobile', 'website', 'download'],
          ),
          FAQ(
            id: 'getting-started-4',
            question: 'Is there a minimum order amount?',
            answer:
                'We don\'t have a minimum order amount, but delivery charges may apply for orders under a certain value. Check our pricing section for current delivery fees.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: ['minimum', 'order', 'amount', 'delivery', 'fees'],
          ),
        ];

      case 'orders':
        return [
          FAQ(
            id: 'orders-1',
            question: 'How do I create a new order?',
            answer:
                'To create an order: 1) Open the app and tap "New Order", 2) Select your preferred service (wash, dry clean, etc.), 3) Choose garments from our list or add custom items, 4) Set pickup and delivery addresses, 5) Choose your preferred pickup date and time, 6) Review and confirm your order.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: ['create', 'order', 'new', 'booking', 'steps'],
          ),
          FAQ(
            id: 'orders-2',
            question: 'Can I edit my order after placing it?',
            answer:
                'You can modify your order within 30 minutes of placing it, provided it hasn\'t been picked up yet. Go to "My Orders", select the order, and tap "Edit Order". For orders already picked up, please contact customer support.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['edit', 'modify', 'change', 'order', 'cancel'],
          ),
          FAQ(
            id: 'orders-3',
            question: 'How can I track my order status?',
            answer:
                'Track your order easily by going to "Track Orders" in the app. You\'ll see real-time updates including: Order Confirmed, Picked Up, In Process, Ready for Delivery, and Delivered. You\'ll also receive push notifications for status changes.',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['track', 'status', 'updates', 'notifications'],
          ),
          FAQ(
            id: 'orders-4',
            question: 'What if I need to cancel my order?',
            answer:
                'You can cancel your order free of charge if it hasn\'t been picked up yet. Go to "My Orders", select the order, and tap "Cancel Order". If the order has been picked up, cancellation fees may apply.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: ['cancel', 'cancellation', 'fees', 'refund'],
          ),
          FAQ(
            id: 'orders-5',
            question: 'Can I schedule orders in advance?',
            answer:
                'Yes! You can schedule pickup up to 7 days in advance. Simply select your preferred date and time slot during order creation. This is perfect for planning ahead or ensuring pickup at convenient times.',
            categoryId: categoryId,
            order: 5,
            createdAt: now,
            updatedAt: now,
            tags: ['schedule', 'advance', 'future', 'planning'],
          ),
          FAQ(
            id: 'orders-6',
            question: 'What happens if I\'m not available during pickup?',
            answer:
                'If you\'re not available during the scheduled pickup time, our driver will attempt to contact you. You can reschedule through the app or leave instructions for a safe pickup location. Repeated missed pickups may incur rescheduling fees.',
            categoryId: categoryId,
            order: 6,
            createdAt: now,
            updatedAt: now,
            tags: ['missed', 'pickup', 'reschedule', 'availability'],
          ),
        ];

      case 'profile':
        return [
          FAQ(
            id: 'profile-1',
            question: 'How do I update my profile information?',
            answer:
                'To update your profile: 1) Go to "Profile" in the app menu, 2) Tap "Edit Profile", 3) Update your name, phone number, or other details, 4) Save changes. Your updated information will be used for future orders and communications.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: ['profile', 'update', 'edit', 'information', 'details'],
          ),
          FAQ(
            id: 'profile-2',
            question: 'How do I change my password?',
            answer:
                'To change your password: 1) Go to "Profile" and tap "Change Password", 2) Enter your current password, 3) Enter your new password (minimum 6 characters), 4) Confirm your new password, 5) Tap "Update Password". You\'ll be logged out and need to sign in with your new password.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['password', 'change', 'security', 'update'],
          ),
          FAQ(
            id: 'profile-3',
            question: 'How do I manage my delivery addresses?',
            answer:
                'Manage addresses in "Profile" > "Addresses". You can add multiple addresses (home, office, etc.), set a default address, edit existing ones, or delete addresses you no longer use. This makes ordering faster and more convenient.',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['addresses', 'delivery', 'manage', 'multiple', 'default'],
          ),
          FAQ(
            id: 'profile-4',
            question: 'Can I change my email address?',
            answer:
                'Currently, email addresses cannot be changed directly in the app as they\'re used for account verification. Please contact customer support if you need to update your email address, and they\'ll assist you with the process.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: ['email', 'change', 'support', 'verification'],
          ),
          FAQ(
            id: 'profile-5',
            question: 'How do I delete my account?',
            answer:
                'To delete your account, please contact our customer support team. They\'ll verify your identity and process the deletion request. Note that this action is permanent and will remove all your order history and saved information.',
            categoryId: categoryId,
            order: 5,
            createdAt: now,
            updatedAt: now,
            tags: ['delete', 'account', 'permanent', 'support'],
          ),
          FAQ(
            id: 'profile-6',
            question: 'What notification settings can I control?',
            answer:
                'You can control notifications in "Settings" > "Notifications". Options include order status updates, pickup reminders, delivery notifications, promotional offers, and app updates. Customize these based on your preferences.',
            categoryId: categoryId,
            order: 6,
            createdAt: now,
            updatedAt: now,
            tags: ['notifications', 'settings', 'control', 'preferences'],
          ),
        ];

      case 'services':
        return [
          FAQ(
            id: 'services-1',
            question: 'What types of clothing do you clean?',
            answer:
                'We clean all types of clothing including everyday wear, business attire, formal wear, delicate items, and specialty garments. We also offer specialized cleaning for leather, suede, wedding dresses, and other premium items.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: ['clothing', 'items', 'types', 'specialty', 'delicate'],
          ),
          FAQ(
            id: 'services-2',
            question: 'Do you offer same-day service?',
            answer:
                'Yes, we offer same-day service for orders placed before 10:00 AM and picked up before 12:00 PM. Additional express charges apply. Subject to availability and service type.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['same-day', 'express', 'rush', 'quick', 'timing'],
          ),
          FAQ(
            id: 'services-3',
            question: 'What\'s the difference between wash and dry clean?',
            answer:
                'Washing uses water and detergent for everyday items like cotton, casual wear, and machine-washable fabrics. Dry cleaning uses special solvents for delicate items, suits, silk, wool, and garments that can\'t be washed with water.',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['wash', 'dry-clean', 'difference', 'methods', 'fabrics'],
          ),
          FAQ(
            id: 'services-4',
            question: 'Do you provide ironing and pressing services?',
            answer:
                'Yes, professional ironing and pressing are included with all our cleaning services. We also offer standalone pressing services for clean garments that just need a fresh, crisp finish.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: ['ironing', 'pressing', 'finishing', 'crisp'],
          ),
        ];

      case 'pricing':
        return [
          FAQ(
            id: 'pricing-1',
            question: 'How is pricing calculated?',
            answer:
                'Pricing is based on the type of garment and cleaning service required. We offer transparent, per-item pricing with no hidden fees. You can see the exact cost breakdown before confirming your order.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: [
              'pricing',
              'cost',
              'calculation',
              'transparent',
              'breakdown',
            ],
          ),
          FAQ(
            id: 'pricing-2',
            question: 'What payment methods do you accept?',
            answer:
                'We accept all major credit cards, debit cards, mobile money payments (M-Pesa, Airtel Money), and cash on delivery. All card payments are processed securely through our encrypted payment system.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['payment', 'methods', 'cards', 'mobile-money', 'secure'],
          ),
          FAQ(
            id: 'pricing-3',
            question: 'Are there any additional fees?',
            answer:
                'Standard pricing includes cleaning and basic packaging. Additional fees may apply for: express service, special stain removal, extra delicate handling, or delivery outside our standard zones. All fees are clearly shown before order confirmation.',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['fees', 'additional', 'express', 'delivery', 'transparent'],
          ),
          FAQ(
            id: 'pricing-4',
            question: 'Do you offer discounts or loyalty programs?',
            answer:
                'Yes! We offer discounts for bulk orders, regular customers, and special promotions. Our loyalty program rewards frequent users with points that can be redeemed for discounts on future orders.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: ['discounts', 'loyalty', 'programs', 'bulk', 'promotions'],
          ),
        ];

      case 'delivery':
        return [
          FAQ(
            id: 'delivery-1',
            question: 'What areas do you deliver to?',
            answer:
                'We currently deliver to all areas within the city limits and selected suburbs. Check our coverage map in the app to confirm service availability in your area. We\'re constantly expanding our delivery zones.',
            categoryId: categoryId,
            order: 1,
            createdAt: now,
            updatedAt: now,
            tags: ['delivery', 'areas', 'coverage', 'zones', 'map'],
          ),
          FAQ(
            id: 'delivery-2',
            question: 'How do I schedule a pickup?',
            answer:
                'Schedule pickup during order creation by selecting your preferred date and time slot. You can also schedule through "My Orders" for existing orders. Choose from available morning, afternoon, or evening slots.',
            categoryId: categoryId,
            order: 2,
            createdAt: now,
            updatedAt: now,
            tags: ['pickup', 'schedule', 'booking', 'time-slots', 'flexible'],
          ),
          FAQ(
            id: 'delivery-3',
            question: 'What are the delivery timeframes?',
            answer:
                'Standard delivery is 24-48 hours from pickup. Express service offers same-day delivery (if picked up before 12 PM). Delivery windows are: Morning (8-12 PM), Afternoon (12-5 PM), Evening (5-8 PM).',
            categoryId: categoryId,
            order: 3,
            createdAt: now,
            updatedAt: now,
            tags: ['delivery', 'timeframes', 'standard', 'express', 'windows'],
          ),
          FAQ(
            id: 'delivery-4',
            question: 'Can someone else receive my delivery?',
            answer:
                'Yes, you can authorize someone else to receive your delivery. Add their name and phone number in the delivery instructions, or notify us in advance. They may be asked to show ID for verification.',
            categoryId: categoryId,
            order: 4,
            createdAt: now,
            updatedAt: now,
            tags: [
              'delivery',
              'authorization',
              'someone-else',
              'verification',
              'instructions',
            ],
          ),
        ];

      default:
        return [];
    }
  }

  List<FAQ> _getDefaultPopularFAQs() {
    final now = DateTime.now();

    return [
      FAQ(
        id: 'popular-1',
        question: 'How do I create a new order?',
        answer:
            'To create an order: 1) Open the app and tap "New Order", 2) Select your preferred service (wash, dry clean, etc.), 3) Choose garments from our list or add custom items, 4) Set pickup and delivery addresses, 5) Choose your preferred pickup date and time, 6) Review and confirm your order.',
        categoryId: 'orders',
        order: 1,
        createdAt: now,
        updatedAt: now,
        tags: ['create', 'order', 'new', 'popular'],
      ),
      FAQ(
        id: 'popular-2',
        question: 'How do I update my profile information?',
        answer:
            'To update your profile: 1) Go to "Profile" in the app menu, 2) Tap "Edit Profile", 3) Update your name, phone number, or other details, 4) Save changes. Your updated information will be used for future orders and communications.',
        categoryId: 'profile',
        order: 2,
        createdAt: now,
        updatedAt: now,
        tags: ['profile', 'update', 'popular'],
      ),
      FAQ(
        id: 'popular-3',
        question: 'How can I track my order status?',
        answer:
            'Track your order easily by going to "Track Orders" in the app. You\'ll see real-time updates including: Order Confirmed, Picked Up, In Process, Ready for Delivery, and Delivered. You\'ll also receive push notifications for status changes.',
        categoryId: 'orders',
        order: 3,
        createdAt: now,
        updatedAt: now,
        tags: ['track', 'status', 'popular'],
      ),
      FAQ(
        id: 'popular-4',
        question: 'What payment methods do you accept?',
        answer:
            'We accept all major credit cards, debit cards, mobile money payments (M-Pesa, Airtel Money), and cash on delivery. All card payments are processed securely through our encrypted payment system.',
        categoryId: 'pricing',
        order: 4,
        createdAt: now,
        updatedAt: now,
        tags: ['payment', 'methods', 'popular'],
      ),
      FAQ(
        id: 'popular-5',
        question: 'Do you offer same-day service?',
        answer:
            'Yes, we offer same-day service for orders placed before 10:00 AM and picked up before 12:00 PM. Additional express charges apply. Subject to availability and service type.',
        categoryId: 'services',
        order: 5,
        createdAt: now,
        updatedAt: now,
        tags: ['same-day', 'express', 'popular'],
      ),
    ];
  }
}
