import 'service_model.dart';
import 'store_model.dart';
import 'customer_model.dart';
import 'garment_model.dart';
import 'invoice_model.dart';

enum OrderStatus {
  pending,
  accepted,
  pickedUp,
  inProcess,
  readyForPickup,
  outForDelivery,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.accepted:
        return 'Accepted';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.inProcess:
        return 'In Process';
      case OrderStatus.readyForPickup:
        return 'Ready for Pickup';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value {
    switch (this) {
      case OrderStatus.pending:
        return 'pending';
      case OrderStatus.accepted:
        return 'accepted';
      case OrderStatus.pickedUp:
        return 'picked_up';
      case OrderStatus.inProcess:
        return 'in_process';
      case OrderStatus.readyForPickup:
        return 'ready_for_pickup';
      case OrderStatus.outForDelivery:
        return 'out_for_delivery';
      case OrderStatus.completed:
        return 'completed';
      case OrderStatus.cancelled:
        return 'cancelled';
    }
  }

  static OrderStatus fromString(String value) {
    switch (value) {
      case 'pending':
        return OrderStatus.pending;
      case 'accepted':
        return OrderStatus.accepted;
      case 'picked_up':
        return OrderStatus.pickedUp;
      case 'in_process':
        return OrderStatus.inProcess;
      case 'ready_for_pickup':
        return OrderStatus.readyForPickup;
      case 'out_for_delivery':
        return OrderStatus.outForDelivery;
      case 'completed':
        return OrderStatus.completed;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }

  // Alias for compatibility with test service
  String toDbString() => value;
}

class Order {
  final String id;
  final String orderNumber;
  final String userId;
  final String? storeId;
  final String serviceId;
  final OrderStatus status;
  final String? pickupAddressId;
  final String? deliveryAddressId;
  final DateTime? pickupDate;
  final String? pickupTimeSlot;
  final DateTime? deliveryDate;
  final String? deliveryTimeSlot;
  final double subtotal;
  final double taxAmount;
  final double deliveryFee;
  final double discountAmount;
  final double totalAmount;
  final bool payOnDelivery;
  final String? specialInstructions;
  final String? assignedAgentId;
  final String? cancelledBy;
  final String? cancelReason;
  final DateTime? cancelledAt;
  final String? deletedBy;
  final String? deleteReason;
  final DateTime? deletedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Related objects
  final Service? service;
  final Store? store;
  final Customer? customer;
  final List<OrderItem> items;
  final Invoice? invoice;

  Order({
    required this.id,
    required this.orderNumber,
    required this.userId,
    this.storeId,
    required this.serviceId,
    required this.status,
    this.pickupAddressId,
    this.deliveryAddressId,
    this.pickupDate,
    this.pickupTimeSlot,
    this.deliveryDate,
    this.deliveryTimeSlot,
    required this.subtotal,
    required this.taxAmount,
    required this.deliveryFee,
    required this.discountAmount,
    required this.totalAmount,
    required this.payOnDelivery,
    this.specialInstructions,
    this.assignedAgentId,
    this.cancelledBy,
    this.cancelReason,
    this.cancelledAt,
    this.deletedBy,
    this.deleteReason,
    this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
    this.service,
    this.store,
    this.customer,
    this.items = const [],
    this.invoice,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      userId: json['user_id'] as String,
      storeId: json['store_id'] as String?,
      serviceId: json['service_id'] as String,
      status: OrderStatus.fromString(json['status'] as String),
      pickupAddressId: json['pickup_address_id'] as String?,
      deliveryAddressId: json['delivery_address_id'] as String?,
      pickupDate: json['pickup_date'] != null 
          ? DateTime.parse(json['pickup_date'] as String) 
          : null,
      pickupTimeSlot: json['pickup_time_slot'] as String?,
      deliveryDate: json['delivery_date'] != null 
          ? DateTime.parse(json['delivery_date'] as String) 
          : null,
      deliveryTimeSlot: json['delivery_time_slot'] as String?,
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['tax_amount'] as num?)?.toDouble() ?? 0.0,
      deliveryFee: (json['delivery_fee'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      payOnDelivery: json['pay_on_delivery'] as bool? ?? false,
      specialInstructions: json['special_instructions'] as String?,
      assignedAgentId: json['assigned_agent_id'] as String?,
      cancelledBy: json['cancelled_by'] as String?,
      cancelReason: json['cancel_reason'] as String?,
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at'] as String) 
          : null,
      deletedBy: json['deleted_by'] as String?,
      deleteReason: json['delete_reason'] as String?,
      deletedAt: json['deleted_at'] != null 
          ? DateTime.parse(json['deleted_at'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      service: json['service'] != null 
          ? Service.fromJson(json['service'] as Map<String, dynamic>) 
          : null,
      store: json['store'] != null 
          ? Store.fromJson(json['store'] as Map<String, dynamic>) 
          : null,
      customer: json['customer'] != null 
          ? Customer.fromJson(json['customer'] as Map<String, dynamic>) 
          : null,
      items: json['items'] != null 
          ? (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList()
          : [],
      invoice: json['invoice'] != null 
          ? Invoice.fromJson(json['invoice'] as Map<String, dynamic>) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'user_id': userId,
      'store_id': storeId,
      'service_id': serviceId,
      'status': status.value,
      'pickup_address_id': pickupAddressId,
      'delivery_address_id': deliveryAddressId,
      'pickup_date': pickupDate?.toIso8601String().split('T')[0],
      'pickup_time_slot': pickupTimeSlot,
      'delivery_date': deliveryDate?.toIso8601String().split('T')[0],
      'delivery_time_slot': deliveryTimeSlot,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'delivery_fee': deliveryFee,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'pay_on_delivery': payOnDelivery,
      'special_instructions': specialInstructions,
      'assigned_agent_id': assignedAgentId,
      'cancelled_by': cancelledBy,
      'cancel_reason': cancelReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'deleted_by': deletedBy,
      'delete_reason': deleteReason,
      'deleted_at': deletedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'service': service?.toJson(),
      'store': store?.toJson(),
      'customer': customer?.toJson(),
      'items': items.map((item) => item.toJson()).toList(),
      'invoice': invoice?.toJson(),
    };
  }

  // Helper getters
  bool get isActive => status != OrderStatus.completed && status != OrderStatus.cancelled;
  bool get canCancel => status == OrderStatus.pending || status == OrderStatus.accepted;
  bool get canDelete => status == OrderStatus.cancelled;
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);
  
  // Payment-related getters
  PaymentStatus get paymentStatus => invoice?.paymentStatus ?? PaymentStatus.pending;
  bool get isPaid => paymentStatus == PaymentStatus.paid;
  bool get hasOutstandingPayment => paymentStatus == PaymentStatus.pending || paymentStatus == PaymentStatus.partial;
  bool get isOverdue => invoice?.isOverdue ?? false;
  String? get invoiceNumber => invoice?.invoiceNumber;
  double get amountPaid => invoice?.totalPaid ?? 0.0;
  double get outstandingAmount => invoice?.outstandingAmount ?? totalAmount;
  
  String get paymentStatusDisplay {
    if (isOverdue) return 'Overdue';
    return paymentStatus.displayName;
  }

  String get statusIcon {
    switch (status) {
      case OrderStatus.pending:
        return '⏳';
      case OrderStatus.accepted:
        return '✅';
      case OrderStatus.pickedUp:
        return '📦';
      case OrderStatus.inProcess:
        return '🔄';
      case OrderStatus.readyForPickup:
        return '✨';
      case OrderStatus.outForDelivery:
        return '🚚';
      case OrderStatus.completed:
        return '🎉';
      case OrderStatus.cancelled:
        return '❌';
    }
  }
}
