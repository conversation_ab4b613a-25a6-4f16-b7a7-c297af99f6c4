import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../providers/auth_provider.dart';
import '../../providers/staff_auth_provider.dart';
import '../../models/order_model.dart';
import '../../models/store_model.dart';
import '../../services/order_service.dart';
import '../../services/store_service.dart';
import '../../widgets/update_status_dialog.dart';
import 'order_details_screen.dart';

enum SortOption {
  dateNewest,
  dateOldest,
  statusAsc,
  statusDesc,
  totalAmountAsc,
  totalAmountDesc,
  customerNameAsc,
  customerNameDesc,
}

class ActiveOrdersScreen extends StatefulWidget {
  const ActiveOrdersScreen({super.key});

  @override
  State<ActiveOrdersScreen> createState() => _ActiveOrdersScreenState();
}

class _ActiveOrdersScreenState extends State<ActiveOrdersScreen> {
  late final OrderService _orderService;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedStatus = 'all';
  String _selectedStoreId = 'all';
  SortOption _selectedSort = SortOption.dateNewest;
  List<Order> _orders = [];
  List<Order> _filteredOrders = [];
  List<Store> _stores = [];
  bool _isLoading = true;
  bool _isLoadingStores = false;
  String? _error;

  // Only active status options
  final List<String> _activeStatusOptions = [
    'all',
    'pending',
    'accepted',
    'picked_up',
    'in_process',
    'ready_for_pickup',
    'out_for_delivery',
  ];

  // Valid active statuses for filtering
  final List<String> _validActiveStatuses = [
    'pending',
    'accepted',
    'picked_up',
    'in_process',
    'ready_for_pickup',
    'out_for_delivery',
  ];

  @override
  void initState() {
    super.initState();
    _orderService = OrderService(supabase: Supabase.instance.client);
    _loadActiveOrders();
    _loadStores();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh orders when dependencies change (e.g., when returning from other screens)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _orders.isNotEmpty) {
        _refreshActiveOrders();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadStores() async {
    try {
      setState(() {
        _isLoadingStores = true;
      });

      final stores = await StoreService.getAllStores();
      setState(() {
        _stores = stores;
        _isLoadingStores = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingStores = false;
      });
    }
  }

  Future<void> _loadActiveOrders() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Get all orders for any authenticated user (as per database RLS policies)
      final allOrders = await _orderService.getAllOrders();

      // Filter to only active orders - more comprehensive filtering
      final activeOrders = allOrders
          .where((order) => _isOrderActive(order))
          .toList();

      // Debug logging
      debugPrint('ActiveOrdersScreen: Loaded ${allOrders.length} total orders');
      debugPrint(
        'ActiveOrdersScreen: Filtered to ${activeOrders.length} active orders',
      );
      if (activeOrders.isNotEmpty) {
        debugPrint(
          'ActiveOrdersScreen: Active order statuses: ${activeOrders.map((o) => o.status.value).toSet()}',
        );
      }

      setState(() {
        _orders = activeOrders;
        _applyFiltersAndSort();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// Refresh active orders - useful for when returning from other screens
  Future<void> _refreshActiveOrders() async {
    await _loadActiveOrders();
  }

  /// Check if an order is truly active
  bool _isOrderActive(Order order) {
    // Check if order is active using the model's isActive property
    if (!order.isActive) return false;

    // Additional validation: ensure status is not completed or cancelled
    if (order.status == OrderStatus.completed ||
        order.status == OrderStatus.cancelled) {
      return false;
    }

    // Ensure the order has a valid status that indicates it's still in progress
    return _validActiveStatuses.contains(order.status.value);
  }

  void _applyFiltersAndSort() {
    List<Order> result = _orders;

    // Filter by status
    if (_selectedStatus != 'all') {
      result = result
          .where((order) => order.status.value == _selectedStatus)
          .toList();
    }

    // Filter by store
    if (_selectedStoreId != 'all') {
      result = result
          .where((order) => order.storeId == _selectedStoreId)
          .toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      final q = _searchQuery.toLowerCase();
      result = result.where((o) {
        final numMatch = o.orderNumber.toLowerCase().contains(q);
        final serviceMatch =
            (o.service?.name.toLowerCase().contains(q) ?? false);
        final storeMatch = (o.store?.name.toLowerCase().contains(q) ?? false);
        final customerMatch =
            (o.customer?.fullName.toLowerCase().contains(q) ?? false);
        return numMatch || serviceMatch || storeMatch || customerMatch;
      }).toList();
    }

    // Apply sorting
    switch (_selectedSort) {
      case SortOption.dateNewest:
        result.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.dateOldest:
        result.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case SortOption.statusAsc:
        result.sort(
          (a, b) => a.status.displayName.compareTo(b.status.displayName),
        );
        break;
      case SortOption.statusDesc:
        result.sort(
          (a, b) => b.status.displayName.compareTo(a.status.displayName),
        );
        break;
      case SortOption.totalAmountAsc:
        result.sort((a, b) => a.totalAmount.compareTo(b.totalAmount));
        break;
      case SortOption.totalAmountDesc:
        result.sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
        break;
      case SortOption.customerNameAsc:
        result.sort((a, b) {
          final aName = a.customer?.fullName ?? '';
          final bName = b.customer?.fullName ?? '';
          return aName.compareTo(bName);
        });
        break;
      case SortOption.customerNameDesc:
        result.sort((a, b) {
          final aName = a.customer?.fullName ?? '';
          final bName = b.customer?.fullName ?? '';
          return bName.compareTo(aName);
        });
        break;
    }

    _filteredOrders = result;
  }

  String _getSortDisplayName(SortOption option) {
    switch (option) {
      case SortOption.dateNewest:
        return 'Date (Newest First)';
      case SortOption.dateOldest:
        return 'Date (Oldest First)';
      case SortOption.statusAsc:
        return 'Status (A-Z)';
      case SortOption.statusDesc:
        return 'Status (Z-A)';
      case SortOption.totalAmountAsc:
        return 'Amount (Low to High)';
      case SortOption.totalAmountDesc:
        return 'Amount (High to Low)';
      case SortOption.customerNameAsc:
        return 'Customer (A-Z)';
      case SortOption.customerNameDesc:
        return 'Customer (Z-A)';
    }
  }

  void _showSortBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Sort Orders By',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ...SortOption.values.map((option) {
                return ListTile(
                  leading: Radio<SortOption>(
                    value: option,
                    groupValue: _selectedSort,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSort = value;
                          _applyFiltersAndSort();
                        });
                        Navigator.pop(context);
                      }
                    },
                  ),
                  title: Text(_getSortDisplayName(option)),
                  onTap: () {
                    setState(() {
                      _selectedSort = option;
                      _applyFiltersAndSort();
                    });
                    Navigator.pop(context);
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Active Orders'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(56),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: TextField(
              controller: _searchController,
              onChanged: (v) {
                setState(() {
                  _searchQuery = v;
                  _applyFiltersAndSort();
                });
              },
              decoration: InputDecoration(
                hintText: 'Search by order number, service, store, or customer',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                            _applyFiltersAndSort();
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
        ),
        actions: [
          // Sort button
          IconButton(
            onPressed: _showSortBottomSheet,
            icon: const Icon(Icons.sort),
            tooltip: 'Sort orders',
          ),
          // Refresh button
          IconButton(
            onPressed: _refreshActiveOrders,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh orders',
          ),
        ],
      ),
      body: Consumer2<AuthProvider, StaffAuthProvider>(
        builder: (context, authProvider, staffAuthProvider, child) {
          final isStaff = staffAuthProvider.userType == UserType.staff;

          return Column(
            children: [
              // Active Status Filter Chips
              Container(
                height: 60,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _activeStatusOptions.length,
                  itemBuilder: (context, index) {
                    final status = _activeStatusOptions[index];
                    final isSelected = _selectedStatus == status;

                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(_formatStatusText(status)),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedStatus = status;
                            _applyFiltersAndSort();
                          });
                        },
                        selectedColor: isStaff
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.blue.withValues(alpha: 0.3),
                        checkmarkColor: isStaff ? Colors.green : Colors.blue,
                      ),
                    );
                  },
                ),
              ),

              // Store Filter Dropdown (Staff only)
              if (isStaff)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: _isLoadingStores
                      ? const LinearProgressIndicator()
                      : DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Filter by Store',
                            border: OutlineInputBorder(),
                          ),
                          value: _selectedStoreId,
                          items: [
                            const DropdownMenuItem(
                              value: 'all',
                              child: Text('All Stores'),
                            ),
                            ..._stores.map(
                              (store) => DropdownMenuItem(
                                value: store.id,
                                child: Text(store.name),
                              ),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedStoreId = value!;
                              _applyFiltersAndSort();
                            });
                          },
                        ),
                ),

              // Sort indicator
              if (_selectedSort != SortOption.dateNewest)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  color: Colors.grey[100],
                  child: Row(
                    children: [
                      const Icon(Icons.sort, size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                        'Sorted by: ${_getSortDisplayName(_selectedSort)}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),

              // Orders List
              Expanded(child: _buildOrdersList(isStaff)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildOrdersList(bool isStaff) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading active orders...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading orders',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshActiveOrders,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pending_actions, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No active orders found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _selectedStatus == 'all'
                  ? 'No active orders available at the moment'
                  : 'No active ${_formatStatusText(_selectedStatus).toLowerCase()} orders',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            // Show total orders count for context
            if (_orders.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Total orders in system: ${_orders.length}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshActiveOrders,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshActiveOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredOrders.length,
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return _buildOrderCard(order, isStaff);
        },
      ),
    );
  }

  Widget _buildOrderCard(Order order, bool isStaff) {
    final statusColor = _getStatusColor(order.status.value);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    order.orderNumber,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        order.statusIcon,
                        style: const TextStyle(fontSize: 12),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        order.status.displayName,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Order Details
            Row(
              children: [
                Icon(
                  Icons.local_laundry_service,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Expanded(child: Text(order.service?.name ?? 'Unknown Service')),
                Icon(Icons.attach_money, size: 16, color: Colors.grey[600]),
                Text('\$${order.totalAmount.toStringAsFixed(2)}'),
              ],
            ),

            const SizedBox(height: 8),

            // Customer info (staff only)
            if (isStaff && order.customer != null) ...[
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(order.customer!.fullName),
                ],
              ),
              const SizedBox(height: 8),
            ],

            if (order.pickupDate != null) ...[
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    'Pickup: ${_formatDate(order.pickupDate!)}${order.pickupTimeSlot != null ? ' ${order.pickupTimeSlot}' : ''}',
                  ),
                ],
              ),
            ],

            if (order.deliveryDate != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.local_shipping, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    'Delivery: ${_formatDate(order.deliveryDate!)}${order.deliveryTimeSlot != null ? ' ${order.deliveryTimeSlot}' : ''}',
                  ),
                ],
              ),
            ],

            if (order.store != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.store, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text('Store: ${order.store!.name}'),
                ],
              ),
            ],

            const SizedBox(height: 12),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      _showOrderDetails(order);
                    },
                    child: const Text('View Details'),
                  ),
                ),
                if (isStaff && _canUpdateStatus(order.status.value)) ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _showUpdateStatusDialog(order);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Update Status'),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatStatusText(String status) {
    switch (status) {
      case 'all':
        return 'All Active';
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'picked_up':
        return 'Picked Up';
      case 'in_process':
        return 'In Process';
      case 'ready_for_pickup':
        return 'Ready for Pickup';
      case 'out_for_delivery':
        return 'Out for Delivery';
      default:
        return status.toUpperCase();
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'accepted':
        return Colors.blue;
      case 'picked_up':
        return Colors.purple;
      case 'in_process':
        return Colors.indigo;
      case 'ready_for_pickup':
        return Colors.teal;
      case 'out_for_delivery':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  bool _canUpdateStatus(String status) {
    return status != 'completed' && status != 'cancelled';
  }

  void _showOrderDetails(Order order) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OrderDetailsScreen(orderId: order.id),
      ),
    ).then((_) => _refreshActiveOrders());
  }

  void _showUpdateStatusDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => UpdateStatusDialog(
        order: order,
        onUpdate: (newStatus, notes) =>
            _handleStatusUpdate(order, newStatus, notes),
      ),
    );
  }

  Future<void> _handleStatusUpdate(
    Order order,
    OrderStatus newStatus,
    String? notes,
  ) async {
    try {
      // Validation before accepting orders
      if (newStatus == OrderStatus.accepted &&
          order.status == OrderStatus.pending) {
        final validationResult = _validateOrderForAcceptance(order);
        if (!validationResult['isValid']) {
          if (mounted) {
            _showOrderValidationDialog(order, validationResult['message']);
          }
          return;
        }
      }

      await _orderService.updateOrderStatus(
        orderId: order.id,
        newStatus: newStatus,
        notes: notes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
        _refreshActiveOrders(); // Refresh the list
      }
    } catch (e) {
      rethrow; // Let the dialog handle the error display
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Validates if an order can be accepted
  Map<String, dynamic> _validateOrderForAcceptance(Order order) {
    // Check if order has items
    if (order.items.isEmpty) {
      return {
        'isValid': false,
        'message':
            'This order has no items added yet. Items, quantities, and weights must be added before accepting the order.',
        'showEditOption': true,
      };
    }

    // Check if items have valid quantities and prices
    for (final item in order.items) {
      if (item.quantity <= 0) {
        return {
          'isValid': false,
          'message':
              'Order contains items with invalid quantities. Please update item quantities before accepting.',
          'showEditOption': true,
        };
      }
      if (item.unitPrice <= 0) {
        return {
          'isValid': false,
          'message':
              'Order contains items with invalid pricing. Please update item prices before accepting.',
          'showEditOption': true,
        };
      }
    }

    // Check if order has a valid total amount
    if (order.totalAmount <= 0) {
      return {
        'isValid': false,
        'message':
            'Order has an invalid total amount. Please update item quantities and pricing.',
        'showEditOption': true,
      };
    }

    return {'isValid': true};
  }

  /// Shows validation dialog when order cannot be accepted
  void _showOrderValidationDialog(Order order, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.warning_outlined,
                color: Colors.orange,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Order Cannot Be Accepted',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 18,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Issue Found',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      message,
                      style: TextStyle(fontSize: 14, color: Colors.orange[800]),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Order Details Preview
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.receipt_long,
                          size: 16,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Order ${order.orderNumber}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildOrderSummaryRow(
                      'Items:',
                      '${order.items.length} items',
                    ),
                    _buildOrderSummaryRow(
                      'Total Amount:',
                      '\$${order.totalAmount.toStringAsFixed(2)}',
                    ),
                    _buildOrderSummaryRow('Status:', order.status.displayName),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditOrder(order);
            },
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('Edit Order'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to order edit screen
  void _navigateToEditOrder(Order order) async {
    // Import statement would be added at the top
    // For now, we'll show a simple edit dialog or navigate to an edit page
    _showOrderItemsEditDialog(order);
  }

  /// Shows a simplified order items edit dialog
  void _showOrderItemsEditDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.edit, color: Colors.blue, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Edit Order Items',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.blue,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Order Management Placeholder',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.blue[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'This feature will allow staff to:\n'
                      '• Add items to the order\n'
                      '• Set quantities and weights\n'
                      '• Update pricing and amounts\n'
                      '• Modify service details\n\n'
                      'Implementation of the full order editing interface '
                      'requires integration with the garment selection, '
                      'pricing calculation, and order update systems.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue[700],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Current order summary
              Text(
                'Current Order Status',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    _buildOrderSummaryRow('Order Number:', order.orderNumber),
                    _buildOrderSummaryRow(
                      'Service:',
                      order.service?.name ?? 'Unknown',
                    ),
                    _buildOrderSummaryRow(
                      'Items Count:',
                      '${order.items.length}',
                    ),
                    _buildOrderSummaryRow(
                      'Total Amount:',
                      '\$${order.totalAmount.toStringAsFixed(2)}',
                    ),
                    _buildOrderSummaryRow('Status:', order.status.displayName),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // TODO: Navigate to proper edit screen
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Order editing interface coming soon!',
                            ),
                            backgroundColor: Colors.blue,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Open Editor'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
