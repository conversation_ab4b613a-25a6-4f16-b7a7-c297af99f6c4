import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../providers/auth_provider.dart';
import 'otp_verification_screen.dart';

class OtpLoginScreen extends StatefulWidget {
  const OtpLoginScreen({super.key});

  @override
  State<OtpLoginScreen> createState() => _OtpLoginScreenState();
}

class _OtpLoginScreenState extends State<OtpLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  bool _isStaffId = false;
  bool _isValidating = false;
  String _validationMessage = '';
  String? _staffEmail;

  @override
  void initState() {
    super.initState();
    // Listen to identifier changes to detect staff ID
    _identifierController.addListener(_onIdentifierChanged);
  }

  @override
  void dispose() {
    _identifierController.dispose();
    super.dispose();
  }

  void _onIdentifierChanged() {
    final value = _identifierController.text;
    final isStaffId = RegExp(r'^[0-9]{4,}$').hasMatch(value);
    if (isStaffId != _isStaffId) {
      setState(() {
        _isStaffId = isStaffId;
        _validationMessage = '';
        _staffEmail = null;
      });
    }
  }

  Future<void> _validateStaffId() async {
    if (_identifierController.text.isEmpty || !_isStaffId) return;

    setState(() {
      _isValidating = true;
      _validationMessage = '';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final staffId = int.tryParse(_identifierController.text);

      if (staffId != null) {
        final staffInfo = await authProvider.validateStaffId(staffId);

        if (staffInfo != null) {
          final fullName = staffInfo['full_name'] as String? ?? '';
          final truncatedName = fullName.length > 3 
              ? '${fullName.substring(0, 3)}***' 
              : fullName;
          
          setState(() {
            _staffEmail = staffInfo['email'] as String?;
            _validationMessage = '✓ Staff found: $truncatedName';
          });
        } else {
          setState(() {
            _validationMessage = '✗ Staff ID not found';
            _staffEmail = null;
          });
        }
      } else {
        setState(() {
          _validationMessage = '✗ Invalid Staff ID format';
          _staffEmail = null;
        });
      }
    } catch (e) {
      setState(() {
        _validationMessage = '✗ Validation failed';
        _staffEmail = null;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isValidating = false;
        });
      }
    }
  }

  Future<void> _handleSendOtp() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Determine the email to use for OTP
      String emailToUse;
      if (_isStaffId && _staffEmail != null) {
        emailToUse = _staffEmail!;
      } else {
        emailToUse = _identifierController.text.trim();
      }
      
      final success = await authProvider.signInWithOtp(
        email: emailToUse,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isStaffId 
              ? 'OTP sent to staff email!' 
              : 'OTP sent to your email!'),
            backgroundColor: Colors.green,
          ),
        );
        
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              email: emailToUse,
              otpType: OtpType.magiclink,
              title: _isStaffId ? 'Staff Login Verification' : 'Login Verification',
              subtitle: _isStaffId 
                ? 'Enter the OTP sent to your staff email to login'
                : 'Enter the OTP sent to your email to login',
              staffId: _isStaffId ? int.tryParse(_identifierController.text) : null,
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage ?? 'Failed to send OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login with OTP'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Icon(
                    Icons.security,
                    size: 80,
                    color: _isStaffId ? Colors.orange : Colors.blue,
                  ),
                  const SizedBox(height: 32),
                  Text(
                    _isStaffId ? 'Staff OTP Login' : 'Login with OTP',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isStaffId 
                      ? 'Enter your staff ID and we\'ll send an OTP to your registered email.'
                      : 'Enter your email address and we\'ll send you a one-time password to login.',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  // Input type indicator
                  if (_identifierController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _isStaffId ? Colors.orange.shade100 : Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: _isStaffId ? Colors.orange : Colors.blue,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isStaffId ? Icons.badge : Icons.email,
                            size: 16,
                            color: _isStaffId ? Colors.orange : Colors.blue,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _isStaffId ? 'Staff OTP Login' : 'Customer OTP Login',
                            style: TextStyle(
                              fontSize: 12,
                              color: _isStaffId ? Colors.orange.shade700 : Colors.blue.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  TextFormField(
                    controller: _identifierController,
                    keyboardType: TextInputType.text,
                    decoration: InputDecoration(
                      labelText: 'Email or Staff ID',
                      hintText: 'Enter your email or staff ID',
                      prefixIcon: Icon(
                        _isStaffId ? Icons.badge : Icons.email,
                        color: _isStaffId ? Colors.orange : Colors.blue,
                      ),
                      suffixIcon: _isValidating
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    _isStaffId ? Colors.orange : Colors.blue,
                                  ),
                                ),
                              ),
                            )
                          : _identifierController.text.isNotEmpty && _validationMessage.isNotEmpty
                              ? Icon(
                                  _validationMessage.startsWith('✓')
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: _validationMessage.startsWith('✓')
                                      ? Colors.green
                                      : Colors.red,
                                )
                              : null,
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: _isStaffId ? Colors.orange : Colors.blue,
                          width: 2,
                        ),
                      ),
                    ),
                    onChanged: (value) {
                      // Debounce validation for staff ID
                      final isStaffIdFormat = RegExp(r'^[0-9]{4,}$').hasMatch(value);
                      if (isStaffIdFormat && value.length >= 4) {
                        Future.delayed(const Duration(milliseconds: 1000), () {
                          if (_identifierController.text == value && value.length >= 4) {
                            _validateStaffId();
                          }
                        });
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email or staff ID';
                      }
                      
                      // Check if it's a staff ID (numeric, 4+ digits) or email format
                      final isStaffId = RegExp(r'^[0-9]{4,}$').hasMatch(value);
                      final isEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);

                      if (!isStaffId && !isEmail) {
                        return 'Please enter a valid email or staff ID';
                      }

                      // For staff ID, check if validation was successful
                      if (isStaffId && _staffEmail == null) {
                        return 'Please wait for staff ID validation';
                      }
                      
                      return null;
                    },
                  ),
                  // Validation message
                  if (_validationMessage.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, left: 12.0),
                      child: Row(
                        children: [
                          Icon(
                            _validationMessage.startsWith('✓')
                                ? Icons.check_circle_outline
                                : Icons.error_outline,
                            size: 16,
                            color: _validationMessage.startsWith('✓')
                                ? Colors.green
                                : Colors.red,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              _validationMessage,
                              style: TextStyle(
                                fontSize: 12,
                                color: _validationMessage.startsWith('✓')
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: (authProvider.status == AuthStatus.loading || _isValidating) 
                        ? null 
                        : _handleSendOtp,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isStaffId ? Colors.orange : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: (authProvider.status == AuthStatus.loading || _isValidating)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                _isValidating ? 'Validating...' : 'Sending OTP...',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            _isStaffId ? 'Send OTP to Staff Email' : 'Send OTP',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Back to Login'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
