import 'package:flutter/material.dart';
import '../../models/customer_model.dart';
import '../../models/country_model.dart';
import '../../services/customer_service.dart';
import '../../widgets/country_picker_dialog.dart';

class CustomerProfileScreen extends StatefulWidget {
  final Customer? customer; // Optional - if null, will load current user's profile
  
  const CustomerProfileScreen({super.key, this.customer});

  @override
  State<CustomerProfileScreen> createState() => _CustomerProfileScreenState();
}

class _CustomerProfileScreenState extends State<CustomerProfileScreen> {
  late Customer _customer;
  bool _isLoading = false;
  bool _isEditing = false;
  
  // Form controllers
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  final _preferredDetergentController = TextEditingController();
  final _preferredFabricSoftenerController = TextEditingController();
  final _specialInstructionsController = TextEditingController();
  
  // Form values
  Country? _selectedCountry;
  String _selectedGender = 'prefer_not_to_say';
  String _selectedTheme = 'system';
  String _selectedCurrency = 'USD';
  String _selectedLanguage = 'en';
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  bool _pushNotifications = true;
  bool _ecoFriendly = false;
  
  // Address management
  List<CustomerAddress> _addresses = [];
  bool _loadingAddresses = false;

  @override
  void initState() {
    super.initState();
    _initializeCustomer();
  }

  Future<void> _initializeCustomer() async {
    setState(() => _isLoading = true);
    try {
      if (widget.customer != null) {
        _customer = widget.customer!;
      } else {
        // Load current user's customer profile
        final currentCustomer = await CustomerService.getCurrentUserCustomer();
        if (currentCustomer != null) {
          _customer = currentCustomer;
        } else {
          // Create a default customer object for new users
          _customer = Customer(
            id: '',
            fullName: '',
            email: null,
            phone: null,
            country: null,
            notes: null,
            isActive: true,
            createdBy: null,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        }
      }
      _initializeForm();
      _loadAddresses();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading customer profile: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _initializeForm() {
    _fullNameController.text = _customer.fullName;
    _emailController.text = _customer.email ?? '';
    _phoneController.text = _customer.phone ?? '';
    _notesController.text = _customer.notes ?? '';
    _dateOfBirthController.text = _customer.dateOfBirth ?? '';
    _preferredDetergentController.text = _customer.preferredDetergent ?? '';
    _preferredFabricSoftenerController.text = _customer.preferredFabricSoftener ?? '';
    _specialInstructionsController.text = _customer.specialInstructions ?? '';
    
    _selectedGender = _customer.gender ?? 'prefer_not_to_say';
    _selectedTheme = _customer.theme;
    _selectedCurrency = _customer.preferredCurrency;
    _selectedLanguage = _customer.preferredLanguage ?? 'en';
    _emailNotifications = _customer.emailNotifications;
    _smsNotifications = _customer.smsNotifications;
    _pushNotifications = _customer.pushNotifications;
    _ecoFriendly = _customer.ecoFriendly;
    
    if (_customer.country != null) {
      // Create a basic country object for display
      _selectedCountry = Country(
        id: '',
        name: _customer.country!,
        code: '',
        flagEmoji: '',
      );
    }
  }

  Future<void> _loadAddresses() async {
    setState(() => _loadingAddresses = true);
    try {
      // Only load addresses if customer has a valid ID
      if (_customer.id.isNotEmpty) {
        final addresses = await CustomerService.getCustomerAddresses(_customer.id);
        setState(() {
          _addresses = addresses;
          _loadingAddresses = false;
        });
      } else {
        setState(() => _loadingAddresses = false);
      }
    } catch (e) {
      setState(() => _loadingAddresses = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading addresses: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _dateOfBirthController.dispose();
    _preferredDetergentController.dispose();
    _preferredFabricSoftenerController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (!_validateForm()) return;
    
    setState(() => _isLoading = true);
    
    try {
      // Use the authenticated user method to create or update profile
      final savedCustomer = await CustomerService.createOrUpdateCurrentUserCustomer(
        fullName: _fullNameController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        country: _selectedCountry?.name,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        dateOfBirth: _dateOfBirthController.text.trim().isEmpty ? null : _dateOfBirthController.text.trim(),
        gender: _selectedGender,
        theme: _selectedTheme,
        preferredLanguage: _selectedLanguage,
        emailNotifications: _emailNotifications,
        smsNotifications: _smsNotifications,
        pushNotifications: _pushNotifications,
        preferredCurrency: _selectedCurrency,
        preferredDetergent: _preferredDetergentController.text.trim().isEmpty ? null : _preferredDetergentController.text.trim(),
        preferredFabricSoftener: _preferredFabricSoftenerController.text.trim().isEmpty ? null : _preferredFabricSoftenerController.text.trim(),
        ecoFriendly: _ecoFriendly,
        specialInstructions: _specialInstructionsController.text.trim().isEmpty ? null : _specialInstructionsController.text.trim(),
      );
      
      setState(() {
        _customer = savedCustomer;
        _isEditing = false;
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _validateForm() {
    if (_fullNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Full name is required')),
      );
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Customer Profile'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 24),
                  _buildPersonalInformation(),
                  const SizedBox(height: 24),
                  _buildPreferences(),
                  const SizedBox(height: 24),
                  _buildLaundryPreferences(),
                  const SizedBox(height: 24),
                  _buildAddresses(),
                  const SizedBox(height: 24),
                  if (_isEditing) _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildProfileHeader() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Colors.blue,
              child: Text(
                _customer.fullName.isNotEmpty 
                    ? _customer.fullName[0].toUpperCase()
                    : '?',
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _customer.fullName.isNotEmpty 
                        ? _customer.fullName 
                        : 'New Customer',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_customer.email != null) ...[
                    Row(
                      children: [
                        Icon(Icons.email, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(_customer.email!),
                      ],
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (_customer.phone != null) ...[
                    Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(_customer.phone!),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInformation() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue[700]),
                const SizedBox(width: 12),
                const Text(
                  'Personal Information',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Full Name
            _buildTextField(
              controller: _fullNameController,
              label: 'Full Name *',
              icon: Icons.person_outline,
              enabled: _isEditing,
            ),
            const SizedBox(height: 16),
            
            // Email
            _buildTextField(
              controller: _emailController,
              label: 'Email',
              icon: Icons.email_outlined,
              enabled: _isEditing,
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            
            // Phone
            _buildTextField(
              controller: _phoneController,
              label: 'Phone',
              icon: Icons.phone_outlined,
              enabled: _isEditing,
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            
            // Date of Birth
            _buildTextField(
              controller: _dateOfBirthController,
              label: 'Date of Birth',
              icon: Icons.calendar_today_outlined,
              enabled: _isEditing,
              onTap: _isEditing ? _selectDateOfBirth : null,
              readOnly: true,
            ),
            const SizedBox(height: 16),
            
            // Gender
            if (_isEditing) ...[
              const Text(
                'Gender',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildGenderChip('male', 'Male'),
                  _buildGenderChip('female', 'Female'),
                  _buildGenderChip('other', 'Other'),
                  _buildGenderChip('prefer_not_to_say', 'Prefer not to say'),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Country
            if (_isEditing) ...[
              const Text(
                'Country',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: _selectCountry,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[400]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.flag_outlined, color: Colors.grey[600]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _selectedCountry != null
                            ? Text(_selectedCountry!.name)
                            : Text(
                                'Select Country',
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                      ),
                      Icon(Icons.arrow_drop_down, color: Colors.grey[600]),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Notes
            _buildTextField(
              controller: _notesController,
              label: 'Notes',
              icon: Icons.note_outlined,
              enabled: _isEditing,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferences() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.orange[700]),
                const SizedBox(width: 12),
                const Text(
                  'Account Preferences',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Theme
            if (_isEditing) ...[
              const Text(
                'Theme',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildThemeChip('light', 'Light'),
                  _buildThemeChip('dark', 'Dark'),
                  _buildThemeChip('system', 'System'),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Currency
            if (_isEditing) ...[
              const Text(
                'Preferred Currency',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildCurrencyChip('USD', 'USD (\$)'),
                  _buildCurrencyChip('EUR', 'EUR (€)'),
                  _buildCurrencyChip('GBP', 'GBP (£)'),
                  _buildCurrencyChip('KES', 'KES (KSh)'),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Language
            if (_isEditing) ...[
              const Text(
                'Preferred Language',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildLanguageChip('en', 'English'),
                  _buildLanguageChip('es', 'Español'),
                  _buildLanguageChip('fr', 'Français'),
                  _buildLanguageChip('sw', 'Kiswahili'),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Notification Preferences
            const Text(
              'Notification Preferences',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            CheckboxListTile(
              title: const Text('Email Notifications'),
              value: _emailNotifications,
              onChanged: _isEditing ? (value) => setState(() => _emailNotifications = value ?? false) : null,
              controlAffinity: ListTileControlAffinity.leading,
              activeColor: Colors.blue,
            ),
            CheckboxListTile(
              title: const Text('SMS Notifications'),
              value: _smsNotifications,
              onChanged: _isEditing ? (value) => setState(() => _smsNotifications = value ?? false) : null,
              controlAffinity: ListTileControlAffinity.leading,
              activeColor: Colors.blue,
            ),
            CheckboxListTile(
              title: const Text('Push Notifications'),
              value: _pushNotifications,
              onChanged: _isEditing ? (value) => setState(() => _pushNotifications = value ?? false) : null,
              controlAffinity: ListTileControlAffinity.leading,
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLaundryPreferences() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_laundry_service, color: Colors.green[700]),
                const SizedBox(width: 12),
                const Text(
                  'Laundry Preferences',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Preferred Detergent
            _buildTextField(
              controller: _preferredDetergentController,
              label: 'Preferred Detergent',
              icon: Icons.cleaning_services_outlined,
              enabled: _isEditing,
              hintText: 'e.g., Tide, Ariel, etc.',
            ),
            const SizedBox(height: 16),
            
            // Preferred Fabric Softener
            _buildTextField(
              controller: _preferredFabricSoftenerController,
              label: 'Preferred Fabric Softener',
              icon: Icons.cleaning_services_outlined,
              enabled: _isEditing,
              hintText: 'e.g., Downy, Comfort, etc.',
            ),
            const SizedBox(height: 16),
            
            // Eco-friendly
            CheckboxListTile(
              title: const Text('Eco-friendly Options'),
              subtitle: const Text('Prefer environmentally friendly cleaning products'),
              value: _ecoFriendly,
              onChanged: _isEditing ? (value) => setState(() => _ecoFriendly = value ?? false) : null,
              controlAffinity: ListTileControlAffinity.leading,
              activeColor: Colors.green,
            ),
            const SizedBox(height: 16),
            
            // Special Instructions
            _buildTextField(
              controller: _specialInstructionsController,
              label: 'Special Instructions',
              icon: Icons.note_outlined,
              enabled: _isEditing,
              maxLines: 3,
              hintText: 'Any special care instructions or preferences...',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddresses() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.red[700]),
                const SizedBox(width: 12),
                const Text(
                  'Addresses',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isEditing)
                  TextButton.icon(
                    onPressed: _addNewAddress,
                    icon: const Icon(Icons.add),
                    label: const Text('Add New'),
                  ),
              ],
            ),
            const SizedBox(height: 20),
            
            if (_loadingAddresses)
              const Center(child: CircularProgressIndicator())
            else if (_addresses.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.location_off, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No addresses added yet',
                      style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your first address to get started',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _addresses.length,
                itemBuilder: (context, index) {
                  final address = _addresses[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: address.isDefault ? Colors.blue : Colors.grey[300],
                        child: Icon(
                          address.isDefault ? Icons.home : Icons.location_on,
                          color: address.isDefault ? Colors.white : Colors.grey[600],
                        ),
                      ),
                      title: Row(
                        children: [
                          Text(address.title),
                          if (address.isDefault) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Default',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      subtitle: Text(address.fullAddress),
                      trailing: _isEditing
                          ? PopupMenuButton(
                              itemBuilder: (context) => [
                                PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit, size: 16),
                                      const SizedBox(width: 8),
                                      const Text('Edit'),
                                    ],
                                  ),
                                ),
                                if (!address.isDefault)
                                  PopupMenuItem(
                                    value: 'default',
                                    child: Row(
                                      children: [
                                        Icon(Icons.home, size: 16),
                                        const SizedBox(width: 8),
                                        const Text('Set as Default'),
                                      ],
                                    ),
                                  ),
                                PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, size: 16, color: Colors.red),
                                      const SizedBox(width: 8),
                                      const Text('Delete', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                              onSelected: (value) => _handleAddressAction(value, address),
                            )
                          : null,
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _isEditing = false;
                _initializeForm(); // Reset form to original values
              });
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: Colors.grey),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                  )
                : const Text('Save Changes'),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool enabled,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? hintText,
    VoidCallback? onTap,
    bool readOnly = false,
  }) {
    return TextField(
      controller: controller,
      enabled: enabled,
      keyboardType: keyboardType,
      maxLines: maxLines,
      readOnly: readOnly,
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      onTap: onTap,
    );
  }

  Widget _buildGenderChip(String value, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _selectedGender == value,
      onSelected: (selected) {
        if (selected) {
          setState(() => _selectedGender = value);
        }
      },
      selectedColor: Colors.blue[100],
    );
  }

  Widget _buildThemeChip(String value, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _selectedTheme == value,
      onSelected: (selected) {
        if (selected) {
          setState(() => _selectedTheme = value);
        }
      },
      selectedColor: Colors.orange[100],
    );
  }

  Widget _buildCurrencyChip(String value, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _selectedCurrency == value,
      onSelected: (selected) {
        if (selected) {
          setState(() => _selectedCurrency = value);
        }
      },
      selectedColor: Colors.green[100],
    );
  }

  Widget _buildLanguageChip(String value, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _selectedLanguage == value,
      onSelected: (selected) {
        if (selected) {
          setState(() => _selectedLanguage = value);
        }
      },
      selectedColor: Colors.purple[100],
    );
  }

  Future<void> _selectDateOfBirth() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime.now().subtract(const Duration(days: 36500)), // 100 years ago
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _dateOfBirthController.text = '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      });
    }
  }

  Future<void> _selectCountry() async {
    showCountryPicker(
      context: context,
      selectedCountry: _selectedCountry,
      onSelect: (country) {
        setState(() => _selectedCountry = country);
      },
    );
  }

  void _addNewAddress() {
    // Navigate to add address screen
    // This would be implemented in a separate screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add address functionality coming soon!')),
    );
  }

  void _handleAddressAction(String action, CustomerAddress address) {
    switch (action) {
      case 'edit':
        // Navigate to edit address screen
        break;
      case 'default':
        _setDefaultAddress(address);
        break;
      case 'delete':
        _deleteAddress(address);
        break;
    }
  }

  Future<void> _setDefaultAddress(CustomerAddress address) async {
    try {
      await CustomerService.updateCustomerAddress(
        address.copyWith(isDefault: true),
      );
      await _loadAddresses(); // Reload addresses
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error setting default address: $e')),
        );
      }
    }
  }

  Future<void> _deleteAddress(CustomerAddress address) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Address'),
        content: Text('Are you sure you want to delete "${address.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CustomerService.deleteCustomerAddress(address.id);
        await _loadAddresses(); // Reload addresses
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting address: $e')),
          );
        }
      }
    }
  }
}
