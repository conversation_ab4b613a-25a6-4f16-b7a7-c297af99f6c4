import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/order_model.dart';
import '../../models/garment_model.dart';
import '../../models/service_model.dart';
import '../../models/invoice_model.dart';
import '../../services/order_service.dart';
import '../../services/service_service.dart';
import '../../services/garment_service.dart';
import '../payments/invoice_preview_page.dart';
import '../../services/invoice_service.dart';
import '../payments/payment_receive_page.dart';
import 'widgets/add_item_dialog.dart';
import 'widgets/order_activity_manager.dart';


class OrderDetailsScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  final OrderService _orderService = OrderService(
    supabase: Supabase.instance.client,
  );
  final InvoiceService _invoiceService = InvoiceService(
    supabase: Supabase.instance.client,
  );

  Order? _order;
  Invoice? _invoice;
  bool _isLoading = true;
  String? _error;
  String _currencySymbol = '';

  // Activity feed state
  List<OrderActivityEntry> _activities = [];
  bool _loadingActivities = false;

  // Services and garments for adding functionality
  List<Service> _availableServices = [];
  List<Garment> _availableGarments = [];
  bool _loadingServices = false;

  @override
  void initState() {
    super.initState();
    _loadOrder();
    _loadCurrencySymbol();
  }

  Widget _buildActivityTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_loadingActivities) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 8),
            Text('No activity yet', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _activities.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final a = _activities[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: a.color.withValues(alpha: 0.15),
            child: Icon(a.icon, color: a.color, size: 18),
          ),
          title: Text(
            a.title,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          subtitle: Text(
            '${a.actor} • ${_formatDateTime(a.at)}${a.subtitle != null ? '\n${a.subtitle}' : ''}',
          ),
          isThreeLine: a.subtitle != null,
        );
      },
    );
  }

  Future<void> _loadCurrencySymbol() async {
    try {
      final symbol = await _orderService.getCurrencySymbol();
      if (mounted) {
        setState(() {
          _currencySymbol = symbol;
        });
      }
    } catch (_) {}
  }

  Future<void> _loadOrder() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final order = await _orderService.getOrderById(widget.orderId);

      // Load invoice information
      Invoice? invoice;
      try {
        invoice = await _invoiceService.getInvoiceByOrderId(widget.orderId);
      } catch (e) {
        // Invoice might not exist for older orders
        debugPrint('Invoice not found: $e');
      }

      setState(() {
        _order = order;
        _invoice = invoice;
        _isLoading = false;
      });
      await _loadActivities();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadActivities() async {
    try {
      setState(() => _loadingActivities = true);
      final supabase = Supabase.instance.client;

      final statusRows = await supabase
          .from('order_status_log')
          .select()
          .eq('order_id', widget.orderId)
          .order('created_at', ascending: false);

      final paymentRows = await supabase
          .from('payments')
          .select()
          .eq('order_id', widget.orderId)
          .order('created_at', ascending: false);

      final userIds = <String>{};
      for (final row in (statusRows as List)) {
        final id = row['changed_by'] as String?;
        if (id != null) userIds.add(id);
      }
      for (final row in (paymentRows as List)) {
        final id = row['created_by'] as String?;
        if (id != null) userIds.add(id);
      }

      Map<String, String> userIdToName = {};
      if (userIds.isNotEmpty) {
        final profiles = await supabase
            .from('staff_profiles')
            .select('id, full_name')
            .inFilter('id', userIds.toList());
        for (final p in (profiles as List)) {
          userIdToName[p['id'] as String] =
              (p['full_name'] as String?) ?? 'User';
        }
      }

      final List<OrderActivityEntry> entries = [];

      for (final row in (statusRows as List)) {
        final at = DateTime.parse(row['created_at'] as String);
        final oldStatus = row['old_status'] as String?;
        final newStatus = row['new_status'] as String?;
        final notes = (row['notes'] as String?) ?? '';
        final actorId = (row['changed_by'] as String?) ?? '';
        final actor = userIdToName[actorId] ?? 'System';

        final title = oldStatus == null && newStatus != null
            ? 'Order created (status: $newStatus)'
            : 'Status changed: ${oldStatus ?? ''} → ${newStatus ?? ''}';

        entries.add(
          OrderActivityEntry(
            at: at,
            actor: actor,
            title: title,
            subtitle: notes.isNotEmpty ? notes : null,
            icon: Icons.update,
            color: Colors.blue,
          ),
        );
      }

      for (final row in (paymentRows as List)) {
        final at = DateTime.parse(row['created_at'] as String);
        final amount = (row['amount'] as num).toDouble();
        final method = (row['method'] as String?) ?? 'payment';
        final tx = row['transaction_code'] as String?;
        final receipt = row['receipt_number'] as String?;
        final actorId = (row['created_by'] as String?) ?? '';
        final actor = userIdToName[actorId] ?? 'System';

        final ref = tx ?? receipt;
        entries.add(
          OrderActivityEntry(
            at: at,
            actor: actor,
            title: 'Payment received: \$${amount.toStringAsFixed(2)}',
            subtitle: ref != null ? '$method • $ref' : method,
            icon: Icons.payment,
            color: Colors.green,
          ),
        );
      }

      entries.sort((a, b) => b.at.compareTo(a.at));
      if (mounted) {
        setState(() {
          _activities = entries;
          _loadingActivities = false;
        });
      }
    } catch (_) {
      if (mounted) setState(() => _loadingActivities = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            _order?.orderNumber ?? 'Order Details',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 4,
          shadowColor: Colors.blue.withValues(alpha: 0.3),
          actions: [
            // Payment collection button - only show for non-cancelled orders with outstanding payment or no invoice
            if (_order != null &&
                _order!.status != OrderStatus.cancelled &&
                (_invoice == null || _invoice!.hasOutstandingAmount))
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: (_invoice != null && _invoice!.hasOutstandingAmount)
                      ? Colors.orange.withValues(
                          alpha: 0.9,
                        ) // Urgent if outstanding
                      : Colors.green.withValues(
                          alpha: 0.7,
                        ), // Normal if no invoice
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  onPressed: () => _openPaymentCollection(),
                  icon: const Icon(Icons.payment, color: Colors.white),
                  tooltip: (_invoice != null && _invoice!.hasOutstandingAmount)
                      ? 'Collect Outstanding Payment'
                      : 'Create Invoice & Collect Payment',
                ),
              ),
            // Print/Preview invoice
            if (_invoice != null)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (ctx) => InvoicePreviewPage(
                          orderId: _order!.id,
                          invoiceId: _invoice!.id,
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.picture_as_pdf, color: Colors.white),
                  tooltip: 'Invoice PDF',
                ),
              ),
            if (_order?.isActive == true)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  onPressed: () => _showUpdateStatusDialog(_order!),
                  icon: const Icon(Icons.edit, color: Colors.white),
                  tooltip: 'Update Status',
                ),
              ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                onPressed: _loadOrder,
                icon: const Icon(Icons.refresh, color: Colors.white),
                tooltip: 'Refresh',
              ),
            ),
          ],
          bottom: const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: TextStyle(fontWeight: FontWeight.bold),
            unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w500),
            indicatorColor: Colors.white,
            tabs: [
              Tab(icon: Icon(Icons.receipt_long), text: 'Details'),
              Tab(icon: Icon(Icons.timeline), text: 'Activity'),
            ],
          ),
        ),
        body: TabBarView(children: [_buildBody(), _buildActivityTab()]),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading order details...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading order',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadOrder,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_order == null) {
      return const Center(child: Text('Order not found'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderHeader(),
          const SizedBox(height: 16),
          _buildOrderItems(),
          const SizedBox(height: 16),
          _buildOrderSummary(),
          if (_invoice != null) ...[
            const SizedBox(height: 16),
            _buildInvoiceSummary(),
          ] else if (_order!.status != OrderStatus.cancelled) ...[
            const SizedBox(height: 16),
            _buildNoInvoiceSection(),
          ],
          // Add repeat order button
          const SizedBox(height: 16),
          _buildRepeatOrderSection(),
        ],
      ),
    );
  }

  Widget _buildOrderHeader() {
    final order = _order!;
    final statusColor = _getStatusColor(order.status);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, statusColor.withValues(alpha: 0.02)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: statusColor.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        children: [
          // Status Header Bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  statusColor.withValues(alpha: 0.1),
                  statusColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.15),
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    order.statusIcon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.status.displayName.toUpperCase(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                          letterSpacing: 1.2,
                        ),
                      ),
                      Text(
                        'Status',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (order.isActive)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: () => _showUpdateStatusDialog(order),
                        child: const Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.edit, color: Colors.white, size: 16),
                              SizedBox(width: 6),
                              Text(
                                'Update',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Order Details
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.orderNumber,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),

                // Service and Store Info
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.room_service,
                        label: 'Service',
                        value: order.service?.name ?? 'Unknown',
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.store,
                        label: 'Store',
                        value: order.store?.name ?? 'Unknown',
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Date and Total
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.calendar_today,
                        label: 'Created',
                        value: _formatDateTime(order.createdAt),
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.attach_money,
                        label: 'Total',
                        value:
                            '$_currencySymbol${order.totalAmount.toStringAsFixed(2)}',
                        color: Colors.purple,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItems() {
    final order = _order!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Expanded(
                child: Text(
                  'Order Items',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              if (order.status == OrderStatus.pending ||
                  order.status == OrderStatus.accepted) ...[
                ElevatedButton.icon(
                  onPressed: () => _showAddServicesItemsDialog(),
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Items'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showAddServiceDialog(),
                  icon: const Icon(Icons.room_service, size: 18),
                  label: const Text('Add Service'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          if (order.items.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.shopping_bag_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No items in this order',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add items and services to complete this order',
                    style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
                  ),
                  if (order.status == OrderStatus.pending ||
                      order.status == OrderStatus.accepted) ...[
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _showAddServicesItemsDialog(),
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Add Items'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton.icon(
                          onPressed: () => _showAddServiceDialog(),
                          icon: const Icon(Icons.room_service, size: 16),
                          label: const Text('Add Service'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            )
          else
            ...order.items.map((item) => _buildItemRow(item)),
        ],
      ),
    );
  }

  Widget _buildItemRow(OrderItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header with name, type, and total
          Row(
            children: [
              // Item icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: item.isServiceItem
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  item.isServiceItem ? Icons.room_service : Icons.checkroom,
                  color: item.isServiceItem
                      ? Colors.blue.shade600
                      : Colors.green.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.garment.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (item.isServiceItem) ...[
                      Text(
                        'Service Item',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ] else ...[
                      Text(
                        item.garment.category.toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Quantity display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  item.displayQuantity,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Total price (prominent)
              Text(
                '\$${item.totalPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Pricing details section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Unit price info
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Unit Price',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        item.priceDescription,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),

                // Pricing type badge
                if (item.pricingType != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getPricingTypeColor(item.pricingType!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getPricingTypeLabel(item.pricingType!),
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Notes section
          if (item.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.note, size: 16, color: Colors.blue.shade600),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      item.notes!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getPricingTypeColor(String pricingType) {
    switch (pricingType) {
      case 'per_kg':
        return Colors.orange.shade600;
      case 'per_item':
        return Colors.green.shade600;
      case 'fixed':
        return Colors.blue.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  String _getPricingTypeLabel(String pricingType) {
    switch (pricingType) {
      case 'per_kg':
        return 'per kg';
      case 'per_item':
        return 'per item';
      case 'fixed':
        return 'fixed';
      default:
        return pricingType;
    }
  }

  Widget _buildOrderSummary() {
    final order = _order!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Order Summary',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow('Subtotal', order.subtotal),
          if (order.taxAmount > 0) _buildSummaryRow('Tax', order.taxAmount),
          if (order.discountAmount > 0)
            _buildSummaryRow('Discount', -order.discountAmount),
          const Divider(),
          _buildSummaryRow('Total', order.totalAmount, isTotal: true),

          // Pay on delivery indicator
          if (order.payOnDelivery) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.delivery_dining, color: Colors.orange, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Pay on Delivery',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTotal ? 16 : 14,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            amount < 0
                ? '-$_currencySymbol${(-amount).toStringAsFixed(2)}'
                : '$_currencySymbol${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: amount < 0 ? Colors.green : null,
            ),
          ),
        ],
      ),
    );
  }

  /// Show dialog to add services to the order
  void _showAddServiceDialog() async {
    if (_availableServices.isEmpty) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      setState(() => _loadingServices = true);
      try {
        _availableServices = await ServiceService.getAllServices();
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Error loading services: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      } finally {
        if (mounted) setState(() => _loadingServices = false);
      }
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Add Service'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: _loadingServices
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    itemCount: _availableServices.length,
                    itemBuilder: (context, index) {
                      final service = _availableServices[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Icon(
                            Icons.room_service,
                            color: Colors.blue.shade600,
                          ),
                          title: Text(
                            service.name,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(service.description ?? 'No description'),
                              const SizedBox(height: 4),
                              Text(
                                service.getPricingText(),
                                style: TextStyle(
                                  color: Colors.green.shade700,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            await _addServiceToOrder(service);
                          },
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    }
  }

  /// Show dialog to add items to the order
  void _showAddServicesItemsDialog() async {
    if (_availableGarments.isEmpty) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      try {
        _availableGarments = await GarmentService.getAllGarments();
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Error loading garments: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AddItemDialog(
          availableGarments: _availableGarments,
          onItemAdded: _addItemToOrder,
        ),
      );
    }
  }

  /// Add a service to the current order
  Future<void> _addServiceToOrder(Service service) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      // For adding a service, we create a generic order item
      // The staff can specify weight/quantity through a dialog
      double? weight;
      double? customPrice;

      if (service.pricingType == 'per_kg') {
        weight = await _showWeightInputDialog();
        if (weight == null || weight <= 0) return;
      }

      // Ask for custom pricing if needed
      final pricingResult = await _showCustomPricingDialog(service);
      if (pricingResult != null) {
        customPrice = pricingResult['amount'];
      }

      // Create a generic garment for the service
      final serviceGarment = Garment(
        id: 'service-${service.id}',
        name: service.name,
        category: 'service',
        description: service.description,
        garmentType: 'service',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      double quantity = 1.0;
      double unitPrice = 0.0;

      if (customPrice != null && customPrice > 0) {
        unitPrice = customPrice;
        if (service.pricingType == 'per_kg' && weight != null) {
          quantity = weight;
        }
      } else {
        switch (service.pricingType) {
          case 'per_kg':
            quantity = weight ?? 1.0;
            unitPrice = service.pricePerKg ?? 0.0;
            break;
          case 'per_item':
            quantity = 1.0;
            unitPrice = service.pricePerItem ?? 0.0;
            break;
          case 'fixed':
            quantity = 1.0;
            unitPrice = service.basePrice;
            break;
        }
      }

      final orderItem = OrderItem(
        garmentId: serviceGarment.id,
        garment: serviceGarment,
        quantity: quantity.round().clamp(1, 999),
        unitPrice: unitPrice,
        pricingType: service.pricingType,
        weight: weight,
        customAmount: customPrice,
        isServiceItem: true,
        notes: _buildServiceNotes(service, weight, customPrice),
      );

      // Add to order via API
      await _orderService.addItemToOrder(_order!.id, orderItem);

      // Reload order
      await _loadOrder();

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('${service.name} added to order'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error adding service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Add an item to the current order
  Future<void> _addItemToOrder(
    Garment garment,
    int quantity,
    double unitPrice, {
    String? pricingType,
    double? weight,
    double? customAmount,
  }) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      final orderItem = OrderItem(
        garmentId: garment.id,
        garment: garment,
        quantity: quantity,
        unitPrice: unitPrice,
        pricingType: pricingType,
        weight: weight,
        customAmount: customAmount,
        notes: _buildItemNotes(pricingType, weight, customAmount),
      );

      // Add to order via API
      await _orderService.addItemToOrder(_order!.id, orderItem);

      // Reload order
      await _loadOrder();

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('${garment.name} added to order'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error adding item: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String? _buildItemNotes(
    String? pricingType,
    double? weight,
    double? customAmount,
  ) {
    List<String> notes = [];

    if (weight != null) {
      notes.add('Weight: ${weight.toStringAsFixed(1)}kg');
    }

    if (customAmount != null) {
      if (pricingType == 'per_kg') {
        notes.add('Custom rate: \$${customAmount.toStringAsFixed(2)}/kg');
      } else {
        notes.add('Custom amount: \$${customAmount.toStringAsFixed(2)}');
      }
    }

    if (pricingType != null) {
      notes.add('Pricing: $pricingType');
    }

    return notes.isNotEmpty ? notes.join(' | ') : null;
  }

  String? _buildServiceNotes(
    Service service,
    double? weight,
    double? customPrice,
  ) {
    List<String> notes = [];

    notes.add('Service: ${service.name}');

    if (weight != null) {
      notes.add('Weight: ${weight.toStringAsFixed(1)}kg');
    }

    if (customPrice != null) {
      if (service.pricingType == 'per_kg') {
        notes.add('Custom rate: \$${customPrice.toStringAsFixed(2)}/kg');
      } else {
        notes.add('Custom price: \$${customPrice.toStringAsFixed(2)}');
      }
    } else {
      notes.add('Standard rate: ${service.getPricingText()}');
    }

    return notes.join(' | ');
  }

  /// Show weight input dialog for per-kg services
  Future<double?> _showWeightInputDialog([Service? service]) async {
    final controller = TextEditingController();

    return showDialog<double>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Enter Weight${service != null ? ' for ${service.name}' : ''}',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This ${service != null ? 'service is' : 'item is'} priced per kilogram. Please enter the weight:',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              decoration: const InputDecoration(
                labelText: 'Weight (kg)',
                border: OutlineInputBorder(),
                suffixText: 'kg',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final navigator = Navigator.of(context);
              final weight = double.tryParse(controller.text);
              if (weight != null && weight > 0) {
                navigator.pop(weight);
              } else {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a valid weight'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  /// Show custom pricing dialog
  Future<Map<String, dynamic>?> _showCustomPricingDialog(
    Service service,
  ) async {
    final controller = TextEditingController();
    String pricingMode = service.pricingType;

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Custom Pricing for ${service.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Set custom pricing or use standard rates:',
                style: TextStyle(color: Colors.grey.shade600),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: pricingMode,
                decoration: const InputDecoration(
                  labelText: 'Pricing Mode',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'per_kg',
                    child: Text('Per Kilogram'),
                  ),
                  DropdownMenuItem(value: 'per_item', child: Text('Per Item')),
                  DropdownMenuItem(value: 'fixed', child: Text('Fixed Price')),
                ],
                onChanged: (value) {
                  setState(() {
                    pricingMode = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                decoration: InputDecoration(
                  labelText: 'Custom Amount',
                  border: const OutlineInputBorder(),
                  prefixText: '\$',
                  helperText: 'Leave empty to use standard pricing',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () =>
                  Navigator.of(context).pop({}), // Use standard pricing
              child: const Text('Use Standard'),
            ),
            ElevatedButton(
              onPressed: () {
                final navigator = Navigator.of(context);
                final amount = double.tryParse(controller.text);
                if (amount != null && amount > 0) {
                  navigator.pop({'amount': amount, 'mode': pricingMode});
                } else {
                  navigator.pop({}); // Use standard pricing
                }
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.purple;
      case OrderStatus.inProcess:
        return Colors.indigo;
      case OrderStatus.readyForPickup:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.amber;
      case OrderStatus.completed:
        return Colors.teal;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _openPaymentCollection() async {
    if (_order == null) return;

    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => PaymentReceivePage(
          orderId: _order!.id,
          invoiceId: _invoice?.id,
          prefilledAmount: _invoice?.effectiveOutstandingAmount,
          isPickupFlow:
              _order!.status == OrderStatus.readyForPickup ||
              _order!.status == OrderStatus.outForDelivery,
        ),
      ),
    );

    // Reload order and invoice after payment
    if (result != null && result['success'] == true) {
      await _loadOrder();
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Payment of ${result['amount']?.toStringAsFixed(2) ?? '0.00'} recorded',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showUpdateStatusDialog(Order order) {
    // Get available statuses based on current workflow
    List<OrderStatus> availableStatuses = _getAvailableStatuses(order);

    if (availableStatuses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No status updates available for this order'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status: ${order.status.displayName}',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            const Text('Select new status:'),
            const SizedBox(height: 8),
            ...availableStatuses.map(
              (status) => ListTile(
                title: Text(status.displayName),
                leading: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                onTap: () {
                  final navigator = Navigator.of(context);
                  navigator.pop();
                  _updateOrderStatus(order, status, null);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  List<OrderStatus> _getAvailableStatuses(Order order) {
    // Check if this is an in-store order (no pickup address)
    bool isInStoreOrder = order.pickupAddressId == null;

    switch (order.status) {
      case OrderStatus.pending:
        return [OrderStatus.accepted, OrderStatus.cancelled];
      case OrderStatus.accepted:
        // For in-store orders, skip the pickup step
        return isInStoreOrder
            ? [OrderStatus.inProcess, OrderStatus.cancelled]
            : [OrderStatus.pickedUp, OrderStatus.cancelled];
      case OrderStatus.pickedUp:
        return [OrderStatus.inProcess];
      case OrderStatus.inProcess:
        return [OrderStatus.readyForPickup];
      case OrderStatus.readyForPickup:
        // Ready for pickup/delivery - choice between customer pickup or delivery
        return [OrderStatus.completed, OrderStatus.outForDelivery];
      case OrderStatus.outForDelivery:
        // Out for delivery - can only mark as completed
        return [OrderStatus.completed];
      case OrderStatus.completed:
      case OrderStatus.cancelled:
        // No further status changes possible
        return [];
    }
  }

  /// Validate order before accepting
  Map<String, dynamic> _validateOrderForAcceptance(Order order) {
    List<String> issues = [];

    // Check if order has items
    if (order.items.isEmpty) {
      issues.add('Order has no items');
    } else {
      // Check each item for completeness
      for (final item in order.items) {
        if (item.quantity <= 0) {
          issues.add('"${item.garment.name}" has invalid quantity');
        }
        if (item.unitPrice <= 0) {
          issues.add('"${item.garment.name}" has no unit price');
        }
      }
    }

    // Check if order has valid totals
    if (order.totalAmount <= 0) {
      issues.add('Order has no total amount');
    }

    // Check if order has weight (if weight-based pricing) - removed totalWeight check as not available
    // Weight validation can be added when totalWeight property is available on Order model

    return {
      'isValid': issues.isEmpty,
      'message': issues.join('\n'),
      'issues': issues,
    };
  }

  /// Show validation dialog when order is incomplete
  void _showOrderValidationDialog(Order order, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.warning, color: Colors.orange, size: 24),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Order Validation Required',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'This order cannot be accepted because:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Text(
                message,
                style: const TextStyle(fontSize: 14, height: 1.5),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Please add the missing information before accepting this order.',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              final navigator = Navigator.of(context);
              navigator.pop();
              _showOrderItemsEditDialog(order);
            },
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('Edit Order'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Show dialog to edit order items
  void _showOrderItemsEditDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Order Edit Interface'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.construction, size: 48, color: Colors.orange),
            const SizedBox(height: 16),
            Text(
              'Quick order editing interface is under development. '
              'Please use the customer edit order screen for comprehensive item management.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateOrderStatus(
    Order order,
    OrderStatus newStatus,
    String? notes,
  ) async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      // Validation before accepting orders
      if (newStatus == OrderStatus.accepted &&
          order.status == OrderStatus.pending) {
        final validationResult = _validateOrderForAcceptance(order);
        if (!validationResult['isValid']) {
          if (mounted) {
            _showOrderValidationDialog(order, validationResult['message']);
          }
          return;
        }
      }

      // Check if payment is required before certain status transitions
      if (await _shouldCollectPaymentBeforeStatusChange(newStatus)) {
        if (mounted) {
          final shouldProceed = await _showPaymentRequiredDialog(newStatus);
          if (!shouldProceed) {
            return; // User cancelled or payment was not completed
          }
        }
      }

      await _orderService.updateOrderStatus(
        orderId: order.id,
        newStatus: newStatus,
        changedBy: user.id,
        notes: notes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
        _loadOrder(); // Refresh order details
      }
    } catch (e) {
      // Detect backend rule enforcing payment-before-status
      final message = e.toString();
      final paymentRequired =
          message.toLowerCase().contains('must be paid') ||
          message.toLowerCase().contains('payment') &&
              message.toLowerCase().contains('before') ||
          message.contains('23514');

      if (paymentRequired) {
        await _showPaymentRequiredDialog(newStatus);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating order status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if payment should be collected before changing to the new status
  Future<bool> _shouldCollectPaymentBeforeStatusChange(
    OrderStatus newStatus,
  ) async {
    // Skip payment requirements for pay-on-delivery orders
    if (_order?.payOnDelivery == true) {
      return false;
    }

    // Require payment before delivery or completion only if there is a real outstanding amount
    if (newStatus == OrderStatus.completed ||
        newStatus == OrderStatus.readyForPickup) {
      if (_invoice == null) {
        return true; // must create invoice/collect payment first
      }
      final double outstanding = _invoice!.effectiveOutstandingAmount;
      return outstanding >
          0.01; // allow update when outstanding is 0.00 (or tiny rounding)
    }
    return false;
  }

  // Removed unused helper that is no longer referenced after tightening outstanding checks.

  /// Show dialog asking user to collect payment before status change
  Future<bool> _showPaymentRequiredDialog(OrderStatus newStatus) async {
    // Special handling for pay-on-delivery orders
    if (_order?.payOnDelivery == true &&
        (newStatus == OrderStatus.completed ||
            newStatus == OrderStatus.outForDelivery)) {
      return await _showPayOnDeliveryDialog(newStatus);
    }

    final bool isPaid =
        _invoice != null && _invoice!.effectiveOutstandingAmount <= 0.01;
    final bool hasOutstanding =
        _invoice != null && _invoice!.effectiveOutstandingAmount > 0.01;
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(!isPaid ? 'Payment Required' : 'No Payment Outstanding'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_invoice != null && !isPaid && hasOutstanding) ...[
              Text(
                'This order has an outstanding amount of '
                '$_currencySymbol${_invoice!.effectiveOutstandingAmount.toStringAsFixed(2)}.',
              ),
              const SizedBox(height: 12),
              Text(
                'Payment must be collected before changing status to "${newStatus.displayName}".',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ] else if (isPaid) ...[
              const Text(
                'This order is fully paid. You can proceed with the status change.',
              ),
            ] else ...[
              const Text('This order must be paid before changing status.'),
            ],
          ],
        ),
        actions: !isPaid
            ? [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    final navigator = Navigator.of(context);
                    navigator.pop(true);
                    await _collectPaymentBeforeStatusChange(newStatus);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Collect Payment'),
                ),
              ]
            : [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('OK'),
                ),
              ],
      ),
    );

    return result ?? false;
  }

  /// Show dialog for pay-on-delivery orders
  Future<bool> _showPayOnDeliveryDialog(OrderStatus newStatus) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delivery_dining, color: Colors.orange),
            SizedBox(width: 8),
            Text('Pay on Delivery Order'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This is a pay-on-delivery order. Payment will be collected when the customer receives their laundry.',
                      style: TextStyle(fontSize: 13, color: Colors.orange[800]),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You can proceed to "${newStatus.displayName}" status. Payment collection will be handled during ${newStatus == OrderStatus.outForDelivery ? "delivery" : "pickup"}.',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            const Text(
              'Would you like to collect payment now or proceed without payment?',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () =>
                Navigator.of(context).pop(true), // Proceed without payment
            child: const Text('Proceed Without Payment'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              final navigator = Navigator.of(context);
              navigator.pop(false); // Collect payment first
            },
            icon: const Icon(Icons.payment),
            label: const Text('Collect Payment Now'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );

    return result ?? true; // Default to proceeding without payment
  }

  /// Collect payment and then proceed with status change
  Future<void> _collectPaymentBeforeStatusChange(
    OrderStatus targetStatus,
  ) async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => PaymentReceivePage(
          orderId: _order!.id,
          invoiceId: _invoice?.id,
          prefilledAmount: _invoice?.effectiveOutstandingAmount,
          isPickupFlow: targetStatus == OrderStatus.completed,
        ),
      ),
    );

    if (result != null && result['success'] == true) {
      await _loadOrder(); // Reload to get updated invoice

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment of $_currencySymbol${result['amount']?.toStringAsFixed(2) ?? '0.00'} collected. '
              'You can now update the order status.',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Payment collection was cancelled. Order status not updated.',
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Widget _buildInvoiceSummary() {
    if (_invoice == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Invoice & Payment',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _invoice!.isPaid ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _invoice!.displayStatus,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInvoiceRow('Invoice Number:', _invoice!.invoiceNumber),
          _buildInvoiceRow(
            'Total Amount:',
            '$_currencySymbol${_invoice!.totalAmount.toStringAsFixed(2)}',
          ),
          _buildInvoiceRow(
            'Paid Amount:',
            '$_currencySymbol${(_invoice!.totalPaid ?? 0.0).toStringAsFixed(2)}',
          ),
          if (_invoice!.hasOutstandingAmount) ...[
            const Divider(height: 20),
            _buildInvoiceRow(
              'Outstanding:',
              '$_currencySymbol${_invoice!.effectiveOutstandingAmount.toStringAsFixed(2)}',
              isHighlight: true,
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _openPaymentCollection,
                icon: const Icon(Icons.payment),
                label: const Text('Collect Outstanding Payment'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ] else if (_invoice!.isPaid) ...[
            const Divider(height: 20),
            // Show only invoice preview for fully paid orders
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (ctx) => InvoicePreviewPage(
                        orderId: _order!.id,
                        invoiceId: _invoice!.id,
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.receipt),
                label: const Text('View Invoice'),
              ),
            ),
          ],
          if (_invoice!.payments != null && _invoice!.payments!.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Payment History',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            ..._invoice!.payments!.map(
              (payment) => Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            payment.displayMethod,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            payment.displayReference,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            _formatDateTime(payment.createdAt),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '$_currencySymbol${payment.amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInvoiceRow(
    String label,
    String value, {
    bool isHighlight = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isHighlight ? Colors.orange : Colors.grey[600],
              fontWeight: isHighlight ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isHighlight ? Colors.orange : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoInvoiceSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.receipt_long, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Payment & Invoice',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'No Invoice',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: const Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'This order doesn\'t have an invoice yet. You can create one and collect payment.',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _openPaymentCollection,
              icon: const Icon(Icons.receipt_long),
              label: const Text('Create Invoice & Collect Payment'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepeatOrderSection() {
    if (_order == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.repeat, color: Colors.blue, size: 20),
              ),
              const SizedBox(width: 12),
              const Text(
                'Repeat Order',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Create a new order with the same items, service, and addresses from this order.',
                    style: TextStyle(fontSize: 13, color: Colors.blue[700]),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _repeatOrder(_order!),
              icon: const Icon(Icons.repeat),
              label: const Text('Repeat This Order'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _repeatOrder(Order order) async {
    // Show confirmation dialog with order details
    final confirmed = await _showRepeatOrderDialog(order);
    if (!confirmed) return;

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      final newOrder = await _orderService.repeatOrder(
        originalOrderId: order.id,
        userId: user.id,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'New order ${newOrder.orderNumber} created successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
        // Optionally navigate to the new order or order history
        Navigator.of(context).pop(); // Go back to previous screen
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error repeating order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _showRepeatOrderDialog(Order order) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.repeat, color: Colors.blue, size: 20),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Repeat Order',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Create a new order with the same details?',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 20),

              // Order Details Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.receipt_long,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Order Details',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    _buildDetailRow(
                      'Order Number:',
                      order.orderNumber,
                      Icons.confirmation_number,
                    ),
                    _buildDetailRow(
                      'Store/Branch:',
                      order.store?.name ?? 'Unknown Store',
                      Icons.store,
                    ),
                    _buildDetailRow(
                      'Service:',
                      order.service?.name ?? 'Unknown Service',
                      Icons.room_service,
                    ),
                    _buildDetailRow(
                      'Number of Items:',
                      '${order.totalItems} items',
                      Icons.inventory,
                    ),
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      'Total Amount:',
                      '$_currencySymbol${order.totalAmount.toStringAsFixed(2)}',
                      Icons.attach_money,
                      isHighlight: true,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, size: 16, color: Colors.blue[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'A new order will be created with the same items, service, and addresses. You can modify details after creation.',
                        style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel', style: TextStyle(color: Colors.grey[600])),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(true),
            icon: const Icon(Icons.repeat, size: 16),
            label: const Text('Create Order'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon, {
    bool isHighlight = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: isHighlight ? Colors.green[600] : Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                fontWeight: isHighlight ? FontWeight.bold : FontWeight.w500,
                color: isHighlight ? Colors.green[700] : Colors.black87,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}

