import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _errorMessage;

  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;

  final SupabaseService _supabaseService = SupabaseService.instance;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    _supabaseService.authStateChanges.listen((AuthState data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;

      switch (event) {
        case AuthChangeEvent.signedIn:
          _user = session?.user;
          _status = AuthStatus.authenticated;
          _errorMessage = null;
          break;
        case AuthChangeEvent.signedOut:
          _user = null;
          _status = AuthStatus.unauthenticated;
          _errorMessage = null;
          break;
        case AuthChangeEvent.userUpdated:
          _user = session?.user;
          break;
        default:
          break;
      }
      notifyListeners();
    });

    // Check initial auth state
    final currentUser = _supabaseService.currentUser;
    if (currentUser != null) {
      _user = currentUser;
      _status = AuthStatus.authenticated;
    } else {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  Future<bool> signUp({
    required String email,
    required String password,
    String? fullName,
    String? phone,
    String? country,
  }) async {
    try {
      _setLoading();
      final response = await _supabaseService.signUpWithEmail(
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
        country: country,
      );

      if (response.user != null) {
        // For customer registration, don't automatically authenticate
        // User must verify email first
        _user = null;
        _status = AuthStatus.unauthenticated;
        _errorMessage = null;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<bool> signIn({
    required String identifier,
    required String password,
  }) async {
    try {
      _setLoading();

      // Check if identifier is staff ID format
      final isStaffId = await _supabaseService.isStaffIdFormat(identifier);

      if (isStaffId) {
        // Handle staff ID login - get staff email and authenticate with Supabase Auth
        final staffInfo = await _supabaseService.getStaffById(
          int.parse(identifier),
        );

        if (staffInfo != null) {
          final staffEmail = staffInfo['email'] as String?;
          final staffProfileId = staffInfo['staff_profile_id'] as String?;

          if (staffEmail != null && staffProfileId != null) {
            try {
              // Authenticate staff using their email and password
              final response = await _supabaseService.signInStaff(
                email: staffEmail,
                password: password,
              );

              if (response.user != null) {
                // Log successful staff login
                await _supabaseService.logStaffLogin(
                  staffProfileId: staffProfileId,
                  loginMethod: 'staff_id',
                );

                _user = response.user;
                _status = AuthStatus.authenticated;
                _errorMessage = null;
                notifyListeners();
                return true;
              } else {
                _setError('Staff authentication failed. Please check your password.');
                return false;
              }
            } catch (e) {
              String errorMessage = e.toString();
              
              // Handle specific authentication errors for staff
              if (errorMessage.contains('invalid_credentials')) {
                errorMessage = 'Invalid staff ID or password. Please check and try again.';
              } else if (errorMessage.contains('email_not_confirmed')) {
                errorMessage = 'Staff account not verified. Please contact administrator.';
              } else if (errorMessage.contains('too_many_requests')) {
                errorMessage = 'Too many login attempts. Please wait before trying again.';
              }

              _setError(errorMessage);
              return false;
            }
          } else {
            _setError(
              'Staff account not properly configured. Please contact administrator.',
            );
            return false;
          }
        } else {
          _setError('Invalid staff ID. Please check and try again.');
          return false;
        }
      } else {
        // Handle regular email login
        final response = await _supabaseService.signInWithEmail(
          email: identifier,
          password: password,
        );

        if (response.user != null) {
          // Check if email is verified for customer accounts
          if (response.user!.emailConfirmedAt != null) {
            _user = response.user;
            _status = AuthStatus.authenticated;
            _errorMessage = null;
            notifyListeners();
            return true;
          } else {
            // Email not verified
            _setError(
              'Please verify your email address before logging in. Check your inbox for the verification link.',
            );
            return false;
          }
        }
      }
      return false;
    } catch (e) {
      String errorMessage = e.toString();

      // Handle specific authentication errors
      if (errorMessage.contains('invalid_credentials')) {
        errorMessage = 'Invalid email or password. Please check and try again.';
      } else if (errorMessage.contains('email_not_confirmed')) {
        errorMessage =
            'Please check your email and confirm your account first.';
      } else if (errorMessage.contains('too_many_requests')) {
        errorMessage =
            'Too many login attempts. Please wait before trying again.';
      }

      _setError(errorMessage);
      return false;
    }
  }

  Future<bool> signInWithOtp({required String email}) async {
    try {
      _setLoading();
      await _supabaseService.signInWithOtp(email: email);
      _status = AuthStatus.unauthenticated; // Still need to verify OTP
      _errorMessage = null;
      notifyListeners();
      return true;
    } catch (e) {
      String errorMessage = e.toString();

      // Handle specific OTP request errors
      if (errorMessage.contains('email_rate_limit_exceeded')) {
        errorMessage =
            'Too many OTP requests. Please wait before requesting another.';
      } else if (errorMessage.contains('invalid_email')) {
        errorMessage = 'Please enter a valid email address.';
      }

      _setError(errorMessage);
      return false;
    }
  }

  /// Resend email verification for customer accounts
  Future<bool> resendVerificationEmail({required String email}) async {
    try {
      _setLoading();
      await _supabaseService.resendVerificationEmail(email: email);
      _errorMessage = null;
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<bool> verifyOtp({
    required String email,
    required String token,
    required OtpType type,
    int? staffId,
  }) async {
    try {
      _setLoading();
      final response = await _supabaseService.verifyOtp(
        email: email,
        token: token,
        type: type,
      );

      if (response.user != null) {
        _user = response.user;
        _status = AuthStatus.authenticated;
        _errorMessage = null;

        // If this was staff OTP login, log the staff login activity
        if (staffId != null) {
          try {
            final staffInfo = await _supabaseService.getStaffById(staffId);
            if (staffInfo != null) {
              final staffProfileId = staffInfo['staff_profile_id'] as String?;
              if (staffProfileId != null) {
                await _supabaseService.logStaffLogin(
                  staffProfileId: staffProfileId,
                  loginMethod: 'staff_id_otp',
                );
              }
            }
          } catch (e) {
            // Log staff activity failed, but don't fail the whole login
            if (kDebugMode) {
              print('Failed to log staff login activity: $e');
            }
          }
        }

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      String errorMessage = e.toString();

      // Handle specific OTP errors
      if (errorMessage.contains('otp_expired')) {
        errorMessage = 'OTP has expired. Please request a new one.';
      } else if (errorMessage.contains('invalid_otp')) {
        errorMessage = 'Invalid OTP. Please check and try again.';
      } else if (errorMessage.contains('too_many_requests')) {
        errorMessage = 'Too many attempts. Please wait before trying again.';
      }

      _setError(errorMessage);
      return false;
    }
  }

  Future<bool> resetPassword({required String email}) async {
    try {
      _setLoading();
      await _supabaseService.resetPassword(email: email);
      _status = AuthStatus.unauthenticated;
      _errorMessage = null;
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<bool> updatePassword({required String password}) async {
    try {
      _setLoading();
      final response = await _supabaseService.updatePassword(
        password: password,
      );

      if (response.user != null) {
        _user = response.user;
        _status = AuthStatus.authenticated;
        _errorMessage = null;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<bool> resetPasswordByStaffId({required int staffId}) async {
    try {
      _setLoading();

      // First validate the staff ID
      final staffInfo = await _supabaseService.getStaffById(staffId);
      if (staffInfo == null) {
        _setError('Invalid staff ID. Please check and try again.');
        return false;
      }

      // Check if staff is active
      if (staffInfo['is_active'] != true) {
        _setError('Staff account is not active. Please contact administrator.');
        return false;
      }

      // Get the staff's email from their profile
      final staffEmail = staffInfo['email'] as String?;
      if (staffEmail == null) {
        _setError(
          'Staff account not properly configured. Please contact administrator.',
        );
        return false;
      }

      // Use the reset password functionality for the staff's email
      await _supabaseService.resetPassword(email: staffEmail);

      _status = AuthStatus.unauthenticated;
      _errorMessage = null;
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading();
      await _supabaseService.signOut();
      _user = null;
      _status = AuthStatus.unauthenticated;
      _errorMessage = null;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  void _setLoading() {
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _status = AuthStatus.error;
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _status = _user != null
          ? AuthStatus.authenticated
          : AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  // Public method to validate staff ID
  Future<Map<String, dynamic>?> validateStaffId(int staffId) async {
    try {
      return await _supabaseService.getStaffById(staffId);
    } catch (e) {
      return null;
    }
  }

  // Public method to check if input is staff ID format
  Future<bool> isStaffIdFormat(String input) async {
    try {
      return await _supabaseService.isStaffIdFormat(input);
    } catch (e) {
      return false;
    }
  }
}
