import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'dart:math';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    await dotenv.load(fileName: ".env");

    final supabaseUrl = dotenv.env['SUPABASE_URL'];
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];

    if (supabaseUrl == null || supabaseAnonKey == null) {
      throw Exception(
        'Supabase URL and Anon Key must be provided in .env file',
      );
    }

    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
  }

  // Authentication methods
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? fullName,
    String? phone,
    String? country,
  }) async {
    return await client.auth.signUp(
      email: email,
      password: password,
      data: {
        'full_name': fullName,
        'phone': phone,
        'country': country,
        'is_staff': false, // Explicitly mark as customer
      },
    );
  }

  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signInWithOtp({required String email}) async {
    await client.auth.signInWithOtp(email: email, emailRedirectTo: null);
  }

  Future<AuthResponse> verifyOtp({
    required String email,
    required String token,
    required OtpType type,
  }) async {
    return await client.auth.verifyOTP(email: email, token: token, type: type);
  }

  Future<void> resetPassword({required String email}) async {
    await client.auth.resetPasswordForEmail(email, redirectTo: null);
  }

  Future<void> resendVerificationEmail({required String email}) async {
    await client.auth.resend(type: OtpType.signup, email: email);
  }

  Future<UserResponse> updatePassword({required String password}) async {
    return await client.auth.updateUser(UserAttributes(password: password));
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  User? get currentUser => client.auth.currentUser;

  Session? get currentSession => client.auth.currentSession;

  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // Staff-specific authentication methods
  Future<AuthResponse> signUpStaff({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    return await client.auth.signUp(
      email: email,
      password: password,
      data: {'is_staff': true, 'full_name': fullName, 'phone': phone},
    );
  }

  // Create customer account with authentication (for staff use)
  Future<Map<String, dynamic>> createCustomerAccount({
    required String email,
    required String fullName,
    String? phone,
    String? country,
  }) async {
    try {
      // Generate a secure temporary password
      final tempPassword = _generateSecurePassword();
      
      // Use regular signup instead of admin.createUser (which requires admin privileges)
      final authResponse = await client.auth.signUp(
        email: email,
        password: tempPassword,
        data: {
          'full_name': fullName,
          'phone': phone,
          'country': country,
          'is_staff': false,
          'created_by_staff': true,
          'email_confirmed': true, // Mark as confirmed in metadata
        },
      );

      if (authResponse.user == null) {
        throw Exception('Failed to create user account');
      }

      // Note: The email will be sent for confirmation, but we can handle this differently
      // For now, we'll create the account and the customer can confirm their email later
      // The staff member should inform the customer to check their email and confirm

      return {
        'user_id': authResponse.user!.id,
        'email': email,
        'temp_password': tempPassword,
        'full_name': fullName,
        'email_confirmation_required': true,
      };
    } catch (e) {
      throw Exception('Failed to create customer account: $e');
    }
  }

  // Generate a secure temporary password
  String _generateSecurePassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(12, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  Future<Map<String, dynamic>?> getStaffProfile() async {
    if (currentUser == null) return null;

    final response = await client
        .from('staff_profiles')
        .select('*')
        .eq('auth_user_id', currentUser!.id)
        .eq('is_active', true)
        .maybeSingle();

    return response;
  }

  Future<bool> isStaff() async {
    if (currentUser == null) return false;

    final response = await client.rpc('is_staff');
    return response as bool? ?? false;
  }

  Future<List<Map<String, dynamic>>> getAllStaff() async {
    final response = await client
        .from('staff_profiles')
        .select('*')
        .eq('is_active', true)
        .order('staff_id');

    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>?> createStaffProfile({
    required String fullName,
    required String email,
    String? phone,
    String? password,
    bool emailEnabled = true,
  }) async {
    final response = await client.rpc(
      'create_staff_profile',
      params: {
        'p_full_name': fullName,
        'p_email': email,
        'p_phone': phone,
        'p_password': password,
        'p_email_enabled': emailEnabled,
        'p_auth_user_id': currentUser?.id,
      },
    );

    return response;
  }

  Future<void> logStaffActivity({
    required String staffId,
    required String action,
    Map<String, dynamic>? details,
  }) async {
    await client.rpc(
      'log_staff_activity',
      params: {
        'p_staff': staffId,
        'p_action': action,
        'p_details': details,
        'p_by': currentUser?.id,
      },
    );
  }

  // Staff ID Authentication Methods
  Future<bool> isStaffIdFormat(String input) async {
    try {
      final response = await client.rpc(
        'is_staff_id_format',
        params: {'p_input': input},
      );
      return response as bool? ?? false;
    } catch (e) {
      // Fallback to regex validation if RPC fails
      return RegExp(r'^[0-9]{4,}$').hasMatch(input);
    }
  }

  Future<Map<String, dynamic>?> getStaffById(int staffId) async {
    try {
      // Use the new get_staff_by_id function from migration 08
      final response = await client.rpc(
        'get_staff_by_id',
        params: {'p_staff_id': staffId},
      );

      if (response != null && response is List && response.isNotEmpty) {
        final staffInfo = response.first as Map<String, dynamic>;
        
        // Check if staff is active
        final isActive = staffInfo['is_active'] as bool? ?? true;
        if (!isActive) {
          return null;
        }

        // Return the staff information with proper structure
        return {
          'auth_user_id': staffInfo['auth_user_id'],
          'staff_profile_id': staffInfo['staff_profile_id'],
          'email': staffInfo['email'],
          'full_name': staffInfo['full_name'],
          'phone': staffInfo['phone'],
          'is_active': staffInfo['is_active'],
          'staff_id': staffId, // Add the original staff_id for reference
        };
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> logStaffLogin({
    required String staffProfileId,
    String loginMethod = 'staff_id',
  }) async {
    await client.rpc(
      'log_staff_login',
      params: {
        'p_staff_profile_id': staffProfileId,
        'p_login_method': loginMethod,
      },
    );
  }

  // Staff authentication using email and password
  Future<AuthResponse> signInStaff({
    required String email,
    required String password,
  }) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }
}
