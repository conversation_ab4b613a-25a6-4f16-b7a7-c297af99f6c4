import 'package:flutter/material.dart';
// Removed currency_picker import - now using fixed currency symbol
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../services/invoice_service.dart';
// Removed currency_service import - no longer needed
import '../../services/auth_service.dart';
import 'create_order_screen.dart';
import 'order_details_screen.dart';
import '../payments/payment_receive_page.dart';

class OrderHistoryPage extends StatefulWidget {
  final bool showActiveOnly;

  const OrderHistoryPage({super.key, this.showActiveOnly = false});

  @override
  State<OrderHistoryPage> createState() => _OrderHistoryPageState();
}

class _OrderHistoryPageState extends State<OrderHistoryPage> {
  final OrderService _orderService = OrderService(
    supabase: Supabase.instance.client,
  );
  final InvoiceService _invoiceService = InvoiceService(
    supabase: Supabase.instance.client,
  );
  final TextEditingController _searchController = TextEditingController();
  // Removed _currencyService - now using fixed currency symbol
  final AuthService _authService = AuthService(
    supabase: Supabase.instance.client,
  );
  bool _isLoading = true;
  String _currencySymbol = '';
  String _creatorName = '';
  List<Order> _orders = [];
  List<Order> _filteredOrders = [];
  String? _error;
  OrderStatus? _selectedStatus;
  String _sortBy = 'newest'; // newest, oldest, amount_high, amount_low
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadOrders();
    _searchController.addListener(_onSearchChanged);
    _loadCurrencySymbol();
    _creatorName = _authService.currentUser?.email?.split('@')[0] ?? 'User';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrencySymbol() async {
    try {
      final symbol = await _orderService.getCurrencySymbol();
      if (mounted) {
        setState(() {
          _currencySymbol = symbol;
        });
      }
    } catch (_) {
      // Ignore and keep default/empty symbol on failure
    }
  }

  // Removed _showCurrencyPicker - now using fixed currency symbol

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _applyFiltersAndSort();
    });
  }

  Future<void> _loadOrders() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        throw 'User not authenticated';
      }

      // Fetch all orders from all users instead of just current user's orders
      final orders = await _orderService.getAllOrders();

      setState(() {
        _orders = orders;
        _applyFiltersAndSort();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFiltersAndSort() {
    List<Order> filtered = List.from(_orders);

    // Apply active only filter
    if (widget.showActiveOnly) {
      filtered = filtered.where((order) => order.isActive).toList();
    }

    // Apply status filter
    if (_selectedStatus != null) {
      filtered = filtered
          .where((order) => order.status == _selectedStatus)
          .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((order) {
        final query = _searchQuery.toLowerCase();
        return order.orderNumber.toLowerCase().contains(query) ||
            (order.service?.name.toLowerCase().contains(query) ?? false) ||
            (order.store?.name.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'newest':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'amount_high':
        filtered.sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
        break;
      case 'amount_low':
        filtered.sort((a, b) => a.totalAmount.compareTo(b.totalAmount));
        break;
    }

    _filteredOrders = filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          widget.showActiveOnly
              ? 'All Active Orders • $_creatorName'
              : 'All Orders History • $_creatorName',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 4,
        shadowColor: Colors.blue.withValues(alpha: 0.3),
        actions: [
          // Sort dropdown
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.sort, color: Colors.white),
              tooltip: 'Sort by',
              onSelected: (String sortBy) {
                setState(() {
                  _sortBy = sortBy;
                  _applyFiltersAndSort();
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'newest',
                  child: Row(
                    children: [
                      Icon(Icons.schedule, size: 18),
                      SizedBox(width: 8),
                      Text('Newest First'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'oldest',
                  child: Row(
                    children: [
                      Icon(Icons.history, size: 18),
                      SizedBox(width: 8),
                      Text('Oldest First'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'amount_high',
                  child: Row(
                    children: [
                      Icon(Icons.trending_up, size: 18),
                      SizedBox(width: 8),
                      Text('Highest Amount'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'amount_low',
                  child: Row(
                    children: [
                      Icon(Icons.trending_down, size: 18),
                      SizedBox(width: 8),
                      Text('Lowest Amount'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Status filter dropdown
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: PopupMenuButton<OrderStatus?>(
              icon: Icon(
                Icons.filter_list,
                color: _selectedStatus != null ? Colors.yellow : Colors.white,
              ),
              tooltip: 'Filter by status',
              onSelected: (OrderStatus? status) {
                setState(() {
                  _selectedStatus = status;
                  _applyFiltersAndSort();
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: null,
                  child: Row(
                    children: [
                      Icon(Icons.clear_all, size: 18),
                      SizedBox(width: 8),
                      Text('All Statuses'),
                    ],
                  ),
                ),
                ...OrderStatus.values.map(
                  (status) => PopupMenuItem(
                    value: status,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getStatusColor(status),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(status.displayName),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: _loadOrders,
              icon: const Icon(Icons.refresh, color: Colors.white),
              tooltip: 'Refresh',
            ),
          ),
          // Removed currency picker - now using fixed currency symbol
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(child: _buildBody()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreateOrderScreen()),
          ).then((_) => _loadOrders()); // Refresh when returning
        },
        icon: const Icon(Icons.add),
        label: const Text('New Order'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search orders by number, service, or store...',
              prefixIcon: const Icon(Icons.search, color: Colors.blue),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                      },
                      icon: const Icon(Icons.clear, color: Colors.grey),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.blue, width: 2),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
          ),

          const SizedBox(height: 12),

          // Filter chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            alignment: WrapAlignment.spaceBetween,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              Text(
                'Filters:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              // Spacing handled by Wrap's spacing parameter
              if (_selectedStatus != null)
                Chip(
                  avatar: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStatusColor(_selectedStatus!),
                      shape: BoxShape.circle,
                    ),
                  ),
                  label: Text(_selectedStatus!.displayName),
                  onDeleted: () {
                    setState(() {
                      _selectedStatus = null;
                      _applyFiltersAndSort();
                    });
                  },
                  backgroundColor: _getStatusColor(
                    _selectedStatus!,
                  ).withValues(alpha: 0.1),
                ),
              // Spacing handled by Wrap's spacing parameter
              Chip(
                avatar: Icon(_getSortIcon(), size: 16),
                label: Text(_getSortLabel()),
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
              ),
              // Order count chip to make it consistent with other filter elements
              Chip(
                label: Text(
                  '${_filteredOrders.length} orders',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                backgroundColor: Colors.grey.withValues(alpha: 0.1),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getSortIcon() {
    switch (_sortBy) {
      case 'newest':
        return Icons.schedule;
      case 'oldest':
        return Icons.history;
      case 'amount_high':
        return Icons.trending_up;
      case 'amount_low':
        return Icons.trending_down;
      default:
        return Icons.sort;
    }
  }

  String _getSortLabel() {
    switch (_sortBy) {
      case 'newest':
        return 'Newest First';
      case 'oldest':
        return 'Oldest First';
      case 'amount_high':
        return 'Highest Amount';
      case 'amount_low':
        return 'Lowest Amount';
      default:
        return 'Sort';
    }
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading orders...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading orders',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadOrders,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredOrders.isEmpty) {
      // Different messages for no data vs filtered results
      final bool hasOrders = _orders.isNotEmpty;

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              hasOrders
                  ? Icons.search_off
                  : (widget.showActiveOnly
                        ? Icons.track_changes
                        : Icons.history),
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              hasOrders
                  ? 'No matching orders found'
                  : (widget.showActiveOnly
                        ? 'No Active Orders'
                        : 'No Order History'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              hasOrders
                  ? 'Try adjusting your search or filters'
                  : (widget.showActiveOnly
                        ? 'You don\'t have any active orders at the moment.'
                        : 'You haven\'t placed any orders yet.'),
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (hasOrders)
              ElevatedButton.icon(
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _selectedStatus = null;
                    _searchQuery = '';
                    _applyFiltersAndSort();
                  });
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear Filters'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              )
            else
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateOrderScreen(),
                    ),
                  ).then((_) => _loadOrders());
                },
                icon: const Icon(Icons.add),
                label: const Text('Place Your First Order'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        itemCount: _filteredOrders.length,
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    final statusColor = _getStatusColor(order.status);

    // Debug logging
    debugPrint(
      'Building card for order: ${order.orderNumber}, Status: ${order.status}, CanDelete: ${order.canDelete}, CanCancel: ${order.canCancel}',
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      shadowColor: statusColor.withValues(alpha: 0.1),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, statusColor.withValues(alpha: 0.02)],
          ),
          border: Border.all(
            color: statusColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OrderDetailsScreen(orderId: order.id),
              ),
            ).then((_) => _loadOrders());
          },
          borderRadius: BorderRadius.circular(16),
          child: Column(
            children: [
              // Status header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      statusColor.withValues(alpha: 0.1),
                      statusColor.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.15),
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        order.statusIcon,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      order.status.displayName.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: statusColor,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const Spacer(),
                    // Payment status indicator
                    FutureBuilder<bool?>(
                      future: _hasOutstandingPayment(order.id),
                      builder: (context, snapshot) {
                        if (snapshot.data == true) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Payment Due',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    SizedBox(
                      width: 90,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.centerRight,
                        child: Text(
                          '$_currencySymbol ${order.totalAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Order content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order number and service
                    Text(
                      order.orderNumber,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      order.service?.name ?? 'Unknown Service',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Order details in info cards
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoCard(
                            icon: Icons.store,
                            label: 'Store',
                            value: order.store?.name ?? 'Unknown Store',
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildInfoCard(
                            icon: Icons.shopping_cart,
                            label: 'Items',
                            value: '${order.totalItems} items',
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoCard(
                            icon: Icons.calendar_today,
                            label: 'Created',
                            value: _formatDate(order.createdAt),
                            color: Colors.purple,
                          ),
                        ),
                        if (order.pickupDate != null) ...[
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildInfoCard(
                              icon: Icons.schedule,
                              label: 'Pickup',
                              value: _formatPickupDelivery(
                                order.pickupDate!,
                                order.pickupTimeSlot,
                              ),
                              color: Colors.indigo,
                            ),
                          ),
                        ],
                      ],
                    ),

                    // Action buttons for non-cancelled orders
                    if (order.status != OrderStatus.cancelled) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 12),

                      // Payment collection button - only show if payment is needed
                      FutureBuilder<bool?>(
                        future: _hasOutstandingPayment(order.id),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const SizedBox.shrink(); // Hide while loading
                          }

                          final paymentData = snapshot.data;

                          // Show button if:
                          // 1. There's outstanding payment (true), OR
                          // 2. No invoice exists yet (null)
                          // Hide button if invoice exists and is fully paid (false)
                          final shouldShowButton = paymentData != false;

                          if (!shouldShowButton) {
                            return const SizedBox.shrink(); // Hide button for fully paid orders
                          }

                          final hasOutstanding = paymentData == true;

                          return Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: () =>
                                      _openPaymentCollection(order),
                                  icon: Icon(
                                    hasOutstanding
                                        ? Icons.warning
                                        : Icons.payment,
                                    size: 16,
                                  ),
                                  label: Text(
                                    hasOutstanding
                                        ? 'Collect Outstanding Payment'
                                        : 'Collect Payment',
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: hasOutstanding
                                        ? Colors.orange
                                        : Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                            ],
                          );
                        },
                      ),

                      Row(
                        children: [
                          if (order.canCancel) ...[
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _showCancelDialog(order),
                                icon: const Icon(
                                  Icons.cancel_outlined,
                                  size: 16,
                                ),
                                label: const Text('Cancel'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.red,
                                  side: const BorderSide(color: Colors.red),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          if (order.canDelete) ...[
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _showDeleteDialog(order),
                                icon: const Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                ),
                                label: const Text('Delete'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.red[700],
                                  side: BorderSide(color: Colors.red[700]!),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _repeatOrder(order),
                              icon: const Icon(Icons.repeat, size: 16),
                              label: const Text('Repeat'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // Only show update status button for active orders (not delivered or cancelled)
                      if (order.isActive) ...[
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () => _showUpdateStatusDialog(order),
                            icon: const Icon(Icons.update, size: 16),
                            label: const Text('Update Status'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: statusColor,
                              side: BorderSide(color: statusColor),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 14),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.purple; // Order collected from customer
      case OrderStatus.inProcess:
        return Colors.indigo;
      case OrderStatus.readyForPickup:
        return Colors.green; // Ready for customer pickup/delivery
      case OrderStatus.outForDelivery:
        return Colors.amber; // Out for delivery
      case OrderStatus.completed:
        return Colors.teal; // Complete - customer has received laundry
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatPickupDelivery(DateTime date, String? timeSlot) {
    final dateStr = _formatDate(date);
    return timeSlot != null ? '$dateStr $timeSlot' : dateStr;
  }

  void _showCancelDialog(Order order) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.cancel, color: Colors.red),
            SizedBox(width: 8),
            Text('Cancel Order'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to cancel order ${order.orderNumber}?'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for cancellation',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Order'),
          ),
          ElevatedButton(
            onPressed: () => _cancelOrder(order, reasonController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel Order'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelOrder(Order order, String reason) async {
    Navigator.of(context).pop(); // Close dialog

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      await _orderService.cancelOrder(
        orderId: order.id,
        cancelledBy: user.id,
        cancelReason: reason.isEmpty ? 'Cancelled by user' : reason,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order ${order.orderNumber} cancelled successfully'),
            backgroundColor: Colors.orange,
          ),
        );
        _loadOrders(); // Refresh list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _repeatOrder(Order order) async {
    // Show confirmation dialog with order details
    final confirmed = await _showRepeatOrderDialog(order);
    if (!confirmed) return;

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      final newOrder = await _orderService.repeatOrder(
        originalOrderId: order.id,
        userId: user.id,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'New order ${newOrder.orderNumber} created successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
        _loadOrders(); // Refresh list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error repeating order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _showRepeatOrderDialog(Order order) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.repeat,
                color: Colors.blue,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Repeat Order',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Create a new order with the same details?',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 20),
              
              // Order Details Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.receipt_long,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Order Details',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    _buildDetailRow(
                      'Order Number:',
                      order.orderNumber,
                      Icons.confirmation_number,
                    ),
                    _buildDetailRow(
                      'Store/Branch:',
                      order.store?.name ?? 'Unknown Store',
                      Icons.store,
                    ),
                    _buildDetailRow(
                      'Service:',
                      order.service?.name ?? 'Unknown Service',
                      Icons.room_service,
                    ),
                    _buildDetailRow(
                      'Number of Items:',
                      '${order.totalItems} items',
                      Icons.inventory,
                    ),
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      'Total Amount:',
                      '$_currencySymbol${order.totalAmount.toStringAsFixed(2)}',
                      Icons.attach_money,
                      isHighlight: true,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.blue[700],
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'A new order will be created with the same items, service, and addresses. You can modify details after creation.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(true),
            icon: const Icon(Icons.repeat, size: 16),
            label: const Text('Create Order'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  Widget _buildDetailRow(
    String label, 
    String value, 
    IconData icon, {
    bool isHighlight = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: isHighlight ? Colors.green[600] : Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                fontWeight: isHighlight ? FontWeight.bold : FontWeight.w500,
                color: isHighlight ? Colors.green[700] : Colors.black87,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Order order) {
    debugPrint('Delete dialog triggered for order: ${order.orderNumber}');
    debugPrint('Order status: ${order.status}, canDelete: ${order.canDelete}');
    
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red),
            SizedBox(width: 8),
            Text('Delete Order'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to permanently delete order ${order.orderNumber}?',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red[700], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. All order data including items, status history, and related invoices will be permanently removed.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for deletion',
                hintText: 'Please provide a reason for deleting this order...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.edit_note),
              ),
              maxLines: 3,
              textCapitalization: TextCapitalization.sentences,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteOrder(order, reasonController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete Order'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteOrder(Order order, String deleteReason) async {
    Navigator.of(context).pop(); // Close dialog
    
    // Validate delete reason
    if (deleteReason.trim().isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please provide a reason for deletion'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 12),
              Text('Deleting order...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 30), // Long duration for loading
        ),
      );
    }

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      debugPrint(
        'Attempting to delete order: ${order.id} (${order.orderNumber})',
      );
      debugPrint('Order status: ${order.status}');
      debugPrint('Can delete: ${order.canDelete}');
      debugPrint('Delete reason: $deleteReason');

      await _orderService.deleteOrder(
        orderId: order.id,
        deletedBy: user.id,
        deleteReason: deleteReason.trim(),
      );

      if (mounted) {
        // Clear loading snackbar
        ScaffoldMessenger.of(context).clearSnackBars();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Order ${order.orderNumber} deleted successfully'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
        _loadOrders(); // Refresh list
      }
    } catch (e) {
      debugPrint('Delete order error: $e');

      if (mounted) {
        // Clear loading snackbar
        ScaffoldMessenger.of(context).clearSnackBars();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error deleting order: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _showUpdateStatusDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => _UpdateStatusDialog(
        order: order,
        onUpdate: _updateOrderStatus,
        getStatusColor: _getStatusColor,
      ),
    );
  }

  Future<void> _updateOrderStatus(
    Order order,
    OrderStatus newStatus,
    String? notes,
  ) async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      await _orderService.updateOrderStatus(
        orderId: order.id,
        newStatus: newStatus,
        changedBy: user.id,
        notes: notes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
        _loadOrders(); // Refresh list
      }
    } catch (e) {
      // If backend enforces payment-before-status rules, show a friendly dialog
      final message = e.toString();
      final paymentRequired =
          message.toLowerCase().contains('must be paid') ||
          message.toLowerCase().contains('payment') &&
              message.toLowerCase().contains('before') ||
          message.contains('23514'); // constraint violation code commonly used

      if (paymentRequired) {
        await _showPaymentRequiredDialog(order, newStatus);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating order status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showPaymentRequiredDialog(
    Order order,
    OrderStatus targetStatus,
  ) async {
    double? outstanding;
    String message =
        'This order must be paid before changing status to "${targetStatus.displayName}".';

    try {
      final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
      outstanding = invoice.effectiveOutstandingAmount;
    } catch (_) {
      // No invoice yet – allow creating during payment
    }

    if (outstanding != null) {
      message =
          '$message\n\nOutstanding amount: $_currencySymbol${outstanding.toStringAsFixed(2)}';
    } else {
      message =
          '$message\n\nNo invoice exists yet. You can create one and collect payment now.';
    }

    if (!mounted) return;
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange),
            SizedBox(width: 8),
            Text('Payment Required'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(ctx).pop();
              await _openPaymentCollection(order);
            },
            icon: const Icon(Icons.payment),
            label: const Text('Collect Payment'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if an order has outstanding payment
  /// Returns true if there's outstanding payment, false if fully paid, null if no invoice
  Future<bool?> _hasOutstandingPayment(String orderId) async {
    try {
      final invoice = await _invoiceService.getInvoiceByOrderId(orderId);
      return invoice.hasOutstandingAmount;
    } catch (e) {
      // Invoice doesn't exist - return null to indicate no invoice
      return null;
    }
  }

  /// Open payment collection for an order
  Future<void> _openPaymentCollection(Order order) async {
    try {
      // Try to get existing invoice, if not found, PaymentReceivePage will create one
      String? invoiceId;
      double? prefilledAmount;

      try {
        final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
        invoiceId = invoice.id;
        prefilledAmount = invoice.effectiveOutstandingAmount;
      } catch (e) {
        // Invoice doesn't exist - PaymentReceivePage will create one
        debugPrint(
          'No invoice found for order ${order.id}, will create during payment: $e',
        );
      }

      if (!mounted) return;

      final result = await Navigator.of(context).push<Map<String, dynamic>>(
        MaterialPageRoute(
          builder: (context) => PaymentReceivePage(
            orderId: order.id,
            invoiceId: invoiceId,
            prefilledAmount: prefilledAmount,
            isPickupFlow:
                order.status == OrderStatus.readyForPickup ||
                order.status == OrderStatus.outForDelivery,
          ),
        ),
      );

      if (result != null && result['success'] == true) {
        // Refresh the orders list to update payment status
        _loadOrders();

        if (mounted) {
          final statusMessage = result['statusUpdated'] == true
              ? ' Order status updated to ${result['newStatus']}.'
              : '';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Payment of $_currencySymbol${result['amount']?.toStringAsFixed(2) ?? '0.00'} recorded successfully!$statusMessage',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening payment collection: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _UpdateStatusDialog extends StatefulWidget {
  final Order order;
  final Function(Order, OrderStatus, String?) onUpdate;
  final Color Function(OrderStatus) getStatusColor;

  const _UpdateStatusDialog({
    required this.order,
    required this.onUpdate,
    required this.getStatusColor,
  });

  @override
  State<_UpdateStatusDialog> createState() => _UpdateStatusDialogState();
}

class _UpdateStatusDialogState extends State<_UpdateStatusDialog> {
  OrderStatus? selectedStatus;
  final notesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // Get available statuses based on workflow
    List<OrderStatus> availableStatuses;

    // Check if this is an in-store order (no pickup address)
    bool isInStoreOrder = widget.order.pickupAddressId == null;

    switch (widget.order.status) {
      case OrderStatus.pending:
        availableStatuses = [OrderStatus.accepted, OrderStatus.cancelled];
        break;
      case OrderStatus.accepted:
        // For in-store orders, skip the pickup step
        availableStatuses = isInStoreOrder
            ? [OrderStatus.inProcess, OrderStatus.cancelled]
            : [OrderStatus.pickedUp, OrderStatus.cancelled];
        break;
      case OrderStatus.pickedUp:
        availableStatuses = [OrderStatus.inProcess];
        break;
      case OrderStatus.inProcess:
        availableStatuses = [OrderStatus.readyForPickup];
        break;
      case OrderStatus.readyForPickup:
        // Ready for pickup/delivery - choice between customer pickup or delivery
        availableStatuses = [OrderStatus.completed, OrderStatus.outForDelivery];
        break;
      case OrderStatus.outForDelivery:
        // Out for delivery - can only mark as completed
        availableStatuses = [OrderStatus.completed];
        break;
      case OrderStatus.completed:
        // No further status changes possible
        availableStatuses = [];
        break;
      case OrderStatus.cancelled:
        // No further status changes possible
        availableStatuses = [];
        break;
    }

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: const Row(
        children: [
          Icon(Icons.update, color: Colors.indigo),
          SizedBox(width: 8),
          Text('Update Order Status'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Current status: ${widget.order.status.displayName}'),
          const SizedBox(height: 16),

          // Special handling for ready_for_pickup status
          if (widget.order.status == OrderStatus.readyForPickup)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'How will the customer receive their laundry?',
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
                ),
                const SizedBox(height: 12),
                // Customer Pickup Option
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        selectedStatus = OrderStatus.completed;
                      });
                    },
                    icon: const Icon(Icons.person),
                    label: const Text('Customer Picked Up'),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: selectedStatus == OrderStatus.completed
                          ? Colors.green.withValues(alpha: 0.1)
                          : null,
                      foregroundColor: selectedStatus == OrderStatus.completed
                          ? Colors.green
                          : null,
                      side: BorderSide(
                        color: selectedStatus == OrderStatus.completed
                            ? Colors.green
                            : Colors.grey,
                        width: selectedStatus == OrderStatus.completed ? 2 : 1,
                      ),
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Delivery Option
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        selectedStatus = OrderStatus.outForDelivery;
                      });
                    },
                    icon: const Icon(Icons.local_shipping),
                    label: const Text('Send for Delivery'),
                    style: OutlinedButton.styleFrom(
                      backgroundColor:
                          selectedStatus == OrderStatus.outForDelivery
                          ? Colors.amber.withValues(alpha: 0.1)
                          : null,
                      foregroundColor:
                          selectedStatus == OrderStatus.outForDelivery
                          ? Colors.amber[700]
                          : null,
                      side: BorderSide(
                        color: selectedStatus == OrderStatus.outForDelivery
                            ? Colors.amber[700]!
                            : Colors.grey,
                        width: selectedStatus == OrderStatus.outForDelivery
                            ? 2
                            : 1,
                      ),
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
              ],
            )
          else
            // Normal status selector for other statuses
            DropdownButtonFormField<OrderStatus>(
              value: selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Select New Status',
                border: OutlineInputBorder(),
              ),
              hint: const Text('Choose a new status'),
              items: availableStatuses
                  .map(
                    (status) => DropdownMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: widget.getStatusColor(status),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(status.displayName),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  selectedStatus = value;
                });
              },
            ),

          const SizedBox(height: 16),
          TextField(
            controller: notesController,
            decoration: const InputDecoration(
              labelText: 'Notes (optional)',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: selectedStatus != null
              ? () {
                  widget.onUpdate(
                    widget.order,
                    selectedStatus!,
                    notesController.text.isNotEmpty
                        ? notesController.text
                        : null,
                  );
                  Navigator.of(context).pop();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.indigo,
            foregroundColor: Colors.white,
          ),
          child: const Text('Update Status'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }
}
