import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/garment_model.dart';

class GarmentService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Get all active garments
  static Future<List<Garment>> getAllGarments() async {
    try {
      final response = await _supabase
          .from('garments')
          .select()
          .eq('is_active', true)
          .order('category')
          .order('name');

      return (response as List)
          .map((json) => Garment.fromJson(json))
          .toList();
    } catch (e) {
      // Return mock data if garments table doesn't exist yet
      return _getMockGarments();
    }
  }

  // Get garments by category
  static Future<Map<String, List<Garment>>> getGarmentsByCategory() async {
    try {
      final garments = await getAllGarments();
      final Map<String, List<Garment>> categorized = {};
      
      for (final garment in garments) {
        if (!categorized.containsKey(garment.category)) {
          categorized[garment.category] = [];
        }
        categorized[garment.category]!.add(garment);
      }
      
      return categorized;
    } catch (e) {
      // Return mock categorized data
      final garments = _getMockGarments();
      final Map<String, List<Garment>> categorized = {};
      
      for (final garment in garments) {
        if (!categorized.containsKey(garment.category)) {
          categorized[garment.category] = [];
        }
        categorized[garment.category]!.add(garment);
      }
      
      return categorized;
    }
  }

  // Search garments by name
  static Future<List<Garment>> searchGarments(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllGarments();
      }

      final response = await _supabase
          .from('garments')
          .select()
          .eq('is_active', true)
          .ilike('name', '%$query%')
          .order('name');

      return (response as List)
          .map((json) => Garment.fromJson(json))
          .toList();
    } catch (e) {
      // Return filtered mock data
      final mockGarments = _getMockGarments();
      final lowerQuery = query.toLowerCase();
      return mockGarments.where((garment) =>
        garment.name.toLowerCase().contains(lowerQuery)
      ).toList();
    }
  }

  // Get garment by ID
  static Future<Garment?> getGarmentById(String id) async {
    try {
      final response = await _supabase
          .from('garments')
          .select()
          .eq('id', id)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;
      return Garment.fromJson(response);
    } catch (e) {
      // Return from mock data
      final mockGarments = _getMockGarments();
      try {
        return mockGarments.firstWhere((garment) => garment.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Create new garment
  static Future<Garment> createGarment({
    required String name,
    required String category,
    String? description,
    double? basePrice,
    String? icon,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      final response = await _supabase
          .from('garments')
          .insert({
            'name': name,
            'category': category,
            'description': description,
            'base_price': basePrice ?? 0.0,
            'icon': icon,
            'created_by': currentUser?.id,
          })
          .select()
          .single();

      return Garment.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create garment: $e');
    }
  }

  // Update garment
  static Future<Garment> updateGarment(Garment garment) async {
    try {
      final response = await _supabase
          .from('garments')
          .update(garment.toJson())
          .eq('id', garment.id)
          .select()
          .single();

      return Garment.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update garment: $e');
    }
  }

  // Delete garment (soft delete)
  static Future<void> deleteGarment(String garmentId) async {
    try {
      await _supabase
          .from('garments')
          .update({'is_active': false})
          .eq('id', garmentId);
    } catch (e) {
      throw Exception('Failed to delete garment: $e');
    }
  }

  // Mock data for when garments table doesn't exist yet
  static List<Garment> _getMockGarments() {
    return [
      // Clothing
      Garment(
        id: '1',
        name: 'T-Shirt',
        category: 'clothing',
        description: 'Regular cotton t-shirt',
        icon: 'checkroom',
        garmentType: 'shirts',
        material: 'cotton',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '2',
        name: 'Dress Shirt',
        category: 'clothing',
        description: 'Formal dress shirt',
        icon: 'checkroom',
        garmentType: 'shirts',
        material: 'cotton',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '3',
        name: 'Jeans',
        category: 'clothing',
        description: 'Denim jeans',
        icon: 'checkroom',
        garmentType: 'pants',
        material: 'denim',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '4',
        name: 'Suit Jacket',
        category: 'clothing',
        description: 'Formal suit jacket',
        icon: 'checkroom',
        garmentType: 'jackets',
        material: 'wool',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '5',
        name: 'Dress',
        category: 'clothing',
        description: 'Ladies dress',
        icon: 'checkroom',
        garmentType: 'dresses',
        material: 'polyester',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      
      // Household
      Garment(
        id: '6',
        name: 'Bed Sheet',
        category: 'household',
        description: 'Single bed sheet',
        icon: 'bed',
        garmentType: 'bed_sheets',
        material: 'cotton',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '7',
        name: 'Pillowcase',
        category: 'household',
        description: 'Standard pillowcase',
        icon: 'bed',
        garmentType: 'pillowcases',
        material: 'cotton',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '8',
        name: 'Towel',
        category: 'household',
        description: 'Bath towel',
        icon: 'dry',
        garmentType: 'towels',
        material: 'cotton',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '9',
        name: 'Comforter',
        category: 'household',
        description: 'Large comforter/duvet',
        icon: 'bed',
        garmentType: 'blankets',
        material: 'polyester',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      
      // Special
      Garment(
        id: '10',
        name: 'Wedding Dress',
        category: 'special',
        description: 'Delicate wedding dress',
        icon: 'favorite',
        garmentType: 'wedding_dress',
        material: 'silk',
        requiresSpecialCare: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '11',
        name: 'Leather Jacket',
        category: 'special',
        description: 'Leather garment requiring special care',
        icon: 'checkroom',
        garmentType: 'leather_items',
        material: 'leather',
        requiresSpecialCare: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Garment(
        id: '12',
        name: 'Silk Blouse',
        category: 'special',
        description: 'Delicate silk blouse',
        icon: 'checkroom',
        garmentType: 'silk_items',
        material: 'silk',
        requiresSpecialCare: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}
