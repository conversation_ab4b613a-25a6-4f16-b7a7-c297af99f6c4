import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:currency_picker/currency_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class UserSettings {
  final String? companyName;
  final String? currencyCode;
  final DateTime? updatedAt;

  UserSettings({
    this.companyName,
    this.currencyCode,
    this.updatedAt,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      companyName: json['company_name'] as String?,
      currencyCode: json['currency_code'] as String?,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'company_name': companyName,
      'currency_code': currencyCode,
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  bool get isEmpty => companyName == null && currencyCode == null;
}

class SettingsService {
  final SupabaseClient supabase;

  SettingsService({required this.supabase});
  static const String _themeKey = 'theme_mode';
  static const String _currencyKey = 'selected_currency';
  static const String _languageKey = 'selected_language';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _biometricKey = 'biometric_enabled';
  static const String _autoSyncKey = 'auto_sync_enabled';

  // Supabase user settings methods
  Future<UserSettings?> getSettings() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return null;

      final response = await supabase
          .from('user_settings')
          .select()
          .eq('user_id', user.id)
          .maybeSingle();

      if (response == null) return null;
      return UserSettings.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting settings: $e');
      }
      return null;
    }
  }

  Future<void> upsertSettings({
    String? companyName,
    String? currencyCode,
  }) async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final Map<String, dynamic> data = {
        'user_id': user.id,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (companyName != null) data['company_name'] = companyName;
      if (currencyCode != null) data['currency_code'] = currencyCode;

      await supabase
          .from('user_settings')
          .upsert(data);
    } catch (e) {
      if (kDebugMode) {
        print('Error upserting settings: $e');
      }
      rethrow;
    }
  }

  // Theme settings
  Future<String> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_themeKey) ?? 'system';
  }

  Future<void> setThemeMode(String themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, themeMode);
  }

  // Currency settings
  Future<String?> getSelectedCurrencyCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_currencyKey);
  }

  Future<Currency?> getSelectedCurrency() async {
    final currencyCode = await getSelectedCurrencyCode();
    if (currencyCode != null) {
      try {
        // Use a fallback approach since CurrencyService methods may vary
        return Currency.from(json: {'code': currencyCode});
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  Future<void> setSelectedCurrency(Currency currency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currencyKey, currency.code);
  }

  // Language settings
  Future<String> getSelectedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? 'en';
  }

  Future<void> setSelectedLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }

  // Notification settings
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsKey) ?? true;
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsKey, enabled);
  }

  // Biometric settings
  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_biometricKey) ?? false;
  }

  Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_biometricKey, enabled);
  }

  // Auto sync settings
  Future<bool> isAutoSyncEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoSyncKey) ?? true;
  }

  Future<void> setAutoSyncEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoSyncKey, enabled);
  }

  // Clear all settings (useful for logout/reset)
  Future<void> clearAllSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Get all settings as a map
  Future<Map<String, dynamic>> getAllSettings() async {
    final currency = await getSelectedCurrency();
    
    return {
      'theme_mode': await getThemeMode(),
      'currency': currency?.toJson(),
      'language': await getSelectedLanguage(),
      'notifications_enabled': await areNotificationsEnabled(),
      'biometric_enabled': await isBiometricEnabled(),
      'auto_sync_enabled': await isAutoSyncEnabled(),
    };
  }

  // Backup settings to a map (for export/sync)
  Future<Map<String, dynamic>> exportSettings() async {
    return await getAllSettings();
  }

  // Restore settings from a map (for import/sync)
  Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings.containsKey('theme_mode')) {
      await setThemeMode(settings['theme_mode'] as String);
    }
    
    if (settings.containsKey('currency') && settings['currency'] != null) {
      try {
        final currencyData = settings['currency'] as Map<String, dynamic>;
        final currency = Currency.from(json: currencyData);
        await setSelectedCurrency(currency);
      } catch (e) {
        // Ignore invalid currency data
      }
    }
    
    if (settings.containsKey('language')) {
      await setSelectedLanguage(settings['language'] as String);
    }
    
    if (settings.containsKey('notifications_enabled')) {
      await setNotificationsEnabled(settings['notifications_enabled'] as bool);
    }
    
    if (settings.containsKey('biometric_enabled')) {
      await setBiometricEnabled(settings['biometric_enabled'] as bool);
    }
    
    if (settings.containsKey('auto_sync_enabled')) {
      await setAutoSyncEnabled(settings['auto_sync_enabled'] as bool);
    }
  }
}
