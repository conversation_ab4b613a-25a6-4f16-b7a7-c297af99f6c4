import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';

class InvoiceService {
  final SupabaseClient _supabase;

  InvoiceService({required SupabaseClient supabase}) : _supabase = supabase;

  // ============================================
  // INVOICE OPERATIONS
  // ============================================

  /// Get or create invoice for an order
  Future<Invoice> getOrCreateInvoiceForOrder(String orderId) async {
    try {
      // First, try to get existing invoice
      final response = await _supabase
          .from('invoices')
          .select()
          .eq('order_id', orderId)
          .maybeSingle();

      if (response != null) {
        return Invoice.fromJson(response);
      }

      // If no invoice exists, we need to create one
      // Get order details first
      final orderResponse = await _supabase
          .from('orders')
          .select()
          .eq('id', orderId)
          .single();

      // Create invoice for the order
      final invoiceData = {
        'order_id': orderId,
        'user_id': orderResponse['user_id'],
        'subtotal': orderResponse['subtotal'] ?? 0.0,
        'tax_amount': orderResponse['tax_amount'] ?? 0.0,
        'delivery_fee': orderResponse['delivery_fee'] ?? 0.0,
        'discount_amount': orderResponse['discount_amount'] ?? 0.0,
        'total_amount': orderResponse['total_amount'] ?? 0.0,
        'payment_status': 'pending',
      };

      final newInvoice = await _supabase
          .from('invoices')
          .insert(invoiceData)
          .select()
          .single();

      return Invoice.fromJson(newInvoice);
    } catch (e) {
      throw 'Error getting or creating invoice: $e';
    }
  }

  /// Get invoice with payment summary
  Future<Invoice> getInvoiceWithPayments(String invoiceId) async {
    try {
      final response = await _supabase.rpc(
        'get_invoice_with_payments',
        params: {'invoice_uuid': invoiceId},
      );

      if (response == null || (response is List && response.isEmpty)) {
        throw 'Invoice not found';
      }

      final Map<String, dynamic> row =
          (response is List) ? Map<String, dynamic>.from(response.first) : Map<String, dynamic>.from(response as Map);
      return Invoice.fromJson(row);
    } catch (e) {
      // Fallback path: compute summary without RPC (handles schema drift e.g. missing invoice_sequence_number)
      try {
        final invoiceRow = await _supabase
            .from('invoices')
            .select()
            .eq('id', invoiceId)
            .single();

        final paymentRows = await _supabase
            .from('payments')
            .select('amount')
            .eq('invoice_id', invoiceId);

        final paymentsList = (paymentRows as List);
        final totalPaid = paymentsList.fold<double>(0.0, (sum, p) => sum + ((p['amount'] as num?)?.toDouble() ?? 0.0));
        final outstanding = ((invoiceRow['total_amount'] as num?)?.toDouble() ?? 0.0) - totalPaid;

        final combined = <String, dynamic>{
          ...Map<String, dynamic>.from(invoiceRow as Map),
          'total_paid': totalPaid,
          'outstanding_amount': outstanding,
          'payments_count': paymentsList.length,
        };
        return Invoice.fromJson(combined);
      } catch (_) {
        throw 'Error fetching invoice with payments: $e';
      }
    }
  }

  /// Get invoice by order ID with payment summary
  Future<Invoice> getInvoiceByOrderId(String orderId) async {
    try {
      // Get invoice ID first
      final invoiceResponse = await _supabase
          .from('invoices')
          .select('id')
          .eq('order_id', orderId)
          .single();

      final invoiceId = invoiceResponse['id'] as String;
      return await getInvoiceWithPayments(invoiceId);
    } catch (e) {
      throw 'Error fetching invoice by order ID: $e';
    }
  }

  /// Get payments for an invoice
  Future<List<Payment>> getInvoicePayments(String invoiceId) async {
    try {
      final response = await _supabase.rpc(
        'get_invoice_payments',
        params: {'invoice_uuid': invoiceId},
      );

      if (response == null) return [];

      return (response as List)
          .map((payment) => Payment.fromJson(Map<String, dynamic>.from(payment)))
          .toList();
    } catch (e) {
      throw 'Error fetching invoice payments: $e';
    }
  }

  // ============================================
  // PAYMENT OPERATIONS
  // ============================================

  /// Create a new payment
  Future<Payment> createPayment({
    required String invoiceId,
    required String orderId,
    required double amount,
    required PaymentMethod method,
    String? receiptNumber,
    String? transactionCode,
    String? channelTarget,
    String? payerPhone,
    String? notes,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw 'User not authenticated';
      }

      // Validate required fields based on payment method
      // Receipt numbers are now optional for all payment methods
      
      if (method.isMpesa && (transactionCode == null || transactionCode.isEmpty)) {
        throw 'Transaction code is required for M-Pesa payments';
      }

      // Check if M-Pesa transaction code already exists
      if (method.isMpesa && transactionCode != null) {
        final existing = await _supabase
            .from('payments')
            .select('id')
            .eq('transaction_code', transactionCode)
            .maybeSingle();

        if (existing != null) {
          throw 'Transaction code already exists. Please check for duplicate payments.';
        }
      }

      // Get invoice to validate outstanding amount
      final invoice = await getInvoiceWithPayments(invoiceId);
      if (amount > invoice.effectiveOutstandingAmount) {
        throw 'Payment amount exceeds outstanding balance of ${invoice.effectiveOutstandingAmount.toStringAsFixed(2)}';
      }

      if (amount <= 0) {
        throw 'Payment amount must be greater than 0';
      }

      final paymentData = {
        'invoice_id': invoiceId,
        'order_id': orderId,
        'amount': amount,
        'method': method.dbValue,
        'receipt_number': receiptNumber,
        'transaction_code': transactionCode,
        'channel_target': channelTarget,
        'payer_phone': payerPhone,
        'notes': notes,
        'created_by': user.id,
      };

      final response = await _supabase
          .from('payments')
          .insert(paymentData)
          .select()
          .single();

      return Payment.fromJson(response);
    } catch (e) {
      throw 'Error creating payment: $e';
    }
  }

  /// Update a payment
  Future<Payment> updatePayment({
    required String paymentId,
    double? amount,
    PaymentMethod? method,
    String? receiptNumber,
    String? transactionCode,
    String? channelTarget,
    String? payerPhone,
    String? notes,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (amount != null) updateData['amount'] = amount;
      if (method != null) updateData['method'] = method.dbValue;
      if (receiptNumber != null) updateData['receipt_number'] = receiptNumber;
      if (transactionCode != null) updateData['transaction_code'] = transactionCode;
      if (channelTarget != null) updateData['channel_target'] = channelTarget;
      if (payerPhone != null) updateData['payer_phone'] = payerPhone;
      if (notes != null) updateData['notes'] = notes;

      final response = await _supabase
          .from('payments')
          .update(updateData)
          .eq('id', paymentId)
          .select()
          .single();

      return Payment.fromJson(response);
    } catch (e) {
      throw 'Error updating payment: $e';
    }
  }

  /// Delete a payment
  Future<void> deletePayment(String paymentId) async {
    try {
      await _supabase
          .from('payments')
          .delete()
          .eq('id', paymentId);
    } catch (e) {
      throw 'Error deleting payment: $e';
    }
  }

  // ============================================
  // UTILITY FUNCTIONS
  // ============================================

  /// Get outstanding amount for an invoice
  Future<double> getOutstandingAmount(String invoiceId) async {
    try {
      final invoice = await getInvoiceWithPayments(invoiceId);
      return invoice.effectiveOutstandingAmount;
    } catch (e) {
      throw 'Error getting outstanding amount: $e';
    }
  }

  /// Check if an order is fully paid
  Future<bool> isOrderFullyPaid(String orderId) async {
    try {
      final invoice = await getInvoiceByOrderId(orderId);
      return invoice.isPaid;
    } catch (e) {
      throw 'Error checking payment status: $e';
    }
  }

  /// Get all unpaid invoices
  /// If limit is provided, returns only that many invoices
  Future<List<Invoice>> getUnpaidInvoices({int? limit}) async {
    try {
      var query = _supabase
          .from('invoices')
          .select()
          .inFilter('payment_status', ['pending', 'partial'])
          .order('created_at', ascending: false);
      
      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return (response as List)
          .map((invoice) => Invoice.fromJson(invoice))
          .toList();
    } catch (e) {
      throw 'Error fetching unpaid invoices: $e';
    }
  }

  /// Get invoices for a specific customer
  Future<List<Invoice>> getCustomerInvoices(String customerId) async {
    try {
      final response = await _supabase
          .from('invoices')
          .select()
          .eq('user_id', customerId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((invoice) => Invoice.fromJson(invoice))
          .toList();
    } catch (e) {
      throw 'Error fetching customer invoices: $e';
    }
  }

  /// Get payment summary for date range without custom RPC
  Future<List<Map<String, dynamic>>> getPaymentSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = _supabase
          .from('payments')
          .select('method, amount, created_at');

      if (startDate != null) {
        query = query.gte('created_at', startDate.toIso8601String());
      }
      if (endDate != null) {
        query = query.lte('created_at', endDate.toIso8601String());
      }

      final rows = await query;

      // Aggregate in Dart by method
      final Map<String, Map<String, dynamic>> byMethod = {};
      for (final row in rows as List) {
        final method = row['method'] as String;
        final amount = (row['amount'] as num).toDouble();
        final entry = byMethod.putIfAbsent(method, () => {
              'method': method,
              'count': 0,
              'total': 0.0,
            });
        entry['count'] = (entry['count'] as int) + 1;
        entry['total'] = ((entry['total'] as double) + amount);
      }

      // Convert to list sorted by total desc
      final result = byMethod.values.toList()
        ..sort((a, b) => (b['total'] as double).compareTo(a['total'] as double));

      return result;
    } catch (e) {
      throw 'Error getting payment summary: $e';
    }
  }

  /// Validate M-Pesa transaction code format (basic validation)
  bool isValidMpesaTransactionCode(String code) {
    // M-Pesa transaction codes are typically 10 characters, alphanumeric
    final regex = RegExp(r'^[A-Z0-9]{10}$');
    return regex.hasMatch(code.toUpperCase());
  }

  /// Generate receipt number for cash payments
  String generateCashReceiptNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'CASH-$timestamp';
  }
}
