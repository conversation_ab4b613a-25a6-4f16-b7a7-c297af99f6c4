import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/order_model.dart';
import '../models/garment_model.dart';
import '../models/service_model.dart';

class OrderService {
  final SupabaseClient _supabase;

  OrderService({required SupabaseClient supabase}) : _supabase = supabase;

  // Simple UUID v4 validator (case-insensitive)
  bool _isValidUuid(String? value) {
    if (value == null || value.isEmpty) return false;
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
      caseSensitive: false,
    );
    return uuidRegex.hasMatch(value.trim());
  }

  // Create a new order
  Future<Order> createOrder({
    required String userId,
    String? customerId,
    String? storeId,
    required String serviceId,
    required List<OrderItem> items,
    String? pickupAddressId,
    String? deliveryAddressId,
    DateTime? pickupDate,
    String? pickupTimeSlot,
    DateTime? deliveryDate,
    String? deliveryTimeSlot,
    String? specialInstructions,
    double discountAmount = 0.0,
    double? customAmount,
    String? customPricingMode,
  }) async {
    try {
      // Ensure user is authenticated
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Fetch the service to get pricing information
      final serviceResponse = await _supabase
          .from('services')
          .select()
          .eq('id', serviceId)
          .maybeSingle();

      if (serviceResponse == null) {
        throw Exception('Service not found with ID: $serviceId');
      }

      final service = Service.fromJson(serviceResponse);

      // Recalculate item prices based on custom pricing or service pricing
      final recalculatedItems = <OrderItem>[];
      double subtotal = 0.0;

      for (final item in items) {
        double unitPrice = 0.0;

        // Use custom pricing if provided
        if (customAmount != null && customAmount > 0) {
          if (customPricingMode == 'per_kg') {
            unitPrice = customAmount; // Per kg rate
          } else {
            // Fixed pricing - distribute across all items
            final totalItems = items.fold(
              0,
              (sum, item) => sum + item.quantity,
            );
            unitPrice = totalItems > 0 ? customAmount / totalItems : 0.0;
          }
        } else {
          // Use standard service pricing
          switch (service.pricingType) {
            case 'per_item':
              unitPrice = service.pricePerItem ?? 0.0;
              break;
            case 'per_kg':
              // For per_kg pricing, assume 1kg per item unless weight is specified
              // In a real implementation, you might want to add weight to OrderItem
              unitPrice = service.pricePerKg ?? 0.0;
              break;
            case 'fixed':
              // For fixed pricing, distribute the base price across all items
              final totalItems = items.fold(
                0,
                (sum, item) => sum + item.quantity,
              );
              unitPrice = totalItems > 0 ? service.basePrice / totalItems : 0.0;
              break;
            default:
              unitPrice = 0.0;
          }
        }

        final recalculatedItem = OrderItem(
          garmentId: item.garmentId,
          garment: item.garment,
          quantity: item.quantity,
          unitPrice: unitPrice,
          notes: item.notes,
        );

        recalculatedItems.add(recalculatedItem);
        subtotal += recalculatedItem.totalPrice;
      }

      // Validate required IDs exist in database
      final userExists = await _supabase
          .from('profiles')
          .select('id')
          .eq('id', userId)
          .maybeSingle();

      if (userExists == null) {
        throw Exception('User not found with ID: $userId');
      }

      // Check and handle customer address IDs (use customer_addresses table)
      if (pickupAddressId != null) {
        try {
          final address = await _supabase
              .from('customer_addresses')
              .select('id, customer_id')
              .eq('id', pickupAddressId)
              .maybeSingle();

          if (address == null) {
            debugPrint(
              'Pickup address not found in customer_addresses: $pickupAddressId. It will be ignored.',
            );
            pickupAddressId = null;
          } else if (customerId != null &&
              address['customer_id'] != customerId) {
            debugPrint(
              'Pickup address $pickupAddressId does not belong to customer $customerId. It will be ignored.',
            );
            pickupAddressId = null;
          }
        } catch (e) {
          debugPrint('Error checking pickup address: $e');
          pickupAddressId = null;
        }
      }

      // Validate delivery address (customer_addresses)
      if (deliveryAddressId != null) {
        try {
          final address = await _supabase
              .from('customer_addresses')
              .select('id, customer_id')
              .eq('id', deliveryAddressId)
              .maybeSingle();
          if (address == null) {
            debugPrint(
              'Delivery address not found in customer_addresses: $deliveryAddressId. It will be ignored.',
            );
            deliveryAddressId = null;
          } else if (customerId != null &&
              address['customer_id'] != customerId) {
            debugPrint(
              'Delivery address $deliveryAddressId does not belong to customer $customerId. It will be ignored.',
            );
            deliveryAddressId = null;
          }
        } catch (e) {
          debugPrint('Error checking delivery address: $e');
          deliveryAddressId = null;
        }
      }

      // Validate store id (set to null if invalid)
      if (storeId != null && storeId.isNotEmpty) {
        try {
          final storeExists = await _supabase
              .from('stores')
              .select('id')
              .eq('id', storeId)
              .maybeSingle();
          if (storeExists == null) {
            debugPrint('Store not found: $storeId. It will be ignored.');
            storeId = null;
          }
        } catch (e) {
          debugPrint('Error checking store: $e');
          storeId = null;
        }
      }

      // For staff creating orders for customers, we need to use the current user's ID
      // to satisfy RLS policies, then we can update the user_id if needed
      final orderUserId = userId;

      // Calculate totals
      final taxAmount = subtotal * 0.1; // 10% tax
      final deliveryFee = deliveryDate != null ? 5.0 : 0.0;
      final totalAmount = subtotal + taxAmount + deliveryFee - discountAmount;

      // Create order
      final orderData = {
        'user_id': orderUserId,
        'store_id': storeId,
        'service_id': serviceId.isNotEmpty ? serviceId : null,
        'status': 'pending',
        'pickup_address_id': pickupAddressId,
        'delivery_address_id': deliveryAddressId,
        'pickup_date': pickupDate?.toIso8601String().split('T')[0],
        'pickup_time_slot': pickupTimeSlot,
        'delivery_date': deliveryDate?.toIso8601String().split('T')[0],
        'delivery_time_slot': deliveryTimeSlot,
        'subtotal': subtotal,
        'tax_amount': taxAmount,
        'delivery_fee': deliveryFee,
        'discount_amount': discountAmount,
        'total_amount': totalAmount,
        'pay_on_delivery': false,
        'special_instructions': specialInstructions,
      };

      final response = await _supabase
          .from('orders')
          .insert(orderData)
          .select()
          .single();

      final orderId = response['id'] as String;

      // Ensure garments exist in database and get final garment IDs
      final List<Map<String, dynamic>> itemsData = [];
      for (final item in recalculatedItems) {
        String finalGarmentId = item.garmentId;
        String garmentName = item.garment.name;
        String garmentCategory = item.garment.category;

        // Check if garment exists in database, if not create it or use fallback
        try {
          // First check if the garment exists by ID (only if it's a valid UUID)
          Map<String, dynamic>? existingGarmentById;
          if (_isValidUuid(item.garmentId)) {
            existingGarmentById = await _supabase
                .from('garments')
                .select()
                .eq('id', item.garmentId)
                .maybeSingle();
          }

          if (existingGarmentById != null) {
            // Garment exists, use it
            finalGarmentId = existingGarmentById['id'] as String;
            debugPrint('Using existing garment: $finalGarmentId');
          } else {
            // Try to find a garment by name as fallback
            final existingGarmentByName = await _supabase
                .from('garments')
                .select()
                .ilike('name', '%$garmentName%')
                .limit(1)
                .maybeSingle();

            if (existingGarmentByName != null) {
              // Found garment by name, use it
              finalGarmentId = existingGarmentByName['id'] as String;
              debugPrint('Using existing garment by name: $finalGarmentId');
            } else {
              // No existing garment found, create a new one
              try {
                // Use the existing garment ID or let database generate UUID
                final garmentData = {
                  'name': garmentName,
                  'category': garmentCategory,
                  'description':
                      item.garment.description ?? 'Added automatically',
                  'is_active': true,
                };

                // Only include ID if it's a valid UUID; otherwise let DB generate one
                if (item.garment.id.isNotEmpty &&
                    _isValidUuid(item.garment.id)) {
                  garmentData['id'] = item.garment.id;
                }

                final response = await _supabase
                    .from('garments')
                    .insert(garmentData)
                    .select()
                    .single();

                finalGarmentId = response['id'] as String;
                debugPrint('Created new garment: $finalGarmentId');
              } catch (insertError) {
                // Creation failed, find any garment as final fallback
                debugPrint('Error creating garment: $insertError');

                final fallbackGarment = await _supabase
                    .from('garments')
                    .select()
                    .eq('is_active', true)
                    .limit(1)
                    .maybeSingle();

                if (fallbackGarment != null) {
                  finalGarmentId = fallbackGarment['id'] as String;
                  debugPrint('Using fallback garment: $finalGarmentId');
                } else {
                  // Create a basic fallback garment
                  try {
                    final response = await _supabase
                        .from('garments')
                        .insert({
                          'name': 'Basic Item',
                          'category': 'clothing',
                          'description': 'Default item for orders',
                          'is_active': true,
                        })
                        .select()
                        .single();

                    finalGarmentId = response['id'] as String;
                    debugPrint('Created fallback garment: $finalGarmentId');
                  } catch (fallbackError) {
                    // If we can't create a garment at all, use a placeholder
                    debugPrint('Could not create any garment: $fallbackError');
                    throw Exception(
                      'Unable to create or find any valid garments. Please check your database setup.',
                    );
                  }
                }
              }
            }
          }
        } catch (e) {
          // If all attempts fail, log error and try to proceed
          debugPrint(
            'Warning: Could not process garment ${item.garmentId}: $e',
          );
          throw Exception('Unable to process garment data: $e');
        }

        itemsData.add({
          'order_id': orderId,
          'garment_id': finalGarmentId,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'total_price': item.totalPrice,
          'notes': item.notes,
        });
      }

      await _supabase.from('order_items').insert(itemsData);

      // Log status change - use current authenticated user as changed_by
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': null,
        'new_status': 'pending',
        'changed_by': currentUser.id,
        'notes': 'Order created',
      });

      // Return the created order
      final createdOrder = await getOrderById(orderId);
      if (createdOrder == null) {
        throw Exception('Failed to retrieve created order');
      }
      return createdOrder;
    } catch (e) {
      // Enhanced error handling for RLS issues and FK hints
      final errorMessage = e.toString();
      if (errorMessage.contains('row-level security policy')) {
        if (errorMessage.contains('order_status_log')) {
          throw Exception(
            'Permission denied: Cannot create order status log. Please ensure you have proper permissions.',
          );
        } else if (errorMessage.contains('orders')) {
          throw Exception(
            'Permission denied: Cannot create order. Please ensure you have proper permissions.',
          );
        } else {
          throw Exception('Permission denied: $errorMessage');
        }
      } else if (errorMessage.contains('foreign key')) {
        // Provide more specific hints based on constraint names if present
        if (errorMessage.contains('orders_delivery_address_id_fkey') ||
            errorMessage.contains('orders_delivery_customer_address_id_fkey')) {
          throw Exception(
            'Invalid delivery address: The selected delivery address does not exist. Please reselect or remove it.',
          );
        } else if (errorMessage.contains('orders_pickup_address_id_fkey') ||
            errorMessage.contains('orders_pickup_customer_address_id_fkey')) {
          throw Exception(
            'Invalid pickup address: The selected pickup address does not exist. Please reselect or remove it.',
          );
        } else if (errorMessage.contains('orders_store_id_fkey')) {
          throw Exception(
            'Invalid store: The selected store does not exist. Please reselect or remove it.',
          );
        } else if (errorMessage.contains('orders_service_id_fkey')) {
          throw Exception(
            'Invalid service: The selected service does not exist. Please reselect.',
          );
        } else if (errorMessage.contains('order_items_garment_id_fkey')) {
          throw Exception(
            'Invalid garment: One or more items reference a garment that does not exist.',
          );
        }
        throw Exception(
          'Invalid data: Please check that all selected items are valid.',
        );
      } else {
        throw Exception('Failed to create order: $e');
      }
    }
  }

  // Get all orders for the current user
  Future<List<Order>> getUserOrders([String? userId]) async {
    final currentUser = _supabase.auth.currentUser;
    if (currentUser == null && userId == null) {
      throw Exception('User not authenticated');
    }

    final targetUserId = userId ?? currentUser!.id;

    debugPrint('Fetching orders for user: $targetUserId');

    final response = await _supabase
        .from('orders')
        .select('''
          *,
          service:services(*),
          store:stores(*),
          order_items(
            *,
            garment:garments(*)
          )
        ''')
        .eq('user_id', targetUserId)
        .order('created_at', ascending: false);

    debugPrint('Retrieved ${response.length} orders for user $targetUserId');

    return (response as List).map((json) {
      // Transform order_items to items for the Order model
      final orderData = Map<String, dynamic>.from(json);
      if (orderData['order_items'] != null) {
        orderData['items'] = orderData['order_items'];
        orderData.remove('order_items');
      }
      return Order.fromJson(orderData);
    }).toList();
  }

  // Get all orders (for staff/admin view)
  Future<List<Order>> getAllOrders({String? storeId, String? status}) async {
    debugPrint('Getting all orders - storeId: $storeId, status: $status');

    var query = _supabase.from('orders').select('''
          *,
          service:services(*),
          store:stores(*),
          order_items(
            *,
            garment:garments(*)
          )
        ''');

    if (storeId != null && storeId != 'all') {
      query = query.eq('store_id', storeId);
    }

    if (status != null && status != 'all') {
      query = query.eq('status', status);
    }

    final response = await query.order('created_at', ascending: false);

    debugPrint('Retrieved ${response.length} orders from database');

    return (response as List).map((json) {
      // Transform order_items to items for the Order model
      final orderData = Map<String, dynamic>.from(json);
      if (orderData['order_items'] != null) {
        orderData['items'] = orderData['order_items'];
        orderData.remove('order_items');
      }
      return Order.fromJson(orderData);
    }).toList();
  }

  // Get orders by status
  Future<List<Order>> getOrdersByStatus(String status) async {
    debugPrint('Getting orders by status: $status');

    final response = await _supabase
        .from('orders')
        .select('''
          *,
          service:services(*),
          store:stores(*),
          order_items(
            *,
            garment:garments(*)
          )
        ''')
        .eq('status', status)
        .order('created_at', ascending: false);

    debugPrint('Retrieved ${response.length} orders with status: $status');

    return (response as List).map((json) {
      // Transform order_items to items for the Order model
      final orderData = Map<String, dynamic>.from(json);
      if (orderData['order_items'] != null) {
        orderData['items'] = orderData['order_items'];
        orderData.remove('order_items');
      }
      return Order.fromJson(orderData);
    }).toList();
  }

  // Get single order by ID
  Future<Order?> getOrderById(String orderId) async {
    try {
      debugPrint('Getting order by ID: $orderId');

      final response = await _supabase
          .from('orders')
          .select('''
            *,
            service:services(*),
            store:stores(*),
            order_items(
              *,
              garment:garments(*)
            )
          ''')
          .eq('id', orderId)
          .single();

      // Transform order_items to items for the Order model
      final orderData = Map<String, dynamic>.from(response);
      if (orderData['order_items'] != null) {
        orderData['items'] = orderData['order_items'];
        orderData.remove('order_items');
      }

      debugPrint('Retrieved order: ${orderData['order_number']}');
      return Order.fromJson(orderData);
    } catch (e) {
      debugPrint('Error getting order by ID $orderId: $e');
      return null;
    }
  }

  // Update order status
  Future<Order> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? changedBy,
    String? notes,
  }) async {
    try {
      // Validate orderId is a valid UUID
      if (!_isValidUuid(orderId)) {
        throw Exception('Invalid order ID format');
      }

      // Get current order to log old status
      final currentOrder = await getOrderById(orderId);
      if (currentOrder == null) {
        throw Exception('Order not found');
      }

      final userId = changedBy ?? _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Validate userId is a valid UUID
      if (!_isValidUuid(userId)) {
        throw Exception('Invalid user ID format');
      }

      debugPrint('Updating order status:');
      debugPrint('- Order ID: $orderId');
      debugPrint('- New Status: ${newStatus.value}');
      debugPrint('- Changed By: $userId');
      debugPrint('- Notes: $notes');

      // Update order status
      await _supabase
          .from('orders')
          .update({'status': newStatus.value})
          .eq('id', orderId);

      // Log status change with explicit UUID validation
      final statusLogData = {
        'order_id': orderId,
        'old_status': currentOrder.status.value,
        'new_status': newStatus.value,
        'changed_by': userId,
      };

      // Only add notes if not null or empty
      if (notes != null && notes.isNotEmpty) {
        statusLogData['notes'] = notes;
      }

      debugPrint('Inserting status log with data: $statusLogData');

      await _supabase.from('order_status_log').insert(statusLogData);

      // Return updated order
      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw Exception('Failed to retrieve updated order');
      }
      return updatedOrder;
    } catch (e) {
      debugPrint('Error in updateOrderStatus: $e');

      // Provide more specific error messages
      final errorMessage = e.toString();
      if (errorMessage.contains('invalid input syntax for type uuid')) {
        throw Exception(
          'Database error: Invalid UUID format detected. Please contact support.',
        );
      } else if (errorMessage.contains('violates foreign key constraint')) {
        throw Exception(
          'Database error: Referenced data not found. Please refresh and try again.',
        );
      } else if (errorMessage.contains('row-level security policy')) {
        throw Exception(
          'Permission denied: You do not have access to update this order.',
        );
      } else {
        throw Exception('Failed to update order status: $e');
      }
    }
  }

  // Get order status history
  Future<List<Map<String, dynamic>>> getOrderStatusHistory(
    String orderId,
  ) async {
    try {
      final response = await _supabase
          .from('order_status_log')
          .select('*')
          .eq('order_id', orderId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      // Return mock data if table doesn't exist
      return [
        {
          'id': '1',
          'order_id': orderId,
          'new_status': 'pending',
          'changed_by': 'system',
          'notes': 'Order created',
          'created_at': DateTime.now()
              .subtract(const Duration(hours: 2))
              .toIso8601String(),
        },
        {
          'id': '2',
          'order_id': orderId,
          'new_status': 'accepted',
          'changed_by': 'staff',
          'notes': 'Order accepted by staff',
          'created_at': DateTime.now()
              .subtract(const Duration(hours: 1))
              .toIso8601String(),
        },
      ];
    }
  }

  // Cancel order
  Future<void> cancelOrder({
    required String orderId,
    required String cancelledBy,
    required String cancelReason,
  }) async {
    try {
      await _supabase
          .from('orders')
          .update({
            'status': 'cancelled',
            'cancelled_by': cancelledBy,
            'cancel_reason': cancelReason,
            'cancelled_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId);

      // Log status change
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'new_status': 'cancelled',
        'changed_by': cancelledBy,
        'notes': 'Order cancelled: $cancelReason',
      });
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }

  // Get currency symbol
  Future<String> getCurrencySymbol() async {
    // For now, return a default currency symbol
    // This can be enhanced to fetch from user settings or store settings
    return '\$';
  }

  // Repeat order
  Future<Order> repeatOrder({
    required String originalOrderId,
    required String userId,
  }) async {
    try {
      final originalOrder = await getOrderById(originalOrderId);
      if (originalOrder == null) {
        throw Exception('Original order not found');
      }

      // Create new order with same details but new dates
      final newOrderData = {
        'user_id': userId,
        'store_id': originalOrder.storeId,
        'service_id': originalOrder.serviceId,
        'status': 'pending',
        'pickup_address_id': originalOrder.pickupAddressId,
        'delivery_address_id': originalOrder.deliveryAddressId,
        'subtotal': originalOrder.subtotal,
        'tax_amount': originalOrder.taxAmount,
        'delivery_fee': originalOrder.deliveryFee,
        'discount_amount': originalOrder.discountAmount,
        'total_amount': originalOrder.totalAmount,
        'pay_on_delivery': originalOrder.payOnDelivery,
        'special_instructions': originalOrder.specialInstructions,
      };

      final response = await _supabase
          .from('orders')
          .insert(newOrderData)
          .select()
          .single();

      final newOrderId = response['id'] as String;

      // Copy order items
      for (final item in originalOrder.items) {
        await _supabase.from('order_items').insert({
          'order_id': newOrderId,
          'garment_id': item.garmentId,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'total_price': item.totalPrice,
          'notes': item.notes,
        });
      }

      // Return the new order
      final newOrder = await getOrderById(newOrderId);
      if (newOrder == null) {
        throw Exception('Failed to retrieve new order');
      }
      return newOrder;
    } catch (e) {
      throw Exception('Failed to repeat order: $e');
    }
  }

  // Delete order
  Future<void> deleteOrder({
    required String orderId,
    required String deletedBy,
    required String deleteReason,
  }) async {
    try {
      // First, check if order can be deleted (should be cancelled or completed)
      final order = await getOrderById(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      // Only allow deletion of cancelled or completed orders
      if (order.status != OrderStatus.cancelled &&
          order.status != OrderStatus.completed) {
        throw Exception('Only cancelled or completed orders can be deleted');
      }

      // Log deletion for audit trail
      await _supabase.from('order_deletion_log').insert({
        'order_id': orderId,
        'deleted_by': deletedBy,
        'delete_reason': deleteReason,
        'deleted_at': DateTime.now().toIso8601String(),
        'order_data': order.toJson(), // Store order data before deletion
      });

      // Delete order items first (due to foreign key constraints)
      await _supabase.from('order_items').delete().eq('order_id', orderId);

      // Delete order status log entries
      await _supabase.from('order_status_log').delete().eq('order_id', orderId);

      // Finally, delete the order
      await _supabase.from('orders').delete().eq('id', orderId);
    } catch (e) {
      throw Exception('Failed to delete order: $e');
    }
  }

  // Update order details
  Future<Order> updateOrder(
    String orderId,
    Map<String, dynamic> updateData,
  ) async {
    try {
      // Remove null values from update data
      final cleanedData = <String, dynamic>{};
      updateData.forEach((key, value) {
        if (value != null) {
          cleanedData[key] = value;
        }
      });

      // Add updated timestamp
      cleanedData['updated_at'] = DateTime.now().toIso8601String();

      await _supabase.from('orders').update(cleanedData).eq('id', orderId);

      // Return the updated order
      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw Exception('Failed to retrieve updated order');
      }

      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }

  // Add item to existing order
  Future<void> addItemToOrder(String orderId, OrderItem item) async {
    try {
      // Check if order exists and is modifiable
      final order = await getOrderById(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }
      
      // Only allow adding items to pending or accepted orders
      if (order.status != OrderStatus.pending && order.status != OrderStatus.accepted) {
        throw Exception('Cannot add items to orders in ${order.status.displayName} status');
      }

      // Ensure garment exists or create it for service items
      String finalGarmentId = item.garmentId;
      
      // Check if garment exists, if not create it (for service items)
      final existingGarment = await _supabase
          .from('garments')
          .select()
          .eq('id', item.garmentId)
          .maybeSingle();
      
      if (existingGarment == null && item.garment.category == 'service') {
        // Create the service garment
        await _supabase.from('garments').insert({
          'id': item.garmentId,
          'name': item.garment.name,
          'category': item.garment.category,
          'description': item.garment.description,
          'garment_type': item.garment.garmentType,
          'is_active': item.garment.isActive,
        });
      } else if (existingGarment == null) {
        throw Exception('Garment not found: ${item.garment.name}');
      }

      // Add the item to order_items
      await _supabase.from('order_items').insert({
        'order_id': orderId,
        'garment_id': finalGarmentId,
        'quantity': item.quantity,
        'unit_price': item.unitPrice,
        'total_price': item.totalPrice,
        'notes': item.notes,
      });

      // Recalculate order totals
      await _recalculateOrderTotals(orderId);
      
    } catch (e) {
      throw Exception('Failed to add item to order: $e');
    }
  }

  // Recalculate order totals
  Future<void> _recalculateOrderTotals(String orderId) async {
    try {
      // Get all items for the order
      final itemsResponse = await _supabase
          .from('order_items')
          .select()
          .eq('order_id', orderId);

      double subtotal = 0.0;
      for (final item in itemsResponse) {
        subtotal += (item['total_price'] as num).toDouble();
      }

      // Calculate tax (10%)
      final taxAmount = subtotal * 0.1;
      
      // Get current order to check for delivery fee and discount
      final orderResponse = await _supabase
          .from('orders')
          .select('delivery_fee, discount_amount')
          .eq('id', orderId)
          .single();
      
      final deliveryFee = (orderResponse['delivery_fee'] as num?)?.toDouble() ?? 0.0;
      final discountAmount = (orderResponse['discount_amount'] as num?)?.toDouble() ?? 0.0;
      
      final totalAmount = subtotal + taxAmount + deliveryFee - discountAmount;

      // Update order totals
      await _supabase.from('orders').update({
        'subtotal': subtotal,
        'tax_amount': taxAmount,
        'total_amount': totalAmount,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', orderId);
      
    } catch (e) {
      throw Exception('Failed to recalculate order totals: $e');
    }
  }

  // Mock data for when tables don't exist yet
}
