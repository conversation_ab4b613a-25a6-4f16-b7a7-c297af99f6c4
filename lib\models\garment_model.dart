class Garment {
  final String id;
  final String name;
  final String category; // 'clothing', 'household', 'special'
  final String? description;
  final String? icon;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Enhanced garment properties
  final String
  garmentType; // Specific type like 'shirts', 'pants', 'dresses', etc.
  final String? material; // 'cotton', 'silk', 'leather', 'wool', etc.
  final String? careInstructions;
  final bool requiresSpecialCare;
  final double? estimatedWeight; // in kg
  final String? size; // 'S', 'M', 'L', 'XL', etc.
  final String? color;
  final List<String>
  suitableServices; // List of service types suitable for this garment

  Garment({
    required this.id,
    required this.name,
    required this.category,
    this.description,
    this.icon,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    required this.garmentType,
    this.material,
    this.careInstructions,
    this.requiresSpecialCare = false,
    this.estimatedWeight,
    this.size,
    this.color,
    this.suitableServices = const ['wash_fold'],
  });

  factory Garment.fromJson(Map<String, dynamic> json) {
    return Garment(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      garmentType: json['garment_type'] as String? ?? json['name'] as String,
      material: json['material'] as String?,
      careInstructions: json['care_instructions'] as String?,
      requiresSpecialCare: json['requires_special_care'] as bool? ?? false,
      estimatedWeight: json['estimated_weight'] != null
          ? (json['estimated_weight'] as num).toDouble()
          : null,
      size: json['size'] as String?,
      color: json['color'] as String?,
      suitableServices: json['suitable_services'] != null
          ? List<String>.from(json['suitable_services'])
          : ['wash_fold'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'icon': icon,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'garment_type': garmentType,
      'material': material,
      'care_instructions': careInstructions,
      'requires_special_care': requiresSpecialCare,
      'estimated_weight': estimatedWeight,
      'size': size,
      'color': color,
      'suitable_services': suitableServices,
    };
  }

  Garment copyWith({
    String? id,
    String? name,
    String? category,
    String? description,
    String? icon,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? garmentType,
    String? material,
    String? careInstructions,
    bool? requiresSpecialCare,
    double? estimatedWeight,
    String? size,
    String? color,
    List<String>? suitableServices,
  }) {
    return Garment(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      garmentType: garmentType ?? this.garmentType,
      material: material ?? this.material,
      careInstructions: careInstructions ?? this.careInstructions,
      requiresSpecialCare: requiresSpecialCare ?? this.requiresSpecialCare,
      estimatedWeight: estimatedWeight ?? this.estimatedWeight,
      size: size ?? this.size,
      color: color ?? this.color,
      suitableServices: suitableServices ?? this.suitableServices,
    );
  }

  // Helper methods
  String get materialDisplayName {
    switch (material) {
      case 'cotton':
        return 'Cotton';
      case 'silk':
        return 'Silk';
      case 'leather':
        return 'Leather';
      case 'wool':
        return 'Wool';
      case 'polyester':
        return 'Polyester';
      case 'linen':
        return 'Linen';
      default:
        return material ?? 'Unknown';
    }
  }

  String get sizeDisplayName {
    switch (size) {
      case 'XS':
        return 'Extra Small';
      case 'S':
        return 'Small';
      case 'M':
        return 'Medium';
      case 'L':
        return 'Large';
      case 'XL':
        return 'Extra Large';
      case 'XXL':
        return '2XL';
      default:
        return size ?? 'One Size';
    }
  }

  bool get isSpecialCare =>
      requiresSpecialCare ||
      material == 'silk' ||
      material == 'leather' ||
      garmentType == 'wedding_dress';

  String get careInstructionsDisplay {
    if (careInstructions != null && careInstructions!.isNotEmpty) {
      return careInstructions!;
    }

    if (isSpecialCare) {
      return 'Requires special care - handle with care';
    }

    return 'Standard care instructions apply';
  }
}

class OrderItem {
  final String garmentId;
  final Garment garment;
  final int quantity;
  final double unitPrice;
  final String? notes;

  // Enhanced pricing support based on service types
  final String? pricingType; // 'per_kg', 'per_item', 'fixed'
  final double? weight; // For per_kg pricing
  final double? customAmount; // For custom fixed pricing
  final bool
  isServiceItem; // True if this represents a service rather than garment

  OrderItem({
    required this.garmentId,
    required this.garment,
    required this.quantity,
    required this.unitPrice,
    this.notes,
    this.pricingType,
    this.weight,
    this.customAmount,
    this.isServiceItem = false,
  });

  double get totalPrice {
    if (customAmount != null && customAmount! > 0) {
      // Custom fixed amount pricing
      if (pricingType == 'per_kg' && weight != null) {
        return customAmount! * weight!;
      } else {
        return customAmount!;
      }
    }

    // Standard pricing calculation
    switch (pricingType) {
      case 'per_kg':
        if (weight != null) {
          return unitPrice * weight!;
        }
        return unitPrice * quantity;
      case 'per_item':
        return unitPrice * quantity;
      case 'fixed':
        return unitPrice; // Fixed price, quantity doesn't affect total
      default:
        return unitPrice * quantity; // Default behavior
    }
  }

  String get displayQuantity {
    switch (pricingType) {
      case 'per_kg':
        if (weight != null) {
          return '${weight!.toStringAsFixed(1)}kg';
        }
        return '${quantity}x';
      case 'per_item':
        return '${quantity}x';
      case 'fixed':
        return '1x'; // Fixed services are typically single items
      default:
        return '${quantity}x';
    }
  }

  String get priceDescription {
    if (customAmount != null && customAmount! > 0) {
      if (pricingType == 'per_kg') {
        return '\$${customAmount!.toStringAsFixed(2)}/kg (custom)';
      } else {
        return '\$${customAmount!.toStringAsFixed(2)} (custom fixed)';
      }
    }

    switch (pricingType) {
      case 'per_kg':
        return '\$${unitPrice.toStringAsFixed(2)}/kg';
      case 'per_item':
        return '\$${unitPrice.toStringAsFixed(2)}/item';
      case 'fixed':
        return '\$${unitPrice.toStringAsFixed(2)} (fixed)';
      default:
        return '\$${unitPrice.toStringAsFixed(2)}';
    }
  }

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      garmentId: json['garment_id'] as String,
      garment: Garment.fromJson(json['garment'] as Map<String, dynamic>),
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      notes: json['notes'] as String?,
      pricingType: json['pricing_type'] as String?,
      weight: json['weight'] != null
          ? (json['weight'] as num).toDouble()
          : null,
      customAmount: json['custom_amount'] != null
          ? (json['custom_amount'] as num).toDouble()
          : null,
      isServiceItem: json['is_service_item'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'garment_id': garmentId,
      'garment': garment.toJson(),
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'notes': notes,
      'pricing_type': pricingType,
      'weight': weight,
      'custom_amount': customAmount,
      'is_service_item': isServiceItem,
    };
  }

  OrderItem copyWith({
    String? garmentId,
    Garment? garment,
    int? quantity,
    double? unitPrice,
    String? notes,
    String? pricingType,
    double? weight,
    double? customAmount,
    bool? isServiceItem,
  }) {
    return OrderItem(
      garmentId: garmentId ?? this.garmentId,
      garment: garment ?? this.garment,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      notes: notes ?? this.notes,
      pricingType: pricingType ?? this.pricingType,
      weight: weight ?? this.weight,
      customAmount: customAmount ?? this.customAmount,
      isServiceItem: isServiceItem ?? this.isServiceItem,
    );
  }

  // Create an OrderItem from a service
  factory OrderItem.fromService({
    required String serviceId,
    required String serviceName,
    required String pricingType,
    required double unitPrice,
    double? weight,
    int? quantity,
    double? customAmount,
    String? notes,
  }) {
    // Create a virtual garment for the service
    final serviceGarment = Garment(
      id: serviceId,
      name: serviceName,
      category: 'service',
      description: 'Service item',
      icon: 'room_service',
      garmentType: 'service',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return OrderItem(
      garmentId: serviceId,
      garment: serviceGarment,
      quantity: quantity ?? 1,
      unitPrice: unitPrice,
      notes: notes,
      pricingType: pricingType,
      weight: weight,
      customAmount: customAmount,
      isServiceItem: true,
    );
  }
}

// Predefined garment categories
class GarmentCategory {
  static const String clothing = 'clothing';
  static const String household = 'household';
  static const String special = 'special';

  static const List<String> all = [clothing, household, special];

  static String getDisplayName(String category) {
    switch (category) {
      case clothing:
        return 'Clothing';
      case household:
        return 'Household Items';
      case special:
        return 'Special Care Items';
      default:
        return category;
    }
  }

  static List<Garment> getDefaultGarments() {
    final now = DateTime.now();
    return [
      // Clothing
      Garment(
        id: 'shirts',
        name: 'Shirts',
        category: clothing,
        description:
            'All types of shirts including t-shirts, dress shirts, polo shirts',
        icon: 'checkroom',
        garmentType: 'shirts',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'pants',
        name: 'Pants',
        category: clothing,
        description: 'Trousers, jeans, slacks, and other pants',
        icon: 'checkroom',
        garmentType: 'pants',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'dresses',
        name: 'Dresses',
        category: clothing,
        description:
            'All types of dresses including casual, formal, and evening wear',
        icon: 'checkroom',
        garmentType: 'dresses',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'suits',
        name: 'Suits',
        category: clothing,
        description: 'Business suits, blazers, and formal wear',
        icon: 'business_center',
        garmentType: 'suits',
        material: 'wool',
        requiresSpecialCare: true,
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'jackets',
        name: 'Jackets',
        category: clothing,
        description: 'Coats, jackets, and outerwear',
        icon: 'checkroom',
        garmentType: 'jackets',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'underwear',
        name: 'Underwear',
        category: clothing,
        description: 'Underwear, bras, and intimate wear',
        icon: 'checkroom',
        garmentType: 'underwear',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'socks',
        name: 'Socks',
        category: clothing,
        description: 'Socks, stockings, and hosiery',
        icon: 'checkroom',
        garmentType: 'socks',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),

      // Household
      Garment(
        id: 'bed_sheets',
        name: 'Bed Sheets',
        category: household,
        description: 'Bed linens, sheets, and pillowcases',
        icon: 'bed',
        garmentType: 'bed_sheets',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'pillowcases',
        name: 'Pillowcases',
        category: household,
        description: 'Pillow covers and cases',
        icon: 'bed',
        garmentType: 'pillowcases',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'towels',
        name: 'Towels',
        category: household,
        description: 'Bath towels, hand towels, and washcloths',
        icon: 'shower',
        garmentType: 'towels',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'curtains',
        name: 'Curtains',
        category: household,
        description: 'Window curtains, drapes, and blinds',
        icon: 'window',
        garmentType: 'curtains',
        material: 'polyester',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'blankets',
        name: 'Blankets',
        category: household,
        description: 'Blankets, throws, and comforters',
        icon: 'bed',
        garmentType: 'blankets',
        material: 'cotton',
        createdAt: now,
        updatedAt: now,
      ),

      // Special Care Items
      Garment(
        id: 'wedding_dress',
        name: 'Wedding Dress',
        category: special,
        description: 'Wedding dresses and formal gowns',
        icon: 'favorite',
        garmentType: 'wedding_dress',
        material: 'silk',
        requiresSpecialCare: true,
        careInstructions: 'Requires delicate handling and special cleaning',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'leather_items',
        name: 'Leather Items',
        category: special,
        description: 'Leather jackets, bags, shoes, and accessories',
        icon: 'work',
        garmentType: 'leather_items',
        material: 'leather',
        requiresSpecialCare: true,
        careInstructions: 'Requires leather-specific cleaning and conditioning',
        createdAt: now,
        updatedAt: now,
      ),
      Garment(
        id: 'silk_items',
        name: 'Silk Items',
        category: special,
        description: 'Silk clothing and accessories',
        icon: 'star',
        garmentType: 'silk_items',
        material: 'silk',
        requiresSpecialCare: true,
        careInstructions: 'Requires gentle silk-specific cleaning',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  // Get garments by category
  static List<Garment> getGarmentsByCategory(String category) {
    return getDefaultGarments().where((g) => g.category == category).toList();
  }

  // Get garments suitable for a specific service
  static List<Garment> getGarmentsForService(String serviceType) {
    return getDefaultGarments()
        .where((g) => g.suitableServices.contains(serviceType))
        .toList();
  }
}
